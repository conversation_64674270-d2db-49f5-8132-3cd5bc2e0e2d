{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/src/middleware.js"], "name": "src/middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!_next\\/static|_next\\/image|favicon.ico|public\\/).*))(\\.json)?[\\/#\\?]?$", "originalSource": "/((?!_next/static|_next/image|favicon.ico|public/).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "gZSITgjC5wYJfuPX1XTSP", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "omO0o72qw+F8dSgjkVpCYRnbTlVtuWSSDIfNmcwLrTU=", "__NEXT_PREVIEW_MODE_ID": "8747ac6083c5d854ef5fae29351a3b0d", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "31ec25ac61f16d2440f43075699d48080c701e1b40d39bc3b779845b52941078", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "d87a9fef463c345f706c06fc24d32257c2a732e7d26b4a5352efb8f25bfb5d45"}}}, "functions": {"/api/external/v1/chat/completions/route": {"files": ["server/server-reference-manifest.js", "server/app/api/external/v1/chat/completions/route_client-reference-manifest.js", "server/middleware-build-manifest.js", "server/middleware-react-loadable-manifest.js", "server/next-font-manifest.js", "server/interception-route-rewrite-manifest.js", "server/edge-runtime-webpack.js", "server/app/api/external/v1/chat/completions/route.js"], "name": "app/api/external/v1/chat/completions/route", "page": "/api/external/v1/chat/completions/route", "matchers": [{"regexp": "^/api/external/v1/chat/completions$", "originalSource": "/api/external/v1/chat/completions"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "gZSITgjC5wYJfuPX1XTSP", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "omO0o72qw+F8dSgjkVpCYRnbTlVtuWSSDIfNmcwLrTU=", "__NEXT_PREVIEW_MODE_ID": "8747ac6083c5d854ef5fae29351a3b0d", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "31ec25ac61f16d2440f43075699d48080c701e1b40d39bc3b779845b52941078", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "d87a9fef463c345f706c06fc24d32257c2a732e7d26b4a5352efb8f25bfb5d45"}}}, "sortedMiddleware": ["/"]}