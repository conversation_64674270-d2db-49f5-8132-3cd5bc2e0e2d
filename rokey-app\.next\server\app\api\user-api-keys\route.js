/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/user-api-keys/route";
exports.ids = ["app/api/user-api-keys/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/@supabase/realtime-js/dist/main sync recursive":
/*!************************************************************!*\
  !*** ./node_modules/@supabase/realtime-js/dist/main/ sync ***!
  \************************************************************/
/***/ ((module) => {

function webpackEmptyContext(req) {
	var e = new Error("Cannot find module '" + req + "'");
	e.code = 'MODULE_NOT_FOUND';
	throw e;
}
webpackEmptyContext.keys = () => ([]);
webpackEmptyContext.resolve = webpackEmptyContext;
webpackEmptyContext.id = "(rsc)/./node_modules/@supabase/realtime-js/dist/main sync recursive";
module.exports = webpackEmptyContext;

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fuser-api-keys%2Froute&page=%2Fapi%2Fuser-api-keys%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fuser-api-keys%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fuser-api-keys%2Froute&page=%2Fapi%2Fuser-api-keys%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fuser-api-keys%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_RoKey_App_rokey_app_src_app_api_user_api_keys_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/user-api-keys/route.ts */ \"(rsc)/./src/app/api/user-api-keys/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/user-api-keys/route\",\n        pathname: \"/api/user-api-keys\",\n        filename: \"route\",\n        bundlePath: \"app/api/user-api-keys/route\"\n    },\n    resolvedPagePath: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\api\\\\user-api-keys\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_RoKey_App_rokey_app_src_app_api_user_api_keys_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fuser-api-keys%2Froute&page=%2Fapi%2Fuser-api-keys%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fuser-api-keys%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/user-api-keys/route.ts":
/*!********************************************!*\
  !*** ./src/app/api/user-api-keys/route.ts ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/supabase/server */ \"(rsc)/./src/lib/supabase/server.ts\");\n/* harmony import */ var _lib_userApiKeys_apiKeyGenerator__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/userApiKeys/apiKeyGenerator */ \"(rsc)/./src/lib/userApiKeys/apiKeyGenerator.ts\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/dist/esm/index.js\");\n\n\n\n\n// Validation schema for creating API keys\nconst CreateApiKeySchema = zod__WEBPACK_IMPORTED_MODULE_3__.z.object({\n    custom_api_config_id: zod__WEBPACK_IMPORTED_MODULE_3__.z.string().uuid(),\n    key_name: zod__WEBPACK_IMPORTED_MODULE_3__.z.string().min(1).max(100),\n    expires_at: zod__WEBPACK_IMPORTED_MODULE_3__.z.string().datetime().optional()\n});\n// POST /api/user-api-keys - Generate a new API key\nasync function POST(request) {\n    const supabase = (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__.createSupabaseServerClientFromRequest)(request);\n    try {\n        // Authenticate user\n        const { data: { user }, error: authError } = await supabase.auth.getUser();\n        if (authError || !user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Unauthorized'\n            }, {\n                status: 401\n            });\n        }\n        // Parse and validate request body\n        const body = await request.json();\n        const validatedData = CreateApiKeySchema.parse(body);\n        // Check if user owns the custom API config\n        const { data: config, error: configError } = await supabase.from('custom_api_configs').select('id, name, user_id').eq('id', validatedData.custom_api_config_id).eq('user_id', user.id).single();\n        if (configError || !config) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Custom API configuration not found or access denied'\n            }, {\n                status: 404\n            });\n        }\n        // Get user's subscription tier from subscriptions table\n        const { data: subscription } = await supabase.from('subscriptions').select('tier').eq('user_id', user.id).eq('status', 'active').single();\n        const subscriptionTier = subscription?.tier || 'starter';\n        // Check current API key count for this user\n        const { count: currentKeyCount } = await supabase.from('user_generated_api_keys').select('*', {\n            count: 'exact',\n            head: true\n        }).eq('user_id', user.id).eq('status', 'active');\n        // Validate subscription limits\n        const limitCheck = _lib_userApiKeys_apiKeyGenerator__WEBPACK_IMPORTED_MODULE_2__.ApiKeyGenerator.validateSubscriptionLimits(subscriptionTier, currentKeyCount || 0);\n        if (!limitCheck.allowed) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: limitCheck.message\n            }, {\n                status: 403\n            });\n        }\n        // Check for duplicate key name within the config\n        const { data: existingKey } = await supabase.from('user_generated_api_keys').select('id').eq('custom_api_config_id', validatedData.custom_api_config_id).eq('key_name', validatedData.key_name).eq('status', 'active').single();\n        if (existingKey) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'An API key with this name already exists for this configuration'\n            }, {\n                status: 409\n            });\n        }\n        // Generate the API key\n        const { fullKey, prefix, secretPart, hash } = await _lib_userApiKeys_apiKeyGenerator__WEBPACK_IMPORTED_MODULE_2__.ApiKeyGenerator.generateApiKey();\n        const encryptedSuffix = await _lib_userApiKeys_apiKeyGenerator__WEBPACK_IMPORTED_MODULE_2__.ApiKeyGenerator.encryptSuffix(secretPart);\n        // Prepare the API key data with default settings\n        const apiKeyData = {\n            user_id: user.id,\n            custom_api_config_id: validatedData.custom_api_config_id,\n            key_name: validatedData.key_name,\n            key_prefix: prefix,\n            key_hash: hash,\n            encrypted_key_suffix: encryptedSuffix,\n            permissions: {\n                chat: true,\n                streaming: true,\n                all_models: true\n            },\n            allowed_ips: [],\n            allowed_domains: [],\n            expires_at: validatedData.expires_at || null\n        };\n        // Insert the API key into the database\n        const { data: createdKey, error: insertError } = await supabase.from('user_generated_api_keys').insert(apiKeyData).select().single();\n        if (insertError) {\n            console.error('Error creating API key:', insertError);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Failed to create API key'\n            }, {\n                status: 500\n            });\n        }\n        // Return the response with the full API key (only shown once)\n        const response = {\n            id: createdKey.id,\n            key_name: createdKey.key_name,\n            api_key: fullKey,\n            key_prefix: createdKey.key_prefix,\n            permissions: createdKey.permissions,\n            created_at: createdKey.created_at,\n            expires_at: createdKey.expires_at\n        };\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(response, {\n            status: 201\n        });\n    } catch (error) {\n        if (error instanceof zod__WEBPACK_IMPORTED_MODULE_3__.z.ZodError) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Invalid request data',\n                details: error.errors\n            }, {\n                status: 400\n            });\n        }\n        console.error('Error in POST /api/user-api-keys:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Internal server error'\n        }, {\n            status: 500\n        });\n    }\n}\n// GET /api/user-api-keys - List user's API keys\nasync function GET(request) {\n    const supabase = (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__.createSupabaseServerClientFromRequest)(request);\n    try {\n        // Authenticate user\n        const { data: { user }, error: authError } = await supabase.auth.getUser();\n        if (authError || !user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Unauthorized'\n            }, {\n                status: 401\n            });\n        }\n        // Get query parameters\n        const { searchParams } = new URL(request.url);\n        const configId = searchParams.get('config_id');\n        // Build query\n        let query = supabase.from('user_generated_api_keys').select(`\n        id,\n        key_name,\n        key_prefix,\n        encrypted_key_suffix,\n        permissions,\n        allowed_ips,\n        allowed_domains,\n        total_requests,\n        last_used_at,\n        status,\n        expires_at,\n        created_at,\n        custom_api_configs!inner(\n          id,\n          name\n        )\n      `).eq('user_id', user.id).order('created_at', {\n            ascending: false\n        });\n        // Filter by config if specified\n        if (configId) {\n            query = query.eq('custom_api_config_id', configId);\n        }\n        const { data: apiKeys, error } = await query;\n        if (error) {\n            console.error('Error fetching API keys:', error);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Failed to fetch API keys'\n            }, {\n                status: 500\n            });\n        }\n        // Transform the data to include masked keys\n        const transformedKeys = apiKeys.map((key)=>({\n                ...key,\n                masked_key: _lib_userApiKeys_apiKeyGenerator__WEBPACK_IMPORTED_MODULE_2__.ApiKeyGenerator.createMaskedKey(key.key_prefix, key.encrypted_key_suffix),\n                // Remove sensitive data\n                encrypted_key_suffix: undefined\n            }));\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            api_keys: transformedKeys\n        });\n    } catch (error) {\n        console.error('Error in GET /api/user-api-keys:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Internal server error'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/user-api-keys/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/encryption.ts":
/*!*******************************!*\
  !*** ./src/lib/encryption.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   decrypt: () => (/* binding */ decrypt),\n/* harmony export */   encrypt: () => (/* binding */ encrypt)\n/* harmony export */ });\n// Web Crypto API compatible encryption for Edge Runtime\nconst ALGORITHM = 'AES-GCM';\nconst IV_LENGTH = 12; // Recommended for GCM\n// Ensure your ROKEY_ENCRYPTION_KEY is a 64-character hex string (32 bytes)\nconst ROKEY_ENCRYPTION_KEY_FROM_ENV = process.env.ROKEY_ENCRYPTION_KEY;\nconsole.log('[DEBUG] ROKEY_ENCRYPTION_KEY from process.env:', ROKEY_ENCRYPTION_KEY_FROM_ENV);\nconsole.log('[DEBUG] Length:', ROKEY_ENCRYPTION_KEY_FROM_ENV?.length);\nif (!ROKEY_ENCRYPTION_KEY_FROM_ENV || ROKEY_ENCRYPTION_KEY_FROM_ENV.length !== 64) {\n    throw new Error('Invalid ROKEY_ENCRYPTION_KEY. Please set a 64-character hex string (32 bytes) for ROKEY_ENCRYPTION_KEY in your .env.local file.');\n}\n// Convert hex string to Uint8Array for Web Crypto API\nfunction hexToUint8Array(hex) {\n    const bytes = new Uint8Array(hex.length / 2);\n    for(let i = 0; i < hex.length; i += 2){\n        bytes[i / 2] = parseInt(hex.substr(i, 2), 16);\n    }\n    return bytes;\n}\n// Convert Uint8Array to hex string\nfunction uint8ArrayToHex(bytes) {\n    return Array.from(bytes, (byte)=>byte.toString(16).padStart(2, '0')).join('');\n}\nconst keyBytes = hexToUint8Array(ROKEY_ENCRYPTION_KEY_FROM_ENV);\nasync function encrypt(text) {\n    if (typeof text !== 'string' || text.length === 0) {\n        throw new Error('Encryption input must be a non-empty string.');\n    }\n    // Generate random IV\n    const iv = crypto.getRandomValues(new Uint8Array(IV_LENGTH));\n    // Import the key for Web Crypto API\n    const cryptoKey = await crypto.subtle.importKey('raw', keyBytes, {\n        name: ALGORITHM\n    }, false, [\n        'encrypt'\n    ]);\n    // Encrypt the text\n    const encodedText = new TextEncoder().encode(text);\n    const encryptedBuffer = await crypto.subtle.encrypt({\n        name: ALGORITHM,\n        iv: iv\n    }, cryptoKey, encodedText);\n    const encryptedArray = new Uint8Array(encryptedBuffer);\n    // For AES-GCM, the auth tag is included in the encrypted data (last 16 bytes)\n    const encryptedData = encryptedArray.slice(0, -16);\n    const authTag = encryptedArray.slice(-16);\n    // Return IV:authTag:encryptedData format\n    return `${uint8ArrayToHex(iv)}:${uint8ArrayToHex(authTag)}:${uint8ArrayToHex(encryptedData)}`;\n}\nasync function decrypt(encryptedText) {\n    if (typeof encryptedText !== 'string' || encryptedText.length === 0) {\n        throw new Error('Decryption input must be a non-empty string.');\n    }\n    const parts = encryptedText.split(':');\n    if (parts.length !== 3) {\n        throw new Error('Invalid encrypted text format. Expected iv:authTag:encryptedData');\n    }\n    const iv = hexToUint8Array(parts[0]);\n    const authTag = hexToUint8Array(parts[1]);\n    const encryptedData = hexToUint8Array(parts[2]);\n    if (iv.length !== IV_LENGTH) {\n        throw new Error(`Invalid IV length. Expected ${IV_LENGTH} bytes.`);\n    }\n    if (authTag.length !== 16) {\n        throw new Error(`Invalid authTag length. Expected 16 bytes.`);\n    }\n    // Import the key for Web Crypto API\n    const cryptoKey = await crypto.subtle.importKey('raw', keyBytes, {\n        name: ALGORITHM\n    }, false, [\n        'decrypt'\n    ]);\n    // Combine encrypted data and auth tag for Web Crypto API\n    const combinedData = new Uint8Array(encryptedData.length + authTag.length);\n    combinedData.set(encryptedData);\n    combinedData.set(authTag, encryptedData.length);\n    // Decrypt the data\n    const decryptedBuffer = await crypto.subtle.decrypt({\n        name: ALGORITHM,\n        iv: iv\n    }, cryptoKey, combinedData);\n    return new TextDecoder().decode(decryptedBuffer);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/encryption.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/supabase/server.ts":
/*!************************************!*\
  !*** ./src/lib/supabase/server.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createSupabaseServerClientFromRequest: () => (/* binding */ createSupabaseServerClientFromRequest),\n/* harmony export */   createSupabaseServerClientOnRequest: () => (/* binding */ createSupabaseServerClientOnRequest)\n/* harmony export */ });\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/ssr */ \"(rsc)/./node_modules/@supabase/ssr/dist/module/index.js\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n\n\n// This is the standard setup for creating a Supabase server client\n// in Next.js App Router (Server Components, Route Handlers, Server Actions).\n// Updated for Next.js 15 async cookies requirement\nasync function createSupabaseServerClientOnRequest() {\n    const cookieStore = await (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.cookies)();\n    return (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createServerClient)(\"https://hpkzzhpufhbxtxqaugjh.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8\", {\n        cookies: {\n            get (name) {\n                return cookieStore.get(name)?.value;\n            },\n            set (name, value, options) {\n                try {\n                    cookieStore.set({\n                        name,\n                        value,\n                        ...options\n                    });\n                } catch (error) {\n                    // This error can be ignored if running in a Server Component\n                    // where cookies can't be set directly. Cookie setting should be\n                    // handled in Server Actions or Route Handlers.\n                    console.warn(`Failed to set cookie '${name}' (might be in a Server Component):`, error);\n                }\n            },\n            remove (name, options) {\n                try {\n                    // To remove a cookie using the `set` method from `next/headers`,\n                    // you typically set it with an empty value and Max-Age=0 or an expiry date in the past.\n                    cookieStore.set({\n                        name,\n                        value: '',\n                        ...options\n                    });\n                } catch (error) {\n                    // Similar to set, this might fail in a Server Component.\n                    console.warn(`Failed to remove cookie '${name}' (might be in a Server Component):`, error);\n                }\n            }\n        }\n    });\n}\n// Alternative method for API routes that need to handle cookies from request\nfunction createSupabaseServerClientFromRequest(request) {\n    return (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createServerClient)(\"https://hpkzzhpufhbxtxqaugjh.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8\", {\n        cookies: {\n            get (name) {\n                return request.cookies.get(name)?.value;\n            },\n            set (name, value, options) {\n            // In API routes, we can't set cookies directly on the request\n            // This will be handled by the response\n            },\n            remove (name, options) {\n            // In API routes, we can't remove cookies directly on the request\n            // This will be handled by the response\n            }\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabase/server.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/userApiKeys/apiKeyGenerator.ts":
/*!************************************************!*\
  !*** ./src/lib/userApiKeys/apiKeyGenerator.ts ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ApiKeyGenerator: () => (/* binding */ ApiKeyGenerator)\n/* harmony export */ });\n/* harmony import */ var _lib_encryption__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/encryption */ \"(rsc)/./src/lib/encryption.ts\");\n\nclass ApiKeyGenerator {\n    static{\n        this.KEY_PREFIX = 'rk_live_';\n    }\n    static{\n        this.RANDOM_PART_LENGTH = 8 // hex chars for middle part\n        ;\n    }\n    static{\n        this.SECRET_PART_LENGTH = 32 // chars for secret part\n        ;\n    }\n    /**\n   * Generates a new API key with the format: rk_live_{8_hex_chars}_{32_random_chars}\n   * @returns Object containing the full key, prefix, and secret parts\n   */ static async generateApiKey() {\n        // Generate random hex for the middle part (visible in prefix)\n        const randomBytes = crypto.getRandomValues(new Uint8Array(this.RANDOM_PART_LENGTH / 2));\n        const randomHex = Array.from(randomBytes, (byte)=>byte.toString(16).padStart(2, '0')).join('');\n        // Generate random alphanumeric for the secret part\n        const secretPart = this.generateRandomString(this.SECRET_PART_LENGTH);\n        // Construct the full key\n        const prefix = `${this.KEY_PREFIX}${randomHex}`;\n        const fullKey = `${prefix}_${secretPart}`;\n        // Generate hash for storage\n        const hash = await this.hashApiKey(fullKey);\n        return {\n            fullKey,\n            prefix,\n            secretPart,\n            hash\n        };\n    }\n    /**\n   * Generates a cryptographically secure random string\n   * @param length Length of the string to generate\n   * @returns Random alphanumeric string\n   */ static generateRandomString(length) {\n        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';\n        let result = '';\n        for(let i = 0; i < length; i++){\n            const randomBytes = crypto.getRandomValues(new Uint8Array(1));\n            const randomIndex = randomBytes[0] % chars.length;\n            result += chars[randomIndex];\n        }\n        return result;\n    }\n    /**\n   * Creates a SHA-256 hash of the API key for secure storage\n   * @param apiKey The full API key to hash\n   * @returns SHA-256 hash as hex string\n   */ static async hashApiKey(apiKey) {\n        const encoder = new TextEncoder();\n        const data = encoder.encode(apiKey);\n        const hashBuffer = await crypto.subtle.digest('SHA-256', data);\n        const hashArray = new Uint8Array(hashBuffer);\n        return Array.from(hashArray, (byte)=>byte.toString(16).padStart(2, '0')).join('');\n    }\n    /**\n   * Validates the format of an API key\n   * @param apiKey The API key to validate\n   * @returns True if the format is valid\n   */ static isValidFormat(apiKey) {\n        const pattern = new RegExp(`^${this.KEY_PREFIX}[a-f0-9]{${this.RANDOM_PART_LENGTH}}_[a-zA-Z0-9]{${this.SECRET_PART_LENGTH}}$`);\n        return pattern.test(apiKey);\n    }\n    /**\n   * Extracts the prefix from a full API key\n   * @param apiKey The full API key\n   * @returns The prefix part (e.g., \"rk_live_abc12345\")\n   */ static extractPrefix(apiKey) {\n        const parts = apiKey.split('_');\n        if (parts.length >= 3) {\n            return `${parts[0]}_${parts[1]}_${parts[2]}`;\n        }\n        return '';\n    }\n    /**\n   * Encrypts the suffix part of an API key for partial display\n   * @param secretPart The secret part of the API key\n   * @returns Encrypted suffix for storage\n   */ static async encryptSuffix(secretPart) {\n        // Take last 4 characters for display purposes\n        const suffix = secretPart.slice(-4);\n        return await (0,_lib_encryption__WEBPACK_IMPORTED_MODULE_0__.encrypt)(suffix);\n    }\n    /**\n   * Decrypts the suffix for display\n   * @param encryptedSuffix The encrypted suffix from database\n   * @returns Decrypted suffix for display\n   */ static async decryptSuffix(encryptedSuffix) {\n        try {\n            return await (0,_lib_encryption__WEBPACK_IMPORTED_MODULE_0__.decrypt)(encryptedSuffix);\n        } catch (error) {\n            console.error('Failed to decrypt API key suffix:', error);\n            // Return a placeholder that looks like the last 4 chars\n            return 'xxxx';\n        }\n    }\n    /**\n   * Creates a masked version of the API key for display\n   * @param prefix The key prefix\n   * @param encryptedSuffix The encrypted suffix\n   * @returns Masked key for display (e.g., \"rk_live_abc12345_****xyz\")\n   */ static async createMaskedKey(prefix, encryptedSuffix) {\n        const suffix = await this.decryptSuffix(encryptedSuffix);\n        // Show 4 chars at the end, mask the rest (32 - 4 = 28 chars to mask)\n        const maskedLength = this.SECRET_PART_LENGTH - 4;\n        return `${prefix}_${'*'.repeat(maskedLength)}${suffix}`;\n    }\n    /**\n   * Validates subscription tier limits for API key generation\n   * @param subscriptionTier User's subscription tier\n   * @param currentKeyCount Current number of API keys for the user\n   * @returns Object indicating if generation is allowed and any limits\n   */ static validateSubscriptionLimits(subscriptionTier, currentKeyCount) {\n        const limits = {\n            starter: 5,\n            professional: 25,\n            enterprise: 100\n        };\n        const limit = limits[subscriptionTier] || limits.starter;\n        if (currentKeyCount >= limit) {\n            return {\n                allowed: false,\n                limit,\n                message: `You have reached the maximum number of API keys (${limit}) for your ${subscriptionTier} plan.`\n            };\n        }\n        return {\n            allowed: true,\n            limit\n        };\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/userApiKeys/apiKeyGenerator.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/webidl-conversions","vendor-chunks/cookie","vendor-chunks/zod"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fuser-api-keys%2Froute&page=%2Fapi%2Fuser-api-keys%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fuser-api-keys%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();