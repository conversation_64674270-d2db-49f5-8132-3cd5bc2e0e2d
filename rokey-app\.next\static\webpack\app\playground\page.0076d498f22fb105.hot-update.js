"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/playground/page",{

/***/ "(app-pages-browser)/./src/utils/performanceLogs.ts":
/*!**************************************!*\
  !*** ./src/utils/performanceLogs.ts ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   logComprehensivePerformanceReport: () => (/* binding */ logComprehensivePerformanceReport),\n/* harmony export */   logFirstTokenReport: () => (/* binding */ logFirstTokenReport),\n/* harmony export */   logGoogleStreamingDebug: () => (/* binding */ logGoogleStreamingDebug),\n/* harmony export */   performanceLogs: () => (/* binding */ performanceLogs),\n/* harmony export */   quickPerformanceCheck: () => (/* binding */ quickPerformanceCheck),\n/* harmony export */   startPerformanceMonitoring: () => (/* binding */ startPerformanceMonitoring),\n/* harmony export */   stopPerformanceMonitoring: () => (/* binding */ stopPerformanceMonitoring)\n/* harmony export */ });\n/* harmony import */ var _messagingPerformance__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./messagingPerformance */ \"(app-pages-browser)/./src/utils/messagingPerformance.ts\");\n/* harmony import */ var _streamingUtils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./streamingUtils */ \"(app-pages-browser)/./src/utils/streamingUtils.ts\");\n// Comprehensive performance logging utilities for terminal monitoring\n\n\n// Enhanced performance report with first token insights\nfunction logComprehensivePerformanceReport() {\n    console.log('\\n🚀 ===== COMPREHENSIVE PERFORMANCE REPORT =====');\n    console.log('📊 Real-time messaging and streaming performance analysis\\n');\n    // Get messaging performance summary\n    const messagingSummary = _messagingPerformance__WEBPACK_IMPORTED_MODULE_0__.messagingPerformanceTracker.getSummary();\n    console.log('📈 MESSAGING PERFORMANCE OVERVIEW:');\n    console.log(\"   Total Providers: \".concat(messagingSummary.totalProviders));\n    console.log(\"   Total Models: \".concat(messagingSummary.totalModels));\n    console.log(\"   Overall Average Time: \".concat(messagingSummary.overallAverageTime, \"ms\"));\n    if (messagingSummary.fastestProvider) {\n        console.log(\"   \\uD83C\\uDFC6 Fastest Provider: \".concat(messagingSummary.fastestProvider.provider, \" (\").concat(messagingSummary.fastestProvider.averageTotal, \"ms avg)\"));\n    }\n    if (messagingSummary.slowestProvider) {\n        console.log(\"   \\uD83D\\uDC0C Slowest Provider: \".concat(messagingSummary.slowestProvider.provider, \" (\").concat(messagingSummary.slowestProvider.averageTotal, \"ms avg)\"));\n    }\n    console.log('\\n⚡ FIRST TOKEN PERFORMANCE TARGETS:');\n    console.log(\"   \\uD83C\\uDFAF Excellent: < \".concat(_streamingUtils__WEBPACK_IMPORTED_MODULE_1__.PERFORMANCE_THRESHOLDS.EXCELLENT_FIRST_TOKEN, \"ms\"));\n    console.log(\"   ✅ Good: < \".concat(_streamingUtils__WEBPACK_IMPORTED_MODULE_1__.PERFORMANCE_THRESHOLDS.GOOD_FIRST_TOKEN, \"ms\"));\n    console.log(\"   ⚠️ Slow: < \".concat(_streamingUtils__WEBPACK_IMPORTED_MODULE_1__.PERFORMANCE_THRESHOLDS.SLOW_FIRST_TOKEN, \"ms\"));\n    console.log(\"   \\uD83D\\uDEA8 Very Slow: > \".concat(_streamingUtils__WEBPACK_IMPORTED_MODULE_1__.PERFORMANCE_THRESHOLDS.SLOW_FIRST_TOKEN, \"ms\"));\n    console.log('\\n💡 RECOMMENDATIONS:');\n    messagingSummary.recommendations.forEach((rec)=>console.log(\"   • \".concat(rec)));\n    console.log('\\n📋 HOW TO MONITOR FIRST TOKEN PERFORMANCE:');\n    console.log('   1. Send a message in the playground with streaming enabled');\n    console.log('   2. Watch the terminal for \"🚀 FIRST TOKEN\" logs');\n    console.log('   3. Look for \"📊 STREAMING COMPLETE\" summaries');\n    console.log('   4. Use logFirstTokenReport() for detailed analysis');\n    console.log('\\n🔧 PERFORMANCE MONITORING COMMANDS:');\n    console.log('   • logComprehensivePerformanceReport() - This report');\n    console.log('   • logFirstTokenReport() - First token specific analysis');\n    console.log('   • logMessagingReport() - General messaging performance');\n    console.log('   • firstTokenTracker.clear() - Clear tracking data');\n    console.log('\\n===============================================\\n');\n}\n// First token specific performance analysis\nfunction logFirstTokenReport() {\n    var _tracker_timingData;\n    console.log('\\n⚡ ===== FIRST TOKEN PERFORMANCE ANALYSIS =====');\n    // Check if we have any active tracking\n    const tracker = _messagingPerformance__WEBPACK_IMPORTED_MODULE_0__.firstTokenTracker;\n    const activeTrackings = ((_tracker_timingData = tracker.timingData) === null || _tracker_timingData === void 0 ? void 0 : _tracker_timingData.size) || 0;\n    console.log(\"\\uD83D\\uDCCA Active Trackings: \".concat(activeTrackings));\n    if (activeTrackings === 0) {\n        console.log('\\n💡 No active first token tracking sessions.');\n        console.log('   Start a streaming conversation to see real-time metrics!');\n    } else {\n        console.log('\\n🔄 Currently tracking streaming sessions...');\n        console.log('   Watch for \"🚀 FIRST TOKEN RECEIVED\" logs in real-time');\n    }\n    console.log('\\n📈 PERFORMANCE CATEGORIES:');\n    console.log(\"   ⚡ EXCELLENT: < \".concat(_streamingUtils__WEBPACK_IMPORTED_MODULE_1__.PERFORMANCE_THRESHOLDS.EXCELLENT_FIRST_TOKEN, \"ms\"));\n    console.log(\"   ✅ GOOD: \".concat(_streamingUtils__WEBPACK_IMPORTED_MODULE_1__.PERFORMANCE_THRESHOLDS.EXCELLENT_FIRST_TOKEN, \"-\").concat(_streamingUtils__WEBPACK_IMPORTED_MODULE_1__.PERFORMANCE_THRESHOLDS.GOOD_FIRST_TOKEN, \"ms\"));\n    console.log(\"   ⚠️ SLOW: \".concat(_streamingUtils__WEBPACK_IMPORTED_MODULE_1__.PERFORMANCE_THRESHOLDS.GOOD_FIRST_TOKEN, \"-\").concat(_streamingUtils__WEBPACK_IMPORTED_MODULE_1__.PERFORMANCE_THRESHOLDS.SLOW_FIRST_TOKEN, \"ms\"));\n    console.log(\"   \\uD83D\\uDC0C VERY SLOW: > \".concat(_streamingUtils__WEBPACK_IMPORTED_MODULE_1__.PERFORMANCE_THRESHOLDS.SLOW_FIRST_TOKEN, \"ms\"));\n    console.log('\\n🎯 WHAT TO LOOK FOR:');\n    console.log('   • Backend logs: \"🚀 [PROVIDER] FIRST TOKEN: XXXms\"');\n    console.log('   • Frontend logs: \"🚀 FRONTEND FIRST TOKEN: XXXms\"');\n    console.log('   • Completion logs: \"📊 STREAMING COMPLETE\"');\n    console.log('   • Stream quality: \"✅ STREAMING QUALITY\" vs \"⚠️ STREAMING ISSUE\"');\n    console.log('\\n🔍 DEBUGGING TIPS:');\n    console.log('   • Compare backend vs frontend first token times');\n    console.log('   • Look for provider-specific performance patterns');\n    console.log('   • Check chunk count: 1 chunk = buffering issue, many chunks = good streaming');\n    console.log('   • Monitor \"Raw chunk\" logs to see if data arrives gradually');\n    console.log('   • Watch for \"STREAMING ISSUE\" warnings indicating buffering problems');\n    console.log('\\n🚨 GOOGLE STREAMING TROUBLESHOOTING:');\n    console.log('   • Google API does NOT truly stream - we implement artificial streaming');\n    console.log('   • Look for \"[Google Stream] Artificial chunk\" logs in terminal');\n    console.log('   • First token should show \"(Artificial)\" in the log');\n    console.log('   • Compare first token timing: backend vs frontend');\n    console.log('   • Expected behavior: Multiple artificial chunks with delays');\n    console.log('   • If still seeing issues: Check chunk size and delay settings');\n    console.log('\\n===============================================\\n');\n}\n// New function specifically for debugging Google streaming issues\nfunction logGoogleStreamingDebug() {\n    console.log('\\n🔍 ===== GOOGLE STREAMING DEBUG GUIDE =====');\n    console.log('Use this guide to diagnose Google streaming issues\\n');\n    console.log('🎯 EXPECTED BEHAVIOR (ARTIFICIAL STREAMING):');\n    console.log('   • Backend: \"Starting artificial streaming transformation\"');\n    console.log('   • Backend: \"🚀 GOOGLE FIRST TOKEN (Artificial): XXXms\" with content preview');\n    console.log('   • Backend: Multiple \"[Google Stream] Artificial chunk X\" logs with delays');\n    console.log('   • Frontend: \"🚀 FRONTEND FIRST TOKEN: XXXms\" shortly after backend');\n    console.log('   • Frontend: Multiple \"[Frontend Stream] Chunk X\" logs');\n    console.log('   • Frontend: \"✅ STREAMING QUALITY: Good chunk distribution\"');\n    console.log('\\n🚨 PROBLEM INDICATORS:');\n    console.log('   • Missing \"artificial streaming transformation\" log');\n    console.log('   • No \"Artificial chunk\" logs in backend');\n    console.log('   • Frontend: \"⚠️ STREAMING ISSUE: Only 1 chunk received\"');\n    console.log('   • Frontend: \"⚠️ STREAMING ISSUE: Very few chunks for long content\"');\n    console.log('   • First token NOT marked as \"(Artificial)\"');\n    console.log('\\n🔧 DEBUGGING STEPS:');\n    console.log('   1. Send a message and watch terminal logs');\n    console.log('   2. Look for \"artificial streaming transformation\" message');\n    console.log('   3. Check for multiple \"Artificial chunk\" logs with delays');\n    console.log('   4. Verify first token shows \"(Artificial)\" marker');\n    console.log('   5. Compare chunk counts between backend and frontend');\n    console.log('   6. Check for streaming quality assessment in frontend');\n    console.log('\\n💡 SOLUTIONS TO TRY:');\n    console.log('   • Adjust CHUNK_SIZE (currently 15 chars) for different streaming speed');\n    console.log('   • Modify CHUNK_DELAY (currently 50ms) for faster/slower streaming');\n    console.log('   • Check if artificial streaming transformation is being called');\n    console.log('   • Verify frontend is receiving multiple chunks progressively');\n    console.log('\\n===============================================\\n');\n}\n// Real-time performance monitoring for development\nfunction startPerformanceMonitoring() {\n    console.log('🔄 Starting real-time performance monitoring...');\n    console.log('📊 This will log performance insights as they happen.\\n');\n    // Set up interval to show periodic updates\n    const monitoringInterval = setInterval(()=>{\n        const summary = _messagingPerformance__WEBPACK_IMPORTED_MODULE_0__.messagingPerformanceTracker.getSummary();\n        if (summary.totalProviders > 0) {\n            console.log(\"\\uD83D\\uDCC8 Performance Update: \".concat(summary.totalProviders, \" providers, avg \").concat(summary.overallAverageTime, \"ms\"));\n        }\n    }, 30000); // Every 30 seconds\n    console.log('✅ Performance monitoring active. Use stopPerformanceMonitoring() to stop.');\n    // Store interval ID for cleanup\n    globalThis.__performanceMonitoringInterval = monitoringInterval;\n}\nfunction stopPerformanceMonitoring() {\n    const interval = globalThis.__performanceMonitoringInterval;\n    if (interval) {\n        clearInterval(interval);\n        delete globalThis.__performanceMonitoringInterval;\n        console.log('⏹️ Performance monitoring stopped.');\n    } else {\n        console.log('ℹ️ No active performance monitoring to stop.');\n    }\n}\n// Quick performance check\nfunction quickPerformanceCheck() {\n    console.log('\\n⚡ QUICK PERFORMANCE CHECK');\n    console.log('==========================');\n    const summary = _messagingPerformance__WEBPACK_IMPORTED_MODULE_0__.messagingPerformanceTracker.getSummary();\n    if (summary.totalProviders === 0) {\n        console.log('📊 No performance data available yet.');\n        console.log('💡 Send some messages to generate performance metrics!');\n        return;\n    }\n    console.log(\"\\uD83D\\uDCC8 Overall Average: \".concat(summary.overallAverageTime, \"ms\"));\n    // Performance assessment\n    if (summary.overallAverageTime < 2000) {\n        console.log('✅ EXCELLENT overall performance!');\n    } else if (summary.overallAverageTime < 5000) {\n        console.log('👍 GOOD overall performance');\n    } else if (summary.overallAverageTime < 10000) {\n        console.log('⚠️ SLOW performance - needs optimization');\n    } else {\n        console.log('🚨 VERY SLOW performance - urgent optimization needed');\n    }\n    if (summary.fastestProvider) {\n        console.log(\"\\uD83C\\uDFC6 Best: \".concat(summary.fastestProvider.provider, \" (\").concat(summary.fastestProvider.averageTotal, \"ms)\"));\n    }\n    if (summary.slowestProvider) {\n        console.log(\"\\uD83D\\uDC0C Worst: \".concat(summary.slowestProvider.provider, \" (\").concat(summary.slowestProvider.averageTotal, \"ms)\"));\n    }\n    console.log('==========================\\n');\n}\n// Export all logging functions for easy terminal access\nconst performanceLogs = {\n    comprehensive: logComprehensivePerformanceReport,\n    firstToken: logFirstTokenReport,\n    googleDebug: logGoogleStreamingDebug,\n    quick: quickPerformanceCheck,\n    startMonitoring: startPerformanceMonitoring,\n    stopMonitoring: stopPerformanceMonitoring\n};\n// Make functions available globally for terminal access\nif (typeof globalThis !== 'undefined') {\n    globalThis.logComprehensivePerformanceReport = logComprehensivePerformanceReport;\n    globalThis.logFirstTokenReport = logFirstTokenReport;\n    globalThis.logGoogleStreamingDebug = logGoogleStreamingDebug;\n    globalThis.quickPerformanceCheck = quickPerformanceCheck;\n    globalThis.startPerformanceMonitoring = startPerformanceMonitoring;\n    globalThis.stopPerformanceMonitoring = stopPerformanceMonitoring;\n    globalThis.performanceLogs = performanceLogs;\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/utils/performanceLogs.ts\n"));

/***/ })

});