"use strict";(()=>{var e={};e.id=8246,e.ids=[2842,8246],e.modules={507:(e,t,r)=>{r.d(t,{Dc:()=>a,p2:()=>i});let i=[{id:"general_chat",name:"<PERSON> Chat",description:"Casual conversation, simple questions, greetings, and basic interactions that do not require specialized expertise."},{id:"logic_reasoning",name:"Logic & Reasoning",description:"Mathematical calculations, logical puzzles, analytical problem-solving, deductive reasoning, and complex analytical thinking."},{id:"writing",name:"Writing & Content Creation",description:"Creating written content like stories, articles, books, poems, scripts, marketing copy, blog posts, and creative narratives."},{id:"coding_frontend",name:"Coding - Frontend",description:"Building user interfaces with HTML, CSS, JavaScript, React, Vue, Angular, web design, and client-side development."},{id:"coding_backend",name:"Coding - Backend",description:"Programming server-side applications, APIs, databases, Python scripts, Node.js, Java, backend logic, and system architecture."},{id:"research_synthesis",name:"Research & Synthesis",description:"Gathering information from sources, conducting research, analyzing data, and synthesizing findings into comprehensive reports."},{id:"summarization_briefing",name:"Summarization & Briefing",description:"Condensing lengthy documents, extracting key points, creating executive summaries, and briefing complex information."},{id:"translation_localization",name:"Translation & Localization",description:"Converting text between different languages, linguistic translation, cultural adaptation, and multilingual communication."},{id:"data_extraction_structuring",name:"Data Extraction & Structuring",description:"Extracting specific information from unstructured text, organizing data into tables, lists, JSON, CSV, and structured formats."},{id:"brainstorming_ideation",name:"Brainstorming & Ideation",description:"Generating creative ideas, innovative concepts, brainstorming solutions, exploring possibilities, and creative thinking sessions."},{id:"education_tutoring",name:"Education & Tutoring",description:"Teaching concepts, explaining topics step-by-step, creating educational materials, tutoring, and providing learning guidance."},{id:"image_generation",name:"Image Generation",description:"Creating visual images, artwork, graphics, and illustrations from textual descriptions using AI image generation models."},{id:"audio_transcription",name:"Audio Transcription",description:"Converting speech from audio files into written text, transcribing recordings, and speech-to-text processing."},{id:"data_extractor",name:"Data Extractor",description:"Extracting specific data from web pages, scraping content, and gathering information from websites."},{id:"form_filler",name:"Form Filler",description:"Filling out web forms, submitting data, and handling form-based interactions on websites."},{id:"verification_agent",name:"Verification Agent",description:"Verifying information on websites, fact-checking, and validating data accuracy."},{id:"research_assistant",name:"Research Assistant",description:"Conducting web-based research, gathering information from multiple sources, and compiling research findings."},{id:"shopping_assistant",name:"Shopping Assistant",description:"Helping with online shopping, price comparisons, product research, and e-commerce tasks."},{id:"price_comparison",name:"Price Comparison",description:"Comparing prices across different websites, finding deals, and analyzing product pricing."},{id:"fact_checker",name:"Fact Checker",description:"Verifying facts and information across multiple web sources, cross-referencing data for accuracy."},{id:"task_executor",name:"Task Executor",description:"General task execution and automation, handling various web-based tasks and workflows."}],a=e=>i.find(t=>t.id===e)},1025:(e,t,r)=>{r.d(t,{C:()=>n});var i=r(62633),a=r(60799),s=r(99366);class n extends a.m{static lc_name(){return"ImagePromptTemplate"}constructor(e){if(super(e),Object.defineProperty(this,"lc_namespace",{enumerable:!0,configurable:!0,writable:!0,value:["langchain_core","prompts","image"]}),Object.defineProperty(this,"template",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"templateFormat",{enumerable:!0,configurable:!0,writable:!0,value:"f-string"}),Object.defineProperty(this,"validateTemplate",{enumerable:!0,configurable:!0,writable:!0,value:!0}),Object.defineProperty(this,"additionalContentFields",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.template=e.template,this.templateFormat=e.templateFormat??this.templateFormat,this.validateTemplate=e.validateTemplate??this.validateTemplate,this.additionalContentFields=e.additionalContentFields,this.validateTemplate){let e=this.inputVariables;this.partialVariables&&(e=e.concat(Object.keys(this.partialVariables))),(0,s.Ns)([{type:"image_url",image_url:this.template}],this.templateFormat,e)}}_getPromptType(){return"prompt"}async partial(e){let t=this.inputVariables.filter(t=>!(t in e)),r={...this.partialVariables??{},...e};return new n({...this,inputVariables:t,partialVariables:r})}async format(e){let t={};for(let[r,i]of Object.entries(this.template))"string"==typeof i?t[r]=(0,s.Xm)(i,this.templateFormat,e):t[r]=i;let r=e.url||t.url,i=e.detail||t.detail;if(!r)throw Error("Must provide either an image URL.");if("string"!=typeof r)throw Error("url must be a string.");let a={url:r};return i&&(a.detail=i),a}async formatPromptValue(e){let t=await this.format(e);return new i.ImagePromptValue(t)}}},2507:(e,t,r)=>{r.d(t,{Q:()=>n,createSupabaseServerClientOnRequest:()=>s});var i=r(34386),a=r(44999);async function s(){let e=await (0,a.UL)();return(0,i.createServerClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8",{cookies:{get:t=>e.get(t)?.value,set(t,r,i){try{e.set({name:t,value:r,...i})}catch(e){}},remove(t,r){try{e.set({name:t,value:"",...r})}catch(e){}}}})}function n(e){return(0,i.createServerClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8",{cookies:{get:t=>e.cookies.get(t)?.value,set(e,t,r){},remove(e,t){}}})}},2842:(e,t,r)=>{r.d(t,{trainingDataCache:()=>a});class i{set(e,t,r){this.cache.set(e,{data:t,timestamp:Date.now(),jobId:r})}get(e){let t=this.cache.get(e);return t?Date.now()-t.timestamp>this.TTL?(this.cache.delete(e),null):t:null}invalidate(e){return this.cache.delete(e)}clear(){this.cache.clear()}cleanup(){let e=Date.now();for(let[t,r]of this.cache.entries())e-r.timestamp>this.TTL&&this.cache.delete(t)}getStats(){return{size:this.cache.size,keys:Array.from(this.cache.keys())}}constructor(){this.cache=new Map,this.TTL=3e5}}let a=new i;setInterval(()=>{a.cleanup()},6e5)},3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{e.exports=require("punycode")},27910:e=>{e.exports=require("stream")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33892:(e,t,r)=>{r.d(t,{PromptTemplate:()=>s});var i=r(58111),a=r(99366);class s extends i.L{static lc_name(){return"PromptTemplate"}constructor(e){if(super(e),Object.defineProperty(this,"template",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"templateFormat",{enumerable:!0,configurable:!0,writable:!0,value:"f-string"}),Object.defineProperty(this,"validateTemplate",{enumerable:!0,configurable:!0,writable:!0,value:!0}),Object.defineProperty(this,"additionalContentFields",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),"mustache"===e.templateFormat&&void 0===e.validateTemplate&&(this.validateTemplate=!1),Object.assign(this,e),this.validateTemplate){if("mustache"===this.templateFormat)throw Error("Mustache templates cannot be validated.");let e=this.inputVariables;this.partialVariables&&(e=e.concat(Object.keys(this.partialVariables))),(0,a.Ns)(this.template,this.templateFormat,e)}}_getPromptType(){return"prompt"}async format(e){let t=await this.mergePartialAndUserVariables(e);return(0,a.Xm)(this.template,this.templateFormat,t)}static fromExamples(e,t,r,i="\n\n",a=""){return new s({inputVariables:r,template:[a,...e,t].join(i)})}static fromTemplate(e,t){let{templateFormat:r="f-string",...i}=t??{},n=new Set;return(0,a.QC)(e,r).forEach(e=>{"variable"===e.type&&n.add(e.name)}),new s({inputVariables:[...n],templateFormat:r,template:e,...i})}async partial(e){let t=this.inputVariables.filter(t=>!(t in e)),r={...this.partialVariables??{},...e};return new s({...this,inputVariables:t,partialVariables:r})}serialize(){if(void 0!==this.outputParser)throw Error("Cannot serialize a prompt template with an output parser");return{_type:this._getPromptType(),input_variables:this.inputVariables,template:this.template,template_format:this.templateFormat}}static async deserialize(e){if(!e.template)throw Error("Prompt template must have a template");return new s({inputVariables:e.input_variables,template:e.template,templateFormat:e.template_format})}}},34631:e=>{e.exports=require("tls")},42298:(e,t,r)=>{r.d(t,{l:()=>s});var i=r(90163),a=r(99366);class s extends i.YN{static lc_name(){return"DictPromptTemplate"}constructor(e){let t=e.templateFormat??"f-string",r=function e(t,r){let i=[];for(let s of Object.values(t))if("string"==typeof s)(0,a.QC)(s,r).forEach(e=>{"variable"===e.type&&i.push(e.name)});else if(Array.isArray(s))for(let t of s)"string"==typeof t?(0,a.QC)(t,r).forEach(e=>{"variable"===e.type&&i.push(e.name)}):"object"==typeof t&&i.push(...e(t,r));else"object"==typeof s&&null!==s&&i.push(...e(s,r));return Array.from(new Set(i))}(e.template,t);super({inputVariables:r,...e}),Object.defineProperty(this,"lc_namespace",{enumerable:!0,configurable:!0,writable:!0,value:["langchain_core","prompts","dict"]}),Object.defineProperty(this,"lc_serializable",{enumerable:!0,configurable:!0,writable:!0,value:!0}),Object.defineProperty(this,"template",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"templateFormat",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"inputVariables",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.template=e.template,this.templateFormat=t,this.inputVariables=r}async format(e){return function e(t,r,i){let s={};for(let[n,o]of Object.entries(t))if("string"==typeof o)s[n]=(0,a.Xm)(o,i,r);else if(Array.isArray(o)){let t=[];for(let s of o)"string"==typeof s?t.push((0,a.Xm)(s,i,r)):"object"==typeof s&&t.push(e(s,r,i));s[n]=t}else"object"==typeof o&&null!==o?s[n]=e(o,r,i):s[n]=o;return s}(this.template,e,this.templateFormat)}async invoke(e){return await this._callWithConfig(this.format.bind(this),e,{runType:"prompt"})}}},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},46625:(e,t,r)=>{r.d(t,{FewShotPromptTemplate:()=>o,s:()=>l});var i=r(58111),a=r(99366),s=r(33892),n=r(58356);class o extends i.L{constructor(e){if(super(e),Object.defineProperty(this,"lc_serializable",{enumerable:!0,configurable:!0,writable:!0,value:!1}),Object.defineProperty(this,"examples",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"exampleSelector",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"examplePrompt",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"suffix",{enumerable:!0,configurable:!0,writable:!0,value:""}),Object.defineProperty(this,"exampleSeparator",{enumerable:!0,configurable:!0,writable:!0,value:"\n\n"}),Object.defineProperty(this,"prefix",{enumerable:!0,configurable:!0,writable:!0,value:""}),Object.defineProperty(this,"templateFormat",{enumerable:!0,configurable:!0,writable:!0,value:"f-string"}),Object.defineProperty(this,"validateTemplate",{enumerable:!0,configurable:!0,writable:!0,value:!0}),Object.assign(this,e),void 0!==this.examples&&void 0!==this.exampleSelector)throw Error("Only one of 'examples' and 'example_selector' should be provided");if(void 0===this.examples&&void 0===this.exampleSelector)throw Error("One of 'examples' and 'example_selector' should be provided");if(this.validateTemplate){let e=this.inputVariables;this.partialVariables&&(e=e.concat(Object.keys(this.partialVariables))),(0,a.Ns)(this.prefix+this.suffix,this.templateFormat,e)}}_getPromptType(){return"few_shot"}static lc_name(){return"FewShotPromptTemplate"}async getExamples(e){if(void 0!==this.examples)return this.examples;if(void 0!==this.exampleSelector)return this.exampleSelector.selectExamples(e);throw Error("One of 'examples' and 'example_selector' should be provided")}async partial(e){let t=this.inputVariables.filter(t=>!(t in e)),r={...this.partialVariables??{},...e};return new o({...this,inputVariables:t,partialVariables:r})}async format(e){let t=await this.mergePartialAndUserVariables(e),r=await this.getExamples(t),i=await Promise.all(r.map(e=>this.examplePrompt.format(e))),s=[this.prefix,...i,this.suffix].join(this.exampleSeparator);return(0,a.Xm)(s,this.templateFormat,t)}serialize(){if(this.exampleSelector||!this.examples)throw Error("Serializing an example selector is not currently supported");if(void 0!==this.outputParser)throw Error("Serializing an output parser is not currently supported");return{_type:this._getPromptType(),input_variables:this.inputVariables,example_prompt:this.examplePrompt.serialize(),example_separator:this.exampleSeparator,suffix:this.suffix,prefix:this.prefix,template_format:this.templateFormat,examples:this.examples}}static async deserialize(e){let t,{example_prompt:r}=e;if(!r)throw Error("Missing example prompt");let i=await s.PromptTemplate.deserialize(r);if(Array.isArray(e.examples))t=e.examples;else throw Error("Invalid examples format. Only list or string are supported.");return new o({inputVariables:e.input_variables,examplePrompt:i,examples:t,exampleSeparator:e.example_separator,prefix:e.prefix,suffix:e.suffix,templateFormat:e.template_format})}}class l extends n.qF{_getPromptType(){return"few_shot_chat"}static lc_name(){return"FewShotChatMessagePromptTemplate"}constructor(e){if(super(e),Object.defineProperty(this,"lc_serializable",{enumerable:!0,configurable:!0,writable:!0,value:!0}),Object.defineProperty(this,"examples",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"exampleSelector",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"examplePrompt",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"suffix",{enumerable:!0,configurable:!0,writable:!0,value:""}),Object.defineProperty(this,"exampleSeparator",{enumerable:!0,configurable:!0,writable:!0,value:"\n\n"}),Object.defineProperty(this,"prefix",{enumerable:!0,configurable:!0,writable:!0,value:""}),Object.defineProperty(this,"templateFormat",{enumerable:!0,configurable:!0,writable:!0,value:"f-string"}),Object.defineProperty(this,"validateTemplate",{enumerable:!0,configurable:!0,writable:!0,value:!0}),this.examples=e.examples,this.examplePrompt=e.examplePrompt,this.exampleSeparator=e.exampleSeparator??"\n\n",this.exampleSelector=e.exampleSelector,this.prefix=e.prefix??"",this.suffix=e.suffix??"",this.templateFormat=e.templateFormat??"f-string",this.validateTemplate=e.validateTemplate??!0,void 0!==this.examples&&void 0!==this.exampleSelector)throw Error("Only one of 'examples' and 'example_selector' should be provided");if(void 0===this.examples&&void 0===this.exampleSelector)throw Error("One of 'examples' and 'example_selector' should be provided");if(this.validateTemplate){let e=this.inputVariables;this.partialVariables&&(e=e.concat(Object.keys(this.partialVariables))),(0,a.Ns)(this.prefix+this.suffix,this.templateFormat,e)}}async getExamples(e){if(void 0!==this.examples)return this.examples;if(void 0!==this.exampleSelector)return this.exampleSelector.selectExamples(e);throw Error("One of 'examples' and 'example_selector' should be provided")}async formatMessages(e){let t=await this.mergePartialAndUserVariables(e),r=await this.getExamples(t);r=r.map(e=>{let t={};return this.examplePrompt.inputVariables.forEach(r=>{t[r]=e[r]}),t});let i=[];for(let e of r){let t=await this.examplePrompt.formatMessages(e);i.push(...t)}return i}async format(e){let t=await this.mergePartialAndUserVariables(e),r=await this.getExamples(t),i=(await Promise.all(r.map(e=>this.examplePrompt.formatMessages(e)))).flat().map(e=>e.content),s=[this.prefix,...i,this.suffix].join(this.exampleSeparator);return(0,a.Xm)(s,this.templateFormat,t)}async partial(e){let t=this.inputVariables.filter(t=>!(t in e)),r={...this.partialVariables??{},...e};return new l({...this,inputVariables:t,partialVariables:r})}}},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},56534:(e,t,r)=>{r.d(t,{Y:()=>c,w:()=>l});let i="AES-GCM",a=process.env.ROKEY_ENCRYPTION_KEY;if(!a||64!==a.length)throw Error("Invalid ROKEY_ENCRYPTION_KEY. Please set a 64-character hex string (32 bytes) for ROKEY_ENCRYPTION_KEY in your .env.local file.");function s(e){let t=new Uint8Array(e.length/2);for(let r=0;r<e.length;r+=2)t[r/2]=parseInt(e.substr(r,2),16);return t}function n(e){return Array.from(e,e=>e.toString(16).padStart(2,"0")).join("")}let o=s(a);async function l(e){if("string"!=typeof e||0===e.length)throw Error("Encryption input must be a non-empty string.");let t=crypto.getRandomValues(new Uint8Array(12)),r=await crypto.subtle.importKey("raw",o,{name:i},!1,["encrypt"]),a=new TextEncoder().encode(e),s=new Uint8Array(await crypto.subtle.encrypt({name:i,iv:t},r,a)),l=s.slice(0,-16),c=s.slice(-16);return`${n(t)}:${n(c)}:${n(l)}`}async function c(e){if("string"!=typeof e||0===e.length)throw Error("Decryption input must be a non-empty string.");let t=e.split(":");if(3!==t.length)throw Error("Invalid encrypted text format. Expected iv:authTag:encryptedData");let r=s(t[0]),a=s(t[1]),n=s(t[2]);if(12!==r.length)throw Error("Invalid IV length. Expected 12 bytes.");if(16!==a.length)throw Error("Invalid authTag length. Expected 16 bytes.");let l=await crypto.subtle.importKey("raw",o,{name:i},!1,["decrypt"]),c=new Uint8Array(n.length+a.length);c.set(n),c.set(a,n.length);let u=await crypto.subtle.decrypt({name:i,iv:r},l,c);return new TextDecoder().decode(u)}},58111:(e,t,r)=>{r.d(t,{L:()=>s});var i=r(62633),a=r(60799);class s extends a.m{async formatPromptValue(e){let t=await this.format(e);return new i.StringPromptValue(t)}}},58356:(e,t,r)=>{r.d(t,{BJ:()=>v,FS:()=>_,GL:()=>m,OT:()=>f,RZ:()=>k,Wn:()=>b,pl:()=>p,qF:()=>g,sS:()=>w});var i=r(61751),a=r(62633),s=r(90163),n=r(58111),o=r(60799),l=r(33892),c=r(1025),u=r(99366),h=r(13346),d=r(42298);class p extends s.YN{constructor(){super(...arguments),Object.defineProperty(this,"lc_namespace",{enumerable:!0,configurable:!0,writable:!0,value:["langchain_core","prompts","chat"]}),Object.defineProperty(this,"lc_serializable",{enumerable:!0,configurable:!0,writable:!0,value:!0})}async invoke(e,t){return this._callWithConfig(e=>this.formatMessages(e),e,{...t,runType:"prompt"})}}class m extends p{static lc_name(){return"MessagesPlaceholder"}constructor(e){"string"==typeof e&&(e={variableName:e}),super(e),Object.defineProperty(this,"variableName",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"optional",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.variableName=e.variableName,this.optional=e.optional??!1}get inputVariables(){return[this.variableName]}async formatMessages(e){let t,r=e[this.variableName];if(this.optional&&!r)return[];if(!r){let e=Error(`Field "${this.variableName}" in prompt uses a MessagesPlaceholder, which expects an array of BaseMessages as an input value. Received: undefined`);throw e.name="InputFormatError",e}try{t=Array.isArray(r)?r.map(i.coerceMessageLikeToMessage):[(0,i.coerceMessageLikeToMessage)(r)]}catch(i){let e="string"==typeof r?r:JSON.stringify(r,null,2),t=Error(`Field "${this.variableName}" in prompt uses a MessagesPlaceholder, which expects an array of BaseMessages or coerceable values as input.

Received value: ${e}

Additional message: ${i.message}`);throw t.name="InputFormatError",t.lc_error_code=i.lc_error_code,t}return t}}class f extends p{constructor(e){"prompt"in e||(e={prompt:e}),super(e),Object.defineProperty(this,"prompt",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.prompt=e.prompt}get inputVariables(){return this.prompt.inputVariables}async formatMessages(e){return[await this.format(e)]}}class g extends o.m{constructor(e){super(e)}async format(e){return(await this.formatPromptValue(e)).toString()}async formatPromptValue(e){let t=await this.formatMessages(e);return new a.ChatPromptValue(t)}}class b extends f{static lc_name(){return"ChatMessagePromptTemplate"}constructor(e,t){"prompt"in e||(e={prompt:e,role:t}),super(e),Object.defineProperty(this,"role",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.role=e.role}async format(e){return new i.ChatMessage(await this.prompt.format(e),this.role)}static fromTemplate(e,t,r){return new this(l.PromptTemplate.fromTemplate(e,{templateFormat:r?.templateFormat}),t)}}class y extends p{static _messageClass(){throw Error("Can not invoke _messageClass from inside _StringImageMessagePromptTemplate")}constructor(e,t){if("prompt"in e||(e={prompt:e}),super(e),Object.defineProperty(this,"lc_namespace",{enumerable:!0,configurable:!0,writable:!0,value:["langchain_core","prompts","chat"]}),Object.defineProperty(this,"lc_serializable",{enumerable:!0,configurable:!0,writable:!0,value:!0}),Object.defineProperty(this,"inputVariables",{enumerable:!0,configurable:!0,writable:!0,value:[]}),Object.defineProperty(this,"additionalOptions",{enumerable:!0,configurable:!0,writable:!0,value:{}}),Object.defineProperty(this,"prompt",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"messageClass",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"chatMessageClass",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.prompt=e.prompt,Array.isArray(this.prompt)){let e=[];this.prompt.forEach(t=>{"inputVariables"in t&&(e=e.concat(t.inputVariables))}),this.inputVariables=e}else this.inputVariables=this.prompt.inputVariables;this.additionalOptions=t??this.additionalOptions}createMessage(e){let t=this.constructor;if(t._messageClass())return new(t._messageClass())({content:e});if(t.chatMessageClass){let r=t.chatMessageClass();return new r({content:e,role:this.getRoleFromMessageClass(r.lc_name())})}throw Error("No message class defined")}getRoleFromMessageClass(e){switch(e){case"HumanMessage":return"human";case"AIMessage":return"ai";case"SystemMessage":return"system";case"ChatMessage":return"chat";default:throw Error("Invalid message class name")}}static fromTemplate(e,t){if("string"==typeof e)return new this(l.PromptTemplate.fromTemplate(e,t));let r=[];for(let i of e)if("string"==typeof i)r.push(l.PromptTemplate.fromTemplate(i,t));else if(null===i);else if(!(null===i||"object"!=typeof i||Array.isArray(i))&&1===Object.keys(i).length&&"text"in i&&"string"==typeof i.text){let e="";"string"==typeof i.text&&(e=i.text??"");let a={...t,additionalContentFields:i};r.push(l.PromptTemplate.fromTemplate(e,a))}else if(!(null===i||"object"!=typeof i||Array.isArray(i))&&"image_url"in i&&("string"==typeof i.image_url||"object"==typeof i.image_url&&null!==i.image_url&&"url"in i.image_url&&"string"==typeof i.image_url.url)){let e,a=i.image_url??"",s=[];if("string"==typeof a){let r,n=(t?.templateFormat==="mustache"?(0,u.g2)(a):(0,u.D4)(a)).flatMap(e=>"variable"===e.type?[e.name]:[]);if((n?.length??0)>0){if(n.length>1)throw Error(`Only one format variable allowed per image template.
Got: ${n}
From: ${a}`);s=[n[0]]}else s=[];a={url:a},e=new c.C({template:a,inputVariables:s,templateFormat:t?.templateFormat,additionalContentFields:i})}else if("object"==typeof a){if("url"in a){let e;s=(t?.templateFormat==="mustache"?(0,u.g2)(a.url):(0,u.D4)(a.url)).flatMap(e=>"variable"===e.type?[e.name]:[])}else s=[];e=new c.C({template:a,inputVariables:s,templateFormat:t?.templateFormat,additionalContentFields:i})}else throw Error("Invalid image template");r.push(e)}else"object"==typeof i&&r.push(new d.l({template:i,templateFormat:t?.templateFormat}));return new this({prompt:r,additionalOptions:t})}async format(e){if(this.prompt instanceof n.L){let t=await this.prompt.format(e);return this.createMessage(t)}{let t=[];for(let r of this.prompt){let i={};if(!("inputVariables"in r))throw Error(`Prompt ${r} does not have inputVariables defined.`);for(let t of r.inputVariables)i||(i={[t]:e[t]}),i={...i,[t]:e[t]};if(r instanceof n.L){let e,a=await r.format(i);"additionalContentFields"in r&&(e=r.additionalContentFields),t.push({...e,type:"text",text:a})}else if(r instanceof c.C){let e,a=await r.format(i);"additionalContentFields"in r&&(e=r.additionalContentFields),t.push({...e,type:"image_url",image_url:a})}else if(r instanceof d.l){let e,a=await r.format(i);"additionalContentFields"in r&&(e=r.additionalContentFields),t.push({...e,...a})}}return this.createMessage(t)}}async formatMessages(e){return[await this.format(e)]}}class _ extends y{static _messageClass(){return i.HumanMessage}static lc_name(){return"HumanMessagePromptTemplate"}}class w extends y{static _messageClass(){return i.AIMessage}static lc_name(){return"AIMessagePromptTemplate"}}class v extends y{static _messageClass(){return i.SystemMessage}static lc_name(){return"SystemMessagePromptTemplate"}}class k extends g{static lc_name(){return"ChatPromptTemplate"}get lc_aliases(){return{promptMessages:"messages"}}constructor(e){if(super(e),Object.defineProperty(this,"promptMessages",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"validateTemplate",{enumerable:!0,configurable:!0,writable:!0,value:!0}),Object.defineProperty(this,"templateFormat",{enumerable:!0,configurable:!0,writable:!0,value:"f-string"}),"mustache"===e.templateFormat&&void 0===e.validateTemplate&&(this.validateTemplate=!1),Object.assign(this,e),this.validateTemplate){let e=new Set;for(let t of this.promptMessages)if(!(t instanceof i.BaseMessage))for(let r of t.inputVariables)e.add(r);let t=this.inputVariables,r=new Set(this.partialVariables?t.concat(Object.keys(this.partialVariables)):t),a=new Set([...r].filter(t=>!e.has(t)));if(a.size>0)throw Error(`Input variables \`${[...a]}\` are not used in any of the prompt messages.`);let s=new Set([...e].filter(e=>!r.has(e)));if(s.size>0)throw Error(`Input variables \`${[...s]}\` are used in prompt messages but not in the prompt template.`)}}_getPromptType(){return"chat"}async _parseImagePrompts(e,t){if("string"==typeof e.content)return e;let r=await Promise.all(e.content.map(async e=>{if("image_url"!==e.type)return e;let r="";r="string"==typeof e.image_url?e.image_url:e.image_url.url;let i=l.PromptTemplate.fromTemplate(r,{templateFormat:this.templateFormat}),a=await i.format(t);return"string"!=typeof e.image_url&&"url"in e.image_url?e.image_url.url=a:e.image_url=a,e}));return e.content=r,e}async formatMessages(e){let t=await this.mergePartialAndUserVariables(e),r=[];for(let e of this.promptMessages)if(e instanceof i.BaseMessage)r.push(await this._parseImagePrompts(e,t));else{let i=e.inputVariables.reduce((r,i)=>{if(!(i in t)&&!("MessagesPlaceholder"===e.constructor.lc_name()&&e.optional))throw(0,h.Y)(Error(`Missing value for input variable \`${i.toString()}\``),"INVALID_PROMPT_INPUT");return r[i]=t[i],r},{}),a=await e.formatMessages(i);r=r.concat(a)}return r}async partial(e){let t=this.inputVariables.filter(t=>!(t in e)),r={...this.partialVariables??{},...e};return new k({...this,inputVariables:t,partialVariables:r})}static fromTemplate(e,t){let r=new _({prompt:l.PromptTemplate.fromTemplate(e,t)});return this.fromMessages([r])}static fromMessages(e,t){let r=e.reduce((e,r)=>e.concat(r instanceof k?r.promptMessages:[function(e,t){let r;if("function"==typeof e.formatMessages||(0,i.isBaseMessage)(e))return e;if(Array.isArray(e)&&"placeholder"===e[0]){let r=e[1];if(t?.templateFormat==="mustache"&&"string"==typeof r&&"{{"===r.slice(0,2)&&"}}"===r.slice(-2))return new m({variableName:r.slice(2,-2),optional:!0});if("string"==typeof r&&"{"===r[0]&&"}"===r[r.length-1])return new m({variableName:r.slice(1,-1),optional:!0});throw Error(`Invalid placeholder template for format ${t?.templateFormat??'"f-string"'}: "${e[1]}". Expected a variable name surrounded by ${t?.templateFormat==="mustache"?"double":"single"} curly braces.`)}let a=(0,i.coerceMessageLikeToMessage)(e);if(r="string"==typeof a.content?a.content:a.content.map(e=>"text"in e?{...e,text:e.text}:"image_url"in e?{...e,image_url:e.image_url}:e),"human"===a._getType())return _.fromTemplate(r,t);if("ai"===a._getType())return w.fromTemplate(r,t);if("system"===a._getType())return v.fromTemplate(r,t);if(i.ChatMessage.isInstance(a))return b.fromTemplate(a.content,a.role,t);throw Error(`Could not coerce message prompt template from input. Received message type: "${a._getType()}".`)}(r,t)]),[]),a=e.reduce((e,t)=>t instanceof k?Object.assign(e,t.partialVariables):e,Object.create(null)),s=new Set;for(let e of r)if(!(e instanceof i.BaseMessage))for(let t of e.inputVariables)t in a||s.add(t);return new this({...t,inputVariables:[...s],promptMessages:r,partialVariables:a,templateFormat:t?.templateFormat})}static fromPromptMessages(e){return this.fromMessages(e)}}},60799:(e,t,r)=>{r.d(t,{m:()=>a});var i=r(90163);class a extends i.YN{get lc_attributes(){return{partialVariables:void 0}}constructor(e){super(e),Object.defineProperty(this,"lc_serializable",{enumerable:!0,configurable:!0,writable:!0,value:!0}),Object.defineProperty(this,"lc_namespace",{enumerable:!0,configurable:!0,writable:!0,value:["langchain_core","prompts",this._getPromptType()]}),Object.defineProperty(this,"inputVariables",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"outputParser",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"partialVariables",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"metadata",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"tags",{enumerable:!0,configurable:!0,writable:!0,value:void 0});let{inputVariables:t}=e;if(t.includes("stop"))throw Error("Cannot have an input variable named 'stop', as it is used internally, please rename.");Object.assign(this,e)}async mergePartialAndUserVariables(e){let t=this.partialVariables??{},r={};for(let[e,i]of Object.entries(t))"string"==typeof i?r[e]=i:r[e]=await i();return{...r,...e}}async invoke(e,t){let r={...this.metadata,...t?.metadata},i=[...this.tags??[],...t?.tags??[]];return this._callWithConfig(e=>this.formatPromptValue(e),e,{...t,tags:i,metadata:r,runType:"prompt"})}serialize(){throw Error("Use .toJSON() instead")}static async deserialize(e){switch(e._type){case"prompt":{let{PromptTemplate:t}=await Promise.resolve().then(r.bind(r,33892));return t.deserialize(e)}case void 0:{let{PromptTemplate:t}=await Promise.resolve().then(r.bind(r,33892));return t.deserialize({...e,_type:"prompt"})}case"few_shot":{let{FewShotPromptTemplate:t}=await Promise.resolve().then(r.bind(r,46625));return t.deserialize(e)}default:throw Error(`Invalid prompt type in config: ${e._type}`)}}}},61751:(e,t,r)=>{r.r(t),r.d(t,{AIMessage:()=>i.Od,AIMessageChunk:()=>i.H,BaseMessage:()=>a.XQ,BaseMessageChunk:()=>a.gj,ChatMessage:()=>s.cM,ChatMessageChunk:()=>s.XU,FunctionMessage:()=>n.mg,FunctionMessageChunk:()=>n.FK,HumanMessage:()=>o.xc,HumanMessageChunk:()=>o.a7,RemoveMessage:()=>h,SystemMessage:()=>l.tn,SystemMessageChunk:()=>l.uU,ToolMessage:()=>d.uf,ToolMessageChunk:()=>d.dr,_isMessageFieldWithRole:()=>a.dp,_mergeDicts:()=>a.ns,_mergeLists:()=>a.Vt,_mergeObj:()=>a.F7,_mergeStatus:()=>a.Iv,coerceMessageLikeToMessage:()=>c.K0,convertToChunk:()=>c.ih,convertToOpenAIImageBlock:()=>S.Vi,convertToProviderContentBlock:()=>S.up,defaultTextSplitter:()=>x,filterMessages:()=>m,getBufferString:()=>c.Sw,isAIMessage:()=>i.KX,isAIMessageChunk:()=>i.jm,isBase64ContentBlock:()=>S.cJ,isBaseMessage:()=>a.ny,isBaseMessageChunk:()=>a.AJ,isChatMessage:()=>s.kY,isChatMessageChunk:()=>s.bU,isDataContentBlock:()=>S.Fz,isFunctionMessage:()=>n.AP,isFunctionMessageChunk:()=>n.vl,isHumanMessage:()=>o.di,isHumanMessageChunk:()=>o.GZ,isIDContentBlock:()=>S.oe,isOpenAIToolCallArray:()=>a.Ao,isPlainTextContentBlock:()=>S.Ac,isSystemMessage:()=>l.X4,isSystemMessageChunk:()=>l.UF,isToolMessage:()=>d.wk,isToolMessageChunk:()=>d.P,isURLContentBlock:()=>S.W,mapChatMessagesToStoredMessages:()=>c.Js,mapStoredMessageToChatMessage:()=>c.Pz,mapStoredMessagesToChatMessages:()=>c.rf,mergeContent:()=>a._I,mergeMessageRuns:()=>g,parseBase64DataUrl:()=>S.Q7,parseMimeType:()=>S.Ej,trimMessages:()=>y});var i=r(16275),a=r(72892),s=r(28279),n=r(57161),o=r(75448),l=r(98488),c=r(81412),u=r(90163);class h extends a.XQ{constructor(e){super({...e,content:""}),Object.defineProperty(this,"id",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.id=e.id}_getType(){return"remove"}get _printableFields(){return{...super._printableFields,id:this.id}}}var d=r(28895);let p=(e,t)=>{let r=[...new Set(t?.map(e=>{if("string"==typeof e)return e;let t=new e({});if(!("getType"in t)||"function"!=typeof t.getType)throw Error("Invalid type provided.");return t.getType()}))],i=e.getType();return r.some(e=>e===i)};function m(e,t){return Array.isArray(e)?f(e,t):u.jY.from(t=>f(t,e))}function f(e,t={}){let{includeNames:r,excludeNames:i,includeTypes:a,excludeTypes:s,includeIds:n,excludeIds:o}=t,l=[];for(let t of e)if(!(i&&t.name&&i.includes(t.name))){if(s&&p(t,s))continue;if(o&&t.id&&o.includes(t.id))continue;a||n||r?r&&t.name&&r.some(e=>e===t.name)||a&&p(t,a)?l.push(t):n&&t.id&&n.some(e=>e===t.id)&&l.push(t):l.push(t)}return l}function g(e){return Array.isArray(e)?b(e):u.jY.from(b)}function b(e){if(!e.length)return[];let t=[];for(let r of e){let e=t.pop();if(e)if("tool"===r.getType()||r.getType()!==e.getType())t.push(e,r);else{let i=(0,c.ih)(e),a=(0,c.ih)(r),s=i.concat(a);"string"==typeof i.content&&"string"==typeof a.content&&(s.content=`${i.content}
${a.content}`),t.push(function(e){let t,r=e.getType(),i=Object.fromEntries(Object.entries(e).filter(([e])=>!["type","tool_call_chunks"].includes(e)&&!e.startsWith("lc_")));if(r in k&&(t=O(r,i)),!t)throw Error(`Unrecognized message chunk class ${r}. Supported classes are ${Object.keys(k)}`);return t}(s))}else t.push(r)}return t}function y(e,t){if(!Array.isArray(e))return u.jY.from(t=>_(t,e)).withConfig({runName:"trim_messages"});if(!t)throw Error("Options parameter is required when providing messages.");return _(e,t)}async function _(e,t){let r,{maxTokens:i,tokenCounter:a,strategy:s="last",allowPartial:n=!1,endOn:o,startOn:l,includeSystem:c=!1,textSplitter:u}=t;if(l&&"first"===s)throw Error("`startOn` should only be specified if `strategy` is 'last'.");if(c&&"first"===s)throw Error("`includeSystem` should only be specified if `strategy` is 'last'.");r="getNumTokens"in a?async e=>(await Promise.all(e.map(e=>a.getNumTokens(e.content)))).reduce((e,t)=>e+t,0):async e=>a(e);let h=x;if(u&&(h="splitText"in u?u.splitText:async e=>u(e)),"first"===s)return w(e,{maxTokens:i,tokenCounter:r,textSplitter:h,partialStrategy:n?"first":void 0,endOn:o});if("last"===s)return v(e,{maxTokens:i,tokenCounter:r,textSplitter:h,allowPartial:n,includeSystem:c,startOn:l,endOn:o});throw Error(`Unrecognized strategy: '${s}'. Must be one of 'first' or 'last'.`)}async function w(e,t){let{maxTokens:r,tokenCounter:i,textSplitter:a,partialStrategy:s,endOn:n}=t,o=[...e],l=0;for(let e=0;e<o.length;e+=1){let t=e>0?o.slice(0,-e):o;if(await i(t)<=r){l=o.length-e;break}}if(l<o.length-1&&s){let e=!1;if(Array.isArray(o[l].content)){let t=o[l];if("string"==typeof t.content)throw Error("Expected content to be an array.");let a=t.content.length,n="last"===s?[...t.content].reverse():t.content;for(let c=1;c<=a;c+=1){let a="first"===s?n.slice(0,c):n.slice(-c),u=Object.fromEntries(Object.entries(t).filter(([e])=>"type"!==e&&!e.startsWith("lc_"))),h=O(t.getType(),{...u,content:a}),d=[...o.slice(0,l),h];if(await i(d)<=r)o=d,l+=1,e=!0;else break}e&&"last"===s&&(t.content=[...n].reverse())}if(!e){let e,t=o[l];if(Array.isArray(t.content)&&t.content.some(e=>"string"==typeof e||"text"===e.type)){let r=t.content.find(e=>"text"===e.type&&e.text);e=r?.text}else"string"==typeof t.content&&(e=t.content);if(e){let n=await a(e),c=n.length;"last"===s&&n.reverse();for(let e=0;e<c-1;e+=1)if(n.pop(),t.content=n.join(""),await i([...o.slice(0,l),t])<=r){"last"===s&&(t.content=[...n].reverse().join("")),o=[...o.slice(0,l),t],l+=1;break}}}}if(n){let e=Array.isArray(n)?n:[n];for(;l>0&&!p(o[l-1],e);)l-=1}return o.slice(0,l)}async function v(e,t){let{allowPartial:r=!1,includeSystem:i=!1,endOn:s,startOn:n,...o}=t,l=e.map(e=>{let t=Object.fromEntries(Object.entries(e).filter(([e])=>"type"!==e&&!e.startsWith("lc_")));return O(e.getType(),t,(0,a.AJ)(e))});if(s){let e=Array.isArray(s)?s:[s];for(;l.length>0&&!p(l[l.length-1],e);)l=l.slice(0,-1)}let c=i&&l[0]?.getType()==="system",u=c?l.slice(0,1).concat(l.slice(1).reverse()):l.reverse();return(u=await w(u,{...o,partialStrategy:r?"last":void 0,endOn:n}),c)?[u[0],...u.slice(1).reverse()]:u.reverse()}let k={human:{message:o.xc,messageChunk:o.a7},ai:{message:i.Od,messageChunk:i.H},system:{message:l.tn,messageChunk:l.uU},developer:{message:l.tn,messageChunk:l.uU},tool:{message:d.uf,messageChunk:d.dr},function:{message:n.mg,messageChunk:n.FK},generic:{message:s.cM,messageChunk:s.XU},remove:{message:h,messageChunk:h}};function O(e,t,r){let a,c;switch(e){case"human":r?a=new o.a7(t):c=new o.xc(t);break;case"ai":if(r){let e={...t};"tool_calls"in e&&(e={...e,tool_call_chunks:e.tool_calls?.map(e=>({...e,type:"tool_call_chunk",index:void 0,args:JSON.stringify(e.args)}))}),a=new i.H(e)}else c=new i.Od(t);break;case"system":r?a=new l.uU(t):c=new l.tn(t);break;case"developer":r?a=new l.uU({...t,additional_kwargs:{...t.additional_kwargs,__openai_role__:"developer"}}):c=new l.tn({...t,additional_kwargs:{...t.additional_kwargs,__openai_role__:"developer"}});break;case"tool":if("tool_call_id"in t)r?a=new d.dr(t):c=new d.uf(t);else throw Error("Can not convert ToolMessage to ToolMessageChunk if 'tool_call_id' field is not defined.");break;case"function":if(r)a=new n.FK(t);else{if(!t.name)throw Error("FunctionMessage must have a 'name' field");c=new n.mg(t)}break;case"generic":if("role"in t)r?a=new s.XU(t):c=new s.cM(t);else throw Error("Can not convert ChatMessage to ChatMessageChunk if 'role' field is not defined.");break;default:throw Error(`Unrecognized message type ${e}`)}if(r&&a)return a;if(c)return c;throw Error(`Unrecognized message type ${e}`)}function x(e){let t=e.split("\n");return Promise.resolve([...t.slice(0,-1).map(e=>`${e}
`),t[t.length-1]])}var S=r(49517)},62633:(e,t,r)=>{r.r(t),r.d(t,{BasePromptValue:()=>n,ChatPromptValue:()=>l,ImagePromptValue:()=>c,StringPromptValue:()=>o});var i=r(84902),a=r(75448),s=r(81412);class n extends i.Serializable{}class o extends n{static lc_name(){return"StringPromptValue"}constructor(e){super({value:e}),Object.defineProperty(this,"lc_namespace",{enumerable:!0,configurable:!0,writable:!0,value:["langchain_core","prompt_values"]}),Object.defineProperty(this,"lc_serializable",{enumerable:!0,configurable:!0,writable:!0,value:!0}),Object.defineProperty(this,"value",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.value=e}toString(){return this.value}toChatMessages(){return[new a.xc(this.value)]}}class l extends n{static lc_name(){return"ChatPromptValue"}constructor(e){Array.isArray(e)&&(e={messages:e}),super(e),Object.defineProperty(this,"lc_namespace",{enumerable:!0,configurable:!0,writable:!0,value:["langchain_core","prompt_values"]}),Object.defineProperty(this,"lc_serializable",{enumerable:!0,configurable:!0,writable:!0,value:!0}),Object.defineProperty(this,"messages",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.messages=e.messages}toString(){return(0,s.Sw)(this.messages)}toChatMessages(){return this.messages}}class c extends n{static lc_name(){return"ImagePromptValue"}constructor(e){"imageUrl"in e||(e={imageUrl:e}),super(e),Object.defineProperty(this,"lc_namespace",{enumerable:!0,configurable:!0,writable:!0,value:["langchain_core","prompt_values"]}),Object.defineProperty(this,"lc_serializable",{enumerable:!0,configurable:!0,writable:!0,value:!0}),Object.defineProperty(this,"imageUrl",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"value",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.imageUrl=e.imageUrl}toString(){return this.imageUrl.url}toChatMessages(){return[new a.xc({content:[{type:"image_url",image_url:{detail:this.imageUrl.detail,url:this.imageUrl.url}}]})]}}},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},71344:(e,t,r)=>{let i,a;r.r(t),r.d(t,{patchFetch:()=>n0,routeModule:()=>nH,serverHooks:()=>nQ,workAsyncStorage:()=>nX,workUnitAsyncStorage:()=>nZ});var s={};r.r(s);var n={};r.r(n),r.d(n,{insecureHash:()=>eO,sha256:()=>eE});var o={};r.r(o),r.d(o,{BaseCache:()=>eM,InMemoryCache:()=>e$,deserializeStoredGeneration:()=>eN,getCacheKey:()=>eI,serializeGeneration:()=>eR});var l={};r.r(l),r.d(l,{BaseChatMessageHistory:()=>ez,BaseListChatMessageHistory:()=>eV,InMemoryChatMessageHistory:()=>eK});var c={};r.r(c),r.d(c,{Embeddings:()=>eJ});var u={};r.r(u),r.d(u,{BaseExampleSelector:()=>eG,BasePromptSelector:()=>eY,ConditionalPromptSelector:()=>eH,LengthBasedExampleSelector:()=>e0,SemanticSimilarityExampleSelector:()=>e5,isChatModel:()=>eZ,isLLM:()=>eX});var h={};r.r(h),r.d(h,{BaseLangChain:()=>ti,BaseLanguageModel:()=>ta,calculateMaxTokens:()=>tt,getEmbeddingContextSize:()=>e9,getModelContextSize:()=>e7,getModelNameForTiktoken:()=>e6,isOpenAITool:()=>te});var d={};r.r(d),r.d(d,{BaseChatModel:()=>tp,SimpleChatModel:()=>tm,createChatMessageChunkEncoderStream:()=>th});var p={};r.r(p),r.d(p,{BaseLLM:()=>tf,LLM:()=>tg});var m={};r.r(m),r.d(m,{BaseMemory:()=>tb,getInputValue:()=>t_,getOutputValue:()=>tw,getPromptInputKey:()=>tv});var f={};r.r(f),r.d(f,{RouterRunnable:()=>tk,Runnable:()=>e8.YN,RunnableAssign:()=>e8.B2,RunnableBinding:()=>e8.fJ,RunnableBranch:()=>tO,RunnableEach:()=>e8.Vi,RunnableLambda:()=>e8.jY,RunnableMap:()=>e8.ck,RunnableParallel:()=>e8.Pq,RunnablePassthrough:()=>tl,RunnablePick:()=>e8.Fm,RunnableRetry:()=>e8.AO,RunnableSequence:()=>e8.zZ,RunnableToolLike:()=>e8.pG,RunnableWithFallbacks:()=>e8.lH,RunnableWithMessageHistory:()=>tx,_coerceToRunnable:()=>e8.Bp,ensureConfig:()=>to.ZI,getCallbackManagerForConfig:()=>to.kJ,mergeConfigs:()=>to.SV,patchConfig:()=>to.tn,pickRunnableConfigKeys:()=>to.DY});var g={};r.r(g),r.d(g,{applyPatch:()=>tV.X6,compare:()=>tV.UD});var b={};r.r(b),r.d(b,{AsymmetricStructuredOutputParser:()=>tz,BaseCumulativeTransformOutputParser:()=>tI,BaseLLMOutputParser:()=>tC,BaseOutputParser:()=>tA,BaseTransformOutputParser:()=>tj,BytesOutputParser:()=>tN,CommaSeparatedListOutputParser:()=>tM,CustomListOutputParser:()=>tD,JsonMarkdownStructuredOutputParser:()=>tq,JsonOutputParser:()=>tB,ListOutputParser:()=>tR,MarkdownListOutputParser:()=>tL,NumberedListOutputParser:()=>t$,OutputParserException:()=>tT,StringOutputParser:()=>tU,StructuredOutputParser:()=>tF,XMLOutputParser:()=>tG,XML_FORMAT_INSTRUCTIONS:()=>tJ,parseJsonMarkdown:()=>tK.D,parsePartialJson:()=>tK.d,parseXMLMarkdown:()=>tX});var y={};r.r(y),r.d(y,{AIMessagePromptTemplate:()=>tQ.sS,BaseChatPromptTemplate:()=>tQ.qF,BaseMessagePromptTemplate:()=>tQ.pl,BaseMessageStringPromptTemplate:()=>tQ.OT,BasePromptTemplate:()=>tZ.m,BaseStringPromptTemplate:()=>t5.L,ChatMessagePromptTemplate:()=>tQ.Wn,ChatPromptTemplate:()=>tQ.RZ,DEFAULT_FORMATTER_MAPPING:()=>t3.ez,DEFAULT_PARSER_MAPPING:()=>t3.RG,DictPromptTemplate:()=>t9.l,FewShotChatMessagePromptTemplate:()=>t0.s,FewShotPromptTemplate:()=>t0.FewShotPromptTemplate,HumanMessagePromptTemplate:()=>tQ.FS,ImagePromptTemplate:()=>t4.C,MessagesPlaceholder:()=>tQ.GL,PipelinePromptTemplate:()=>t1,PromptTemplate:()=>t2.PromptTemplate,StructuredPrompt:()=>t6,SystemMessagePromptTemplate:()=>tQ.BJ,checkValidTemplate:()=>t3.Ns,interpolateFString:()=>t3.Vt,interpolateMustache:()=>t3.Qs,parseFString:()=>t3.D4,parseMustache:()=>t3.g2,parseTemplate:()=>t3.QC,renderTemplate:()=>t3.Xm});var _={};r.r(_),r.d(_,{BaseRetriever:()=>t7});var w={};r.r(w),r.d(w,{BaseStore:()=>re,InMemoryStore:()=>rt});var v={};r.r(v),r.d(v,{BaseToolkit:()=>rd,DynamicStructuredTool:()=>rh,DynamicTool:()=>ru,StructuredTool:()=>rl,Tool:()=>rc,ToolInputParsingException:()=>ri.qe,isLangChainTool:()=>ro,isRunnableToolLike:()=>rs,isStructuredTool:()=>ra,isStructuredToolParams:()=>rn,tool:()=>rp});var k={};r.r(k),r.d(k,{LangChainTracerV1:()=>ry});var O={};r.r(O),r.d(O,{getTracingCallbackHandler:()=>r_,getTracingV2CallbackHandler:()=>rw});var x={};r.r(x),r.d(x,{RunCollectorCallbackHandler:()=>rk});var S={};r.r(S),r.d(S,{chunkArray:()=>rO});var C={};r.r(C),r.d(C,{convertToOpenAIFunction:()=>rx,convertToOpenAITool:()=>rS,isLangChainTool:()=>ro,isRunnableToolLike:()=>rs,isStructuredTool:()=>ra,isStructuredToolParams:()=>rn});var A={};r.r(A),r.d(A,{cosineSimilarity:()=>rj,euclideanDistance:()=>rN,innerProduct:()=>rI,matrixFunc:()=>rP,maximalMarginalRelevance:()=>rR,normalize:()=>rE});var T={};r.r(T),r.d(T,{SaveableVectorStore:()=>rL,VectorStore:()=>r$,VectorStoreRetriever:()=>rD});var P={};r.r(P),r.d(P,{FakeChatMessageHistory:()=>rJ,FakeChatModel:()=>rV,FakeEmbeddings:()=>rX,FakeLLM:()=>rq,FakeListChatMessageHistory:()=>rG,FakeListChatModel:()=>rW,FakeRetriever:()=>rB,FakeRunnable:()=>rF,FakeSplitIntoListParser:()=>rU,FakeStreamingChatModel:()=>rK,FakeStreamingLLM:()=>rz,FakeTool:()=>rH,FakeTracer:()=>rY,FakeVectorStore:()=>r0,SingleRunExtractor:()=>rQ,SyntheticEmbeddings:()=>rZ});var E={};r.r(E),r.d(E,{extendInteropZodObject:()=>tc.W0,getInteropZodDefaultGetter:()=>tc.RA,getInteropZodObjectShape:()=>tc.PA,getSchemaDescription:()=>tc.cg,interopParse:()=>tc.Zu,interopParseAsync:()=>tc.hZ,interopSafeParse:()=>tc.Yi,interopSafeParseAsync:()=>tc.K3,interopZodObjectPartial:()=>tc.BA,interopZodObjectPassthrough:()=>tc.qT,interopZodObjectStrict:()=>tc.pE,interopZodTransformInputSchema:()=>tc.EN,isInteropZodObject:()=>tc.QV,isInteropZodSchema:()=>tc.c9,isShapelessZodSchema:()=>tc.X2,isSimpleStringZodSchema:()=>tc.yQ,isZodArrayV4:()=>tc.zL,isZodObjectV3:()=>tc.IS,isZodObjectV4:()=>tc.gY,isZodSchema:()=>tc.lm,isZodSchemaV3:()=>tc.IU,isZodSchemaV4:()=>tc.Pq});var j={};r.r(j),r.d(j,{agents:()=>s,caches:()=>o,callbacks__base:()=>eL,callbacks__manager:()=>eU,callbacks__promises:()=>eF,chat_history:()=>l,documents:()=>eB,embeddings:()=>c,example_selectors:()=>u,language_models__base:()=>h,language_models__chat_models:()=>d,language_models__llms:()=>p,load__serializable:()=>em,memory:()=>m,messages:()=>eq,output_parsers:()=>b,outputs:()=>ts,prompt_values:()=>e3,prompts:()=>y,retrievers:()=>_,runnables:()=>f,stores:()=>w,tools:()=>v,tracers__base:()=>rm,tracers__console:()=>rf,tracers__initialize:()=>O,tracers__log_stream:()=>rv,tracers__run_collector:()=>x,tracers__tracer_langchain:()=>rg,tracers__tracer_langchain_v1:()=>k,utils__async_caller:()=>eW,utils__chunk_array:()=>S,utils__env:()=>rb,utils__function_calling:()=>C,utils__hash:()=>n,utils__json_patch:()=>g,utils__json_schema:()=>tu,utils__math:()=>A,utils__stream:()=>tn,utils__testing:()=>P,utils__tiktoken:()=>e4,utils__types:()=>E,vectorstores:()=>T});var I={};r.r(I),r.d(I,{OPTIONS:()=>nY,POST:()=>nG});var N=r(96559),R=r(48088),M=r(37719),D=r(32190),$=r(2507),L=r(39398),U=r(56534),F=r(507),q=r(45697);let z={defaultKeySuccess:(e=1)=>1===e?"default_key_success":`default_key_success_attempt_${e}`,allKeysFailed:e=>`default_all_${e}_attempts_failed`,roleRouting:e=>e,intelligentRoleRouting:e=>`intelligent_role_${e}`,fallbackRouting:e=>`fallback_position_${e}`};var V=r(22123);let K=require("node:async_hooks");class B extends Error{constructor(e,t){let r=e??"";t?.lc_error_code&&(r=`${r}

Troubleshooting URL: https://langchain-ai.github.io/langgraphjs/troubleshooting/errors/${t.lc_error_code}/
`),super(r),Object.defineProperty(this,"lc_error_code",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.lc_error_code=t?.lc_error_code}}class W extends B{get is_bubble_up(){return!0}}class J extends B{constructor(e,t){super(e,t),this.name="GraphRecursionError"}static get unminifiable_name(){return"GraphRecursionError"}}class G extends B{constructor(e,t){super(e,t),this.name="GraphValueError"}static get unminifiable_name(){return"GraphValueError"}}class Y extends W{constructor(e,t){super(JSON.stringify(e,null,2),t),Object.defineProperty(this,"interrupts",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.name="GraphInterrupt",this.interrupts=e??[]}static get unminifiable_name(){return"GraphInterrupt"}}class H extends Y{constructor(e,t){super([{value:e,when:"during"}],t),this.name="NodeInterrupt"}static get unminifiable_name(){return"NodeInterrupt"}}class X extends W{constructor(e){super(),Object.defineProperty(this,"command",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.name="ParentCommand",this.command=e}static get unminifiable_name(){return"ParentCommand"}}function Z(e){return void 0!==e&&!0===e.is_bubble_up}function Q(e){return void 0!==e&&[Y.unminifiable_name,H.unminifiable_name].includes(e.name)}class ee extends B{constructor(e,t){super(e,t),this.name="EmptyInputError"}static get unminifiable_name(){return"EmptyInputError"}}class et extends B{constructor(e,t){super(e,t),this.name="EmptyChannelError"}static get unminifiable_name(){return"EmptyChannelError"}}class er extends B{constructor(e,t){super(e,t),this.name="InvalidUpdateError"}static get unminifiable_name(){return"InvalidUpdateError"}}class ei extends B{constructor(e,t){super(e,t),this.name="UnreachableNodeError"}static get unminifiable_name(){return"UnreachableNodeError"}}var ea=r(89386),es=r(5286);let en=0,eo=0,el=function(e,t,r){let s=t&&r||0,n=t||Array(16),o=(e=e||{}).node,l=e.clockseq;if(e._v6||(o||(o=i),null==l&&(l=a)),null==o||null==l){let t=e.random||(e.rng||es.A)();null==o&&(o=[t[0],t[1],t[2],t[3],t[4],t[5]],i||e._v6||(o[0]|=1,i=o)),null==l&&(l=(t[6]<<8|t[7])&16383,void 0!==a||e._v6||(a=l))}let c=void 0!==e.msecs?e.msecs:Date.now(),u=void 0!==e.nsecs?e.nsecs:eo+1,h=c-en+(u-eo)/1e4;if(h<0&&void 0===e.clockseq&&(l=l+1&16383),(h<0||c>en)&&void 0===e.nsecs&&(u=0),u>=1e4)throw Error("uuid.v1(): Can't create more than 10M uuids/sec");en=c,eo=u,a=l;let d=((0xfffffff&(c+=122192928e5))*1e4+u)%0x100000000;n[s++]=d>>>24&255,n[s++]=d>>>16&255,n[s++]=d>>>8&255,n[s++]=255&d;let p=c/0x100000000*1e4&0xfffffff;n[s++]=p>>>8&255,n[s++]=255&p,n[s++]=p>>>24&15|16,n[s++]=p>>>16&255,n[s++]=l>>>8|128,n[s++]=255&l;for(let e=0;e<6;++e)n[s+e]=o[e];return t||(0,ea.k)(n)};var ec=r(89136),eu=r(98431);function eh(e){return function(e={},t,r=0){let i=el({...e,_v6:!0},new Uint8Array(16));return i=function(e){let t=function(e,t=!1){return Uint8Array.of((15&e[6])<<4|e[7]>>4&15,(15&e[7])<<4|(240&e[4])>>4,(15&e[4])<<4|(240&e[5])>>4,(15&e[5])<<4|(240&e[0])>>4,(15&e[0])<<4|(240&e[1])>>4,(15&e[1])<<4|(240&e[2])>>4,96|15&e[2],e[3],e[8],e[9],e[10],e[11],e[12],e[13],e[14],e[15])}("string"==typeof e?(0,ec.A)(e):e);return"string"==typeof e?(0,ea.k)(t):t}(i),(0,ea.k)(i)}({clockseq:e})}function ed(e,t){let r=t.replace(/-/g,"").match(/.{2}/g).map(e=>parseInt(e,16));return(0,eu.A)(e,new Uint8Array(r))}let ep="__scheduled__";var em=r(84902);let ef=[];var eg="object"==typeof window?window:{},eb="0123456789abcdef".split(""),ey=[-0x80000000,8388608,32768,128],e_=[24,16,8,0],ew=[];function ev(e){e?(ew[0]=ew[16]=ew[1]=ew[2]=ew[3]=ew[4]=ew[5]=ew[6]=ew[7]=ew[8]=ew[9]=ew[10]=ew[11]=ew[12]=ew[13]=ew[14]=ew[15]=0,this.blocks=ew):this.blocks=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],this.h0=0x67452301,this.h1=0xefcdab89,this.h2=0x98badcfe,this.h3=0x10325476,this.h4=0xc3d2e1f0,this.block=this.start=this.bytes=this.hBytes=0,this.finalized=this.hashed=!1,this.first=!0}ev.prototype.update=function(e){if(!this.finalized){var t="string"!=typeof e;t&&e.constructor===eg.ArrayBuffer&&(e=new Uint8Array(e));for(var r,i,a=0,s=e.length||0,n=this.blocks;a<s;){if(this.hashed&&(this.hashed=!1,n[0]=this.block,n[16]=n[1]=n[2]=n[3]=n[4]=n[5]=n[6]=n[7]=n[8]=n[9]=n[10]=n[11]=n[12]=n[13]=n[14]=n[15]=0),t)for(i=this.start;a<s&&i<64;++a)n[i>>2]|=e[a]<<e_[3&i++];else for(i=this.start;a<s&&i<64;++a)(r=e.charCodeAt(a))<128?n[i>>2]|=r<<e_[3&i++]:(r<2048?n[i>>2]|=(192|r>>6)<<e_[3&i++]:(r<55296||r>=57344?n[i>>2]|=(224|r>>12)<<e_[3&i++]:(r=65536+((1023&r)<<10|1023&e.charCodeAt(++a)),n[i>>2]|=(240|r>>18)<<e_[3&i++],n[i>>2]|=(128|r>>12&63)<<e_[3&i++]),n[i>>2]|=(128|r>>6&63)<<e_[3&i++]),n[i>>2]|=(128|63&r)<<e_[3&i++]);this.lastByteIndex=i,this.bytes+=i-this.start,i>=64?(this.block=n[16],this.start=i-64,this.hash(),this.hashed=!0):this.start=i}return this.bytes>0xffffffff&&(this.hBytes+=this.bytes/0x100000000|0,this.bytes=this.bytes%0x100000000),this}},ev.prototype.finalize=function(){if(!this.finalized){this.finalized=!0;var e=this.blocks,t=this.lastByteIndex;e[16]=this.block,e[t>>2]|=ey[3&t],this.block=e[16],t>=56&&(this.hashed||this.hash(),e[0]=this.block,e[16]=e[1]=e[2]=e[3]=e[4]=e[5]=e[6]=e[7]=e[8]=e[9]=e[10]=e[11]=e[12]=e[13]=e[14]=e[15]=0),e[14]=this.hBytes<<3|this.bytes>>>29,e[15]=this.bytes<<3,this.hash()}},ev.prototype.hash=function(){var e,t,r,i=this.h0,a=this.h1,s=this.h2,n=this.h3,o=this.h4,l=this.blocks;for(t=16;t<80;++t)r=l[t-3]^l[t-8]^l[t-14]^l[t-16],l[t]=r<<1|r>>>31;for(t=0;t<20;t+=5)e=a&s|~a&n,o=(r=i<<5|i>>>27)+e+o+0x5a827999+l[t]|0,e=i&(a=a<<30|a>>>2)|~i&s,n=(r=o<<5|o>>>27)+e+n+0x5a827999+l[t+1]|0,e=o&(i=i<<30|i>>>2)|~o&a,s=(r=n<<5|n>>>27)+e+s+0x5a827999+l[t+2]|0,e=n&(o=o<<30|o>>>2)|~n&i,a=(r=s<<5|s>>>27)+e+a+0x5a827999+l[t+3]|0,e=s&(n=n<<30|n>>>2)|~s&o,i=(r=a<<5|a>>>27)+e+i+0x5a827999+l[t+4]|0,s=s<<30|s>>>2;for(;t<40;t+=5)e=a^s^n,o=(r=i<<5|i>>>27)+e+o+0x6ed9eba1+l[t]|0,e=i^(a=a<<30|a>>>2)^s,n=(r=o<<5|o>>>27)+e+n+0x6ed9eba1+l[t+1]|0,e=o^(i=i<<30|i>>>2)^a,s=(r=n<<5|n>>>27)+e+s+0x6ed9eba1+l[t+2]|0,e=n^(o=o<<30|o>>>2)^i,a=(r=s<<5|s>>>27)+e+a+0x6ed9eba1+l[t+3]|0,e=s^(n=n<<30|n>>>2)^o,i=(r=a<<5|a>>>27)+e+i+0x6ed9eba1+l[t+4]|0,s=s<<30|s>>>2;for(;t<60;t+=5)e=a&s|a&n|s&n,o=(r=i<<5|i>>>27)+e+o-0x70e44324+l[t]|0,e=i&(a=a<<30|a>>>2)|i&s|a&s,n=(r=o<<5|o>>>27)+e+n-0x70e44324+l[t+1]|0,e=o&(i=i<<30|i>>>2)|o&a|i&a,s=(r=n<<5|n>>>27)+e+s-0x70e44324+l[t+2]|0,e=n&(o=o<<30|o>>>2)|n&i|o&i,a=(r=s<<5|s>>>27)+e+a-0x70e44324+l[t+3]|0,e=s&(n=n<<30|n>>>2)|s&o|n&o,i=(r=a<<5|a>>>27)+e+i-0x70e44324+l[t+4]|0,s=s<<30|s>>>2;for(;t<80;t+=5)e=a^s^n,o=(r=i<<5|i>>>27)+e+o-0x359d3e2a+l[t]|0,e=i^(a=a<<30|a>>>2)^s,n=(r=o<<5|o>>>27)+e+n-0x359d3e2a+l[t+1]|0,e=o^(i=i<<30|i>>>2)^a,s=(r=n<<5|n>>>27)+e+s-0x359d3e2a+l[t+2]|0,e=n^(o=o<<30|o>>>2)^i,a=(r=s<<5|s>>>27)+e+a-0x359d3e2a+l[t+3]|0,e=s^(n=n<<30|n>>>2)^o,i=(r=a<<5|a>>>27)+e+i-0x359d3e2a+l[t+4]|0,s=s<<30|s>>>2;this.h0=this.h0+i|0,this.h1=this.h1+a|0,this.h2=this.h2+s|0,this.h3=this.h3+n|0,this.h4=this.h4+o|0},ev.prototype.hex=function(){this.finalize();var e=this.h0,t=this.h1,r=this.h2,i=this.h3,a=this.h4;return eb[e>>28&15]+eb[e>>24&15]+eb[e>>20&15]+eb[e>>16&15]+eb[e>>12&15]+eb[e>>8&15]+eb[e>>4&15]+eb[15&e]+eb[t>>28&15]+eb[t>>24&15]+eb[t>>20&15]+eb[t>>16&15]+eb[t>>12&15]+eb[t>>8&15]+eb[t>>4&15]+eb[15&t]+eb[r>>28&15]+eb[r>>24&15]+eb[r>>20&15]+eb[r>>16&15]+eb[r>>12&15]+eb[r>>8&15]+eb[r>>4&15]+eb[15&r]+eb[i>>28&15]+eb[i>>24&15]+eb[i>>20&15]+eb[i>>16&15]+eb[i>>12&15]+eb[i>>8&15]+eb[i>>4&15]+eb[15&i]+eb[a>>28&15]+eb[a>>24&15]+eb[a>>20&15]+eb[a>>16&15]+eb[a>>12&15]+eb[a>>8&15]+eb[a>>4&15]+eb[15&a]},ev.prototype.toString=ev.prototype.hex,ev.prototype.digest=function(){this.finalize();var e=this.h0,t=this.h1,r=this.h2,i=this.h3,a=this.h4;return[e>>24&255,e>>16&255,e>>8&255,255&e,t>>24&255,t>>16&255,t>>8&255,255&t,r>>24&255,r>>16&255,r>>8&255,255&r,i>>24&255,i>>16&255,i>>8&255,255&i,a>>24&255,a>>16&255,a>>8&255,255&a]},ev.prototype.array=ev.prototype.digest,ev.prototype.arrayBuffer=function(){this.finalize();var e=new ArrayBuffer(20),t=new DataView(e);return t.setUint32(0,this.h0),t.setUint32(4,this.h1),t.setUint32(8,this.h2),t.setUint32(12,this.h3),t.setUint32(16,this.h4),e};let ek=!1,eO=e=>(ek||(console.warn(`The default method for hashing keys is insecure and will be replaced in a future version,
but hasn't been replaced yet as to not break existing caches. It's recommended that you use
a more secure hashing algorithm to avoid cache poisoning.

See this page for more information:
|
└> https://js.langchain.com/docs/troubleshooting/warnings/insecure-cache-algorithm`),ek=!0),new ev(!0).update(e).hex());var ex="0123456789abcdef".split(""),eS=[-0x80000000,8388608,32768,128],eC=[24,16,8,0],eA=[0x428a2f98,0x71374491,0xb5c0fbcf,0xe9b5dba5,0x3956c25b,0x59f111f1,0x923f82a4,0xab1c5ed5,0xd807aa98,0x12835b01,0x243185be,0x550c7dc3,0x72be5d74,0x80deb1fe,0x9bdc06a7,0xc19bf174,0xe49b69c1,0xefbe4786,0xfc19dc6,0x240ca1cc,0x2de92c6f,0x4a7484aa,0x5cb0a9dc,0x76f988da,0x983e5152,0xa831c66d,0xb00327c8,0xbf597fc7,0xc6e00bf3,0xd5a79147,0x6ca6351,0x14292967,0x27b70a85,0x2e1b2138,0x4d2c6dfc,0x53380d13,0x650a7354,0x766a0abb,0x81c2c92e,0x92722c85,0xa2bfe8a1,0xa81a664b,0xc24b8b70,0xc76c51a3,0xd192e819,0xd6990624,0xf40e3585,0x106aa070,0x19a4c116,0x1e376c08,0x2748774c,0x34b0bcb5,0x391c0cb3,0x4ed8aa4a,0x5b9cca4f,0x682e6ff3,0x748f82ee,0x78a5636f,0x84c87814,0x8cc70208,0x90befffa,0xa4506ceb,0xbef9a3f7,0xc67178f2],eT=[];function eP(e,t){t?(eT[0]=eT[16]=eT[1]=eT[2]=eT[3]=eT[4]=eT[5]=eT[6]=eT[7]=eT[8]=eT[9]=eT[10]=eT[11]=eT[12]=eT[13]=eT[14]=eT[15]=0,this.blocks=eT):this.blocks=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0],e?(this.h0=0xc1059ed8,this.h1=0x367cd507,this.h2=0x3070dd17,this.h3=0xf70e5939,this.h4=0xffc00b31,this.h5=0x68581511,this.h6=0x64f98fa7,this.h7=0xbefa4fa4):(this.h0=0x6a09e667,this.h1=0xbb67ae85,this.h2=0x3c6ef372,this.h3=0xa54ff53a,this.h4=0x510e527f,this.h5=0x9b05688c,this.h6=0x1f83d9ab,this.h7=0x5be0cd19),this.block=this.start=this.bytes=this.hBytes=0,this.finalized=this.hashed=!1,this.first=!0,this.is224=e}eP.prototype.update=function(e){if(!this.finalized){var t,r=typeof e;if("string"!==r){if("object"===r){if(null===e)throw Error(ERROR);else if(ARRAY_BUFFER&&e.constructor===ArrayBuffer)e=new Uint8Array(e);else if(!Array.isArray(e)&&(!ARRAY_BUFFER||!ArrayBuffer.isView(e)))throw Error(ERROR)}else throw Error(ERROR);t=!0}for(var i,a,s=0,n=e.length,o=this.blocks;s<n;){if(this.hashed&&(this.hashed=!1,o[0]=this.block,this.block=o[16]=o[1]=o[2]=o[3]=o[4]=o[5]=o[6]=o[7]=o[8]=o[9]=o[10]=o[11]=o[12]=o[13]=o[14]=o[15]=0),t)for(a=this.start;s<n&&a<64;++s)o[a>>>2]|=e[s]<<eC[3&a++];else for(a=this.start;s<n&&a<64;++s)(i=e.charCodeAt(s))<128?o[a>>>2]|=i<<eC[3&a++]:(i<2048?o[a>>>2]|=(192|i>>>6)<<eC[3&a++]:(i<55296||i>=57344?o[a>>>2]|=(224|i>>>12)<<eC[3&a++]:(i=65536+((1023&i)<<10|1023&e.charCodeAt(++s)),o[a>>>2]|=(240|i>>>18)<<eC[3&a++],o[a>>>2]|=(128|i>>>12&63)<<eC[3&a++]),o[a>>>2]|=(128|i>>>6&63)<<eC[3&a++]),o[a>>>2]|=(128|63&i)<<eC[3&a++]);this.lastByteIndex=a,this.bytes+=a-this.start,a>=64?(this.block=o[16],this.start=a-64,this.hash(),this.hashed=!0):this.start=a}return this.bytes>0xffffffff&&(this.hBytes+=this.bytes/0x100000000|0,this.bytes=this.bytes%0x100000000),this}},eP.prototype.finalize=function(){if(!this.finalized){this.finalized=!0;var e=this.blocks,t=this.lastByteIndex;e[16]=this.block,e[t>>>2]|=eS[3&t],this.block=e[16],t>=56&&(this.hashed||this.hash(),e[0]=this.block,e[16]=e[1]=e[2]=e[3]=e[4]=e[5]=e[6]=e[7]=e[8]=e[9]=e[10]=e[11]=e[12]=e[13]=e[14]=e[15]=0),e[14]=this.hBytes<<3|this.bytes>>>29,e[15]=this.bytes<<3,this.hash()}},eP.prototype.hash=function(){var e,t,r,i,a,s,n,o,l,c,u,h=this.h0,d=this.h1,p=this.h2,m=this.h3,f=this.h4,g=this.h5,b=this.h6,y=this.h7,_=this.blocks;for(e=16;e<64;++e)t=((a=_[e-15])>>>7|a<<25)^(a>>>18|a<<14)^a>>>3,r=((a=_[e-2])>>>17|a<<15)^(a>>>19|a<<13)^a>>>10,_[e]=_[e-16]+t+_[e-7]+r|0;for(e=0,u=d&p;e<64;e+=4)this.first?(this.is224?(o=300032,y=(a=_[0]-0x543c9a5b)-0x8f1a6c7|0,m=a+0x170e9b5|0):(o=0x2a01a605,y=(a=_[0]-0xc881298)-0x5ab00ac6|0,m=a+0x8909ae5|0),this.first=!1):(t=(h>>>2|h<<30)^(h>>>13|h<<19)^(h>>>22|h<<10),r=(f>>>6|f<<26)^(f>>>11|f<<21)^(f>>>25|f<<7),i=(o=h&d)^h&p^u,a=y+r+(f&g^~f&b)+eA[e]+_[e],s=t+i,y=m+a|0,m=a+s|0),t=(m>>>2|m<<30)^(m>>>13|m<<19)^(m>>>22|m<<10),r=(y>>>6|y<<26)^(y>>>11|y<<21)^(y>>>25|y<<7),i=(l=m&h)^m&d^o,a=g+r+(b&y^~b&f)+eA[e+1]+_[e+1],s=t+i,b=p+a|0,t=((p=a+s|0)>>>2|p<<30)^(p>>>13|p<<19)^(p>>>22|p<<10),r=(b>>>6|b<<26)^(b>>>11|b<<21)^(b>>>25|b<<7),i=(c=p&m)^p&h^l,a=f+r+(g&b^~g&y)+eA[e+2]+_[e+2],s=t+i,g=d+a|0,t=((d=a+s|0)>>>2|d<<30)^(d>>>13|d<<19)^(d>>>22|d<<10),r=(g>>>6|g<<26)^(g>>>11|g<<21)^(g>>>25|g<<7),i=(u=d&p)^d&m^c,a=f+r+(g&b^~g&y)+eA[e+3]+_[e+3],s=t+i,f=h+a|0,h=a+s|0,this.chromeBugWorkAround=!0;this.h0=this.h0+h|0,this.h1=this.h1+d|0,this.h2=this.h2+p|0,this.h3=this.h3+m|0,this.h4=this.h4+f|0,this.h5=this.h5+g|0,this.h6=this.h6+b|0,this.h7=this.h7+y|0},eP.prototype.hex=function(){this.finalize();var e=this.h0,t=this.h1,r=this.h2,i=this.h3,a=this.h4,s=this.h5,n=this.h6,o=this.h7,l=ex[e>>>28&15]+ex[e>>>24&15]+ex[e>>>20&15]+ex[e>>>16&15]+ex[e>>>12&15]+ex[e>>>8&15]+ex[e>>>4&15]+ex[15&e]+ex[t>>>28&15]+ex[t>>>24&15]+ex[t>>>20&15]+ex[t>>>16&15]+ex[t>>>12&15]+ex[t>>>8&15]+ex[t>>>4&15]+ex[15&t]+ex[r>>>28&15]+ex[r>>>24&15]+ex[r>>>20&15]+ex[r>>>16&15]+ex[r>>>12&15]+ex[r>>>8&15]+ex[r>>>4&15]+ex[15&r]+ex[i>>>28&15]+ex[i>>>24&15]+ex[i>>>20&15]+ex[i>>>16&15]+ex[i>>>12&15]+ex[i>>>8&15]+ex[i>>>4&15]+ex[15&i]+ex[a>>>28&15]+ex[a>>>24&15]+ex[a>>>20&15]+ex[a>>>16&15]+ex[a>>>12&15]+ex[a>>>8&15]+ex[a>>>4&15]+ex[15&a]+ex[s>>>28&15]+ex[s>>>24&15]+ex[s>>>20&15]+ex[s>>>16&15]+ex[s>>>12&15]+ex[s>>>8&15]+ex[s>>>4&15]+ex[15&s]+ex[n>>>28&15]+ex[n>>>24&15]+ex[n>>>20&15]+ex[n>>>16&15]+ex[n>>>12&15]+ex[n>>>8&15]+ex[n>>>4&15]+ex[15&n];return this.is224||(l+=ex[o>>>28&15]+ex[o>>>24&15]+ex[o>>>20&15]+ex[o>>>16&15]+ex[o>>>12&15]+ex[o>>>8&15]+ex[o>>>4&15]+ex[15&o]),l},eP.prototype.toString=eP.prototype.hex,eP.prototype.digest=function(){this.finalize();var e=this.h0,t=this.h1,r=this.h2,i=this.h3,a=this.h4,s=this.h5,n=this.h6,o=this.h7,l=[e>>>24&255,e>>>16&255,e>>>8&255,255&e,t>>>24&255,t>>>16&255,t>>>8&255,255&t,r>>>24&255,r>>>16&255,r>>>8&255,255&r,i>>>24&255,i>>>16&255,i>>>8&255,255&i,a>>>24&255,a>>>16&255,a>>>8&255,255&a,s>>>24&255,s>>>16&255,s>>>8&255,255&s,n>>>24&255,n>>>16&255,n>>>8&255,255&n];return this.is224||l.push(o>>>24&255,o>>>16&255,o>>>8&255,255&o),l},eP.prototype.array=eP.prototype.digest,eP.prototype.arrayBuffer=function(){this.finalize();var e=new ArrayBuffer(this.is224?28:32),t=new DataView(e);return t.setUint32(0,this.h0),t.setUint32(4,this.h1),t.setUint32(8,this.h2),t.setUint32(12,this.h3),t.setUint32(16,this.h4),t.setUint32(20,this.h5),t.setUint32(24,this.h6),this.is224||t.setUint32(28,this.h7),e};let eE=(...e)=>new eP(!1,!0).update(e.join("")).hex();var ej=r(81412);let eI=(...e)=>eO(e.join("_"));function eN(e){return void 0!==e.message?{text:e.text,message:(0,ej.Pz)(e.message)}:{text:e.text}}function eR(e){let t={text:e.text};return void 0!==e.message&&(t.message=e.message.toDict()),t}class eM{constructor(){Object.defineProperty(this,"keyEncoder",{enumerable:!0,configurable:!0,writable:!0,value:eI})}makeDefaultKeyEncoder(e){this.keyEncoder=e}}let eD=new Map;class e$ extends eM{constructor(e){super(),Object.defineProperty(this,"cache",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.cache=e??new Map}lookup(e,t){return Promise.resolve(this.cache.get(this.keyEncoder(e,t))??null)}async update(e,t,r){this.cache.set(this.keyEncoder(e,t),r)}static global(){return new e$(eD)}}var eL=r(60662),eU=r(89706),eF=r(30413),eq=r(61751);class ez extends em.Serializable{async addMessages(e){for(let t of e)await this.addMessage(t)}}class eV extends em.Serializable{addUserMessage(e){return this.addMessage(new eq.HumanMessage(e))}addAIChatMessage(e){return this.addMessage(new eq.AIMessage(e))}addAIMessage(e){return this.addMessage(new eq.AIMessage(e))}async addMessages(e){for(let t of e)await this.addMessage(t)}clear(){throw Error("Not implemented.")}}class eK extends eV{constructor(e){super(...arguments),Object.defineProperty(this,"lc_namespace",{enumerable:!0,configurable:!0,writable:!0,value:["langchain","stores","message","in_memory"]}),Object.defineProperty(this,"messages",{enumerable:!0,configurable:!0,writable:!0,value:[]}),this.messages=e??[]}async getMessages(){return this.messages}async addMessage(e){this.messages.push(e)}async clear(){this.messages=[]}}var eB=r(76947),eW=r(15641);class eJ{constructor(e){Object.defineProperty(this,"caller",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.caller=new eW.AsyncCaller(e??{})}}class eG extends em.Serializable{constructor(){super(...arguments),Object.defineProperty(this,"lc_namespace",{enumerable:!0,configurable:!0,writable:!0,value:["langchain_core","example_selectors","base"]})}}class eY{async getPromptAsync(e,t){return this.getPrompt(e).partial(t?.partialVariables??{})}}class eH extends eY{constructor(e,t=[]){super(),Object.defineProperty(this,"defaultPrompt",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"conditionals",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.defaultPrompt=e,this.conditionals=t}getPrompt(e){for(let[t,r]of this.conditionals)if(t(e))return r;return this.defaultPrompt}}function eX(e){return"base_llm"===e._modelType()}function eZ(e){return"base_chat_model"===e._modelType()}function eQ(e){return e.split(/\n| /).length}class e0 extends eG{constructor(e){super(e),Object.defineProperty(this,"examples",{enumerable:!0,configurable:!0,writable:!0,value:[]}),Object.defineProperty(this,"examplePrompt",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"getTextLength",{enumerable:!0,configurable:!0,writable:!0,value:eQ}),Object.defineProperty(this,"maxLength",{enumerable:!0,configurable:!0,writable:!0,value:2048}),Object.defineProperty(this,"exampleTextLengths",{enumerable:!0,configurable:!0,writable:!0,value:[]}),this.examplePrompt=e.examplePrompt,this.maxLength=e.maxLength??2048,this.getTextLength=e.getTextLength??eQ}async addExample(e){this.examples.push(e);let t=await this.examplePrompt.format(e);this.exampleTextLengths.push(this.getTextLength(t))}async calculateExampleTextLengths(e,t){if(e.length>0)return e;let{examples:r,examplePrompt:i}=t;return(await Promise.all(r.map(e=>i.format(e)))).map(e=>this.getTextLength(e))}async selectExamples(e){let t=Object.values(e).join(" "),r=this.maxLength-this.getTextLength(t),i=0,a=[];for(;r>0&&i<this.examples.length;){let e=r-this.exampleTextLengths[i];if(e<0)break;a.push(this.examples[i]),r=e,i+=1}return a}static async fromExamples(e,t){let r=new e0(t);return await Promise.all(e.map(e=>r.addExample(e))),r}}var e1=r(47948);function e2(e){return Object.keys(e).sort().map(t=>e[t])}class e5 extends eG{constructor(e){if(super(e),Object.defineProperty(this,"vectorStoreRetriever",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"exampleKeys",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"inputKeys",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.exampleKeys=e.exampleKeys,this.inputKeys=e.inputKeys,void 0!==e.vectorStore)this.vectorStoreRetriever=e.vectorStore.asRetriever({k:e.k??4,filter:e.filter});else if(e.vectorStoreRetriever)this.vectorStoreRetriever=e.vectorStoreRetriever;else throw Error('You must specify one of "vectorStore" and "vectorStoreRetriever".')}async addExample(e){let t=e2((this.inputKeys??Object.keys(e)).reduce((t,r)=>({...t,[r]:e[r]}),{})).join(" ");await this.vectorStoreRetriever.addDocuments([new e1.y({pageContent:t,metadata:e})])}async selectExamples(e){let t=e2((this.inputKeys??Object.keys(e)).reduce((t,r)=>({...t,[r]:e[r]}),{})).join(" "),r=(await this.vectorStoreRetriever.invoke(t)).map(e=>e.metadata);return this.exampleKeys?r.map(e=>this.exampleKeys.reduce((t,r)=>({...t,[r]:e[r]}),{})):r}static async fromExamples(e,t,r,i={}){let a=i.inputKeys??null,s=e.map(e=>e2(a?a.reduce((t,r)=>({...t,[r]:e[r]}),{}):e).join(" "));return new e5({vectorStore:await r.fromTexts(s,e,t,i),k:i.k??4,exampleKeys:i.exampleKeys,inputKeys:i.inputKeys})}}var e3=r(62633),e4=r(88595),e8=r(90163);let e6=e=>e.startsWith("gpt-3.5-turbo-16k")?"gpt-3.5-turbo-16k":e.startsWith("gpt-3.5-turbo-")?"gpt-3.5-turbo":e.startsWith("gpt-4-32k")?"gpt-4-32k":e.startsWith("gpt-4-")?"gpt-4":e.startsWith("gpt-4o")?"gpt-4o":e,e9=e=>"text-embedding-ada-002"===e?8191:2046,e7=e=>{switch(e6(e)){case"gpt-3.5-turbo-16k":return 16384;case"gpt-3.5-turbo":return 4096;case"gpt-4-32k":return 32768;case"gpt-4":return 8192;case"text-davinci-003":default:return 4097;case"text-curie-001":case"text-babbage-001":case"text-ada-001":case"code-cushman-001":return 2048;case"code-davinci-002":return 8e3}};function te(e){return"object"==typeof e&&!!e&&("type"in e&&"function"===e.type&&"function"in e&&"object"==typeof e.function&&!!e.function&&"name"in e.function&&"parameters"in e.function||!1)}let tt=async({prompt:e,modelName:t})=>{let r;try{r=(await (0,e4.encodingForModel)(e6(t))).encode(e).length}catch(t){console.warn("Failed to calculate number of tokens, falling back to approximate count"),r=Math.ceil(e.length/4)}return e7(t)-r},tr=()=>!1;class ti extends e8.YN{get lc_attributes(){return{callbacks:void 0,verbose:void 0}}constructor(e){super(e),Object.defineProperty(this,"verbose",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"callbacks",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"tags",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"metadata",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.verbose=e.verbose??tr(),this.callbacks=e.callbacks,this.tags=e.tags??[],this.metadata=e.metadata??{}}}class ta extends ti{get callKeys(){return["stop","timeout","signal","tags","metadata","callbacks"]}constructor({callbacks:e,callbackManager:t,...r}){let{cache:i,...a}=r;super({callbacks:e??t,...a}),Object.defineProperty(this,"caller",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"cache",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"_encoding",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),"object"==typeof i?this.cache=i:i?this.cache=e$.global():this.cache=void 0,this.caller=new eW.AsyncCaller(r??{})}async getNumTokens(e){let t,r=Math.ceil((t="string"==typeof e?e:e.map(e=>"string"==typeof e?e:"text"===e.type&&"text"in e?e.text:"").join("")).length/4);if(!this._encoding)try{this._encoding=await (0,e4.encodingForModel)("modelName"in this?e6(this.modelName):"gpt2")}catch(e){console.warn("Failed to calculate number of tokens, falling back to approximate count",e)}if(this._encoding)try{r=this._encoding.encode(t).length}catch(e){console.warn("Failed to calculate number of tokens, falling back to approximate count",e)}return r}static _convertInputToPromptValue(e){return"string"==typeof e?new e3.StringPromptValue(e):Array.isArray(e)?new e3.ChatPromptValue(e.map(ej.K0)):e}_identifyingParams(){return{}}_getSerializedCacheKeyParametersForCall({config:e,...t}){return Object.entries({...this._identifyingParams(),...t,_type:this._llmType(),_model:this._modelType()}).filter(([e,t])=>void 0!==t).map(([e,t])=>`${e}:${JSON.stringify(t)}`).sort().join(",")}serialize(){return{...this._identifyingParams(),_type:this._llmType(),_model:this._modelType()}}static async deserialize(e){throw Error("Use .toJSON() instead")}}var ts=r(38022),tn=r(72180),to=r(27935);class tl extends e8.YN{static lc_name(){return"RunnablePassthrough"}constructor(e){super(e),Object.defineProperty(this,"lc_namespace",{enumerable:!0,configurable:!0,writable:!0,value:["langchain_core","runnables"]}),Object.defineProperty(this,"lc_serializable",{enumerable:!0,configurable:!0,writable:!0,value:!0}),Object.defineProperty(this,"func",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),e&&(this.func=e.func)}async invoke(e,t){let r=(0,to.ZI)(t);return this.func&&await this.func(e,r),this._callWithConfig(e=>Promise.resolve(e),e,r)}async *transform(e,t){let r,i=(0,to.ZI)(t),a=!0;for await(let t of this._transformStreamWithConfig(e,e=>e,i))if(yield t,a)if(void 0===r)r=t;else try{r=(0,tn.concat)(r,t)}catch{r=void 0,a=!1}this.func&&void 0!==r&&await this.func(r,i)}static assign(e){return new e8.B2(new e8.ck({steps:e}))}}var tc=r(60157),tu=r(21760);function th(){let e=new TextEncoder;return new TransformStream({transform(t,r){r.enqueue(e.encode("string"==typeof t.content?t.content:JSON.stringify(t.content)))}})}function td(e){let t=[];for(let r of e){let e=r;if(Array.isArray(r.content))for(let t=0;t<r.content.length;t++){let i=r.content[t];((0,eq.isURLContentBlock)(i)||(0,eq.isBase64ContentBlock)(i))&&e===r&&(e=new r.constructor({...e,content:[...r.content.slice(0,t),(0,eq.convertToOpenAIImageBlock)(i),...r.content.slice(t+1)]}))}t.push(e)}return t}class tp extends ta{constructor(e){super(e),Object.defineProperty(this,"lc_namespace",{enumerable:!0,configurable:!0,writable:!0,value:["langchain","chat_models",this._llmType()]}),Object.defineProperty(this,"disableStreaming",{enumerable:!0,configurable:!0,writable:!0,value:!1})}_separateRunnableConfigFromCallOptionsCompat(e){let[t,r]=super._separateRunnableConfigFromCallOptions(e);return r.signal=t.signal,[t,r]}async invoke(e,t){let r=tp._convertInputToPromptValue(e);return(await this.generatePrompt([r],t,t?.callbacks)).generations[0][0].message}async *_streamResponseChunks(e,t,r){throw Error("Not implemented.")}async *_streamIterator(e,t){if(this._streamResponseChunks===tp.prototype._streamResponseChunks||this.disableStreaming)yield this.invoke(e,t);else{let r,i,a=tp._convertInputToPromptValue(e).toChatMessages(),[s,n]=this._separateRunnableConfigFromCallOptionsCompat(t),o={...s.metadata,...this.getLsParams(n)},l=await eU.CallbackManager.configure(s.callbacks,this.callbacks,s.tags,this.tags,o,this.metadata,{verbose:this.verbose}),c={options:n,invocation_params:this?.invocationParams(n),batch_size:1},u=await l?.handleChatModelStart(this.toJSON(),[td(a)],s.runId,void 0,c,void 0,void 0,s.runName);try{for await(let e of this._streamResponseChunks(a,n,u?.[0])){if(null==e.message.id){let t=u?.at(0)?.runId;null!=t&&e.message._updateId(`run-${t}`)}e.message.response_metadata={...e.generationInfo,...e.message.response_metadata},yield e.message,r=r?r.concat(e):e,(0,eq.isAIMessageChunk)(e.message)&&void 0!==e.message.usage_metadata&&(i={tokenUsage:{promptTokens:e.message.usage_metadata.input_tokens,completionTokens:e.message.usage_metadata.output_tokens,totalTokens:e.message.usage_metadata.total_tokens}})}}catch(e){throw await Promise.all((u??[]).map(t=>t?.handleLLMError(e))),e}await Promise.all((u??[]).map(e=>e?.handleLLMEnd({generations:[[r]],llmOutput:i})))}}getLsParams(e){let t=this.getName().startsWith("Chat")?this.getName().replace("Chat",""):this.getName();return{ls_model_type:"chat",ls_stop:e.stop,ls_provider:t}}async _generateUncached(e,t,r,i){let a,s=e.map(e=>e.map(eq.coerceMessageLikeToMessage));if(void 0!==i&&i.length===s.length)a=i;else{let e={...r.metadata,...this.getLsParams(t)},i=await eU.CallbackManager.configure(r.callbacks,this.callbacks,r.tags,this.tags,e,this.metadata,{verbose:this.verbose}),n={options:t,invocation_params:this?.invocationParams(t),batch_size:1};a=await i?.handleChatModelStart(this.toJSON(),s.map(td),r.runId,void 0,n,void 0,void 0,r.runName)}let n=[],o=[];if(a?.[0].handlers.find(eL.callbackHandlerPrefersStreaming)&&!this.disableStreaming&&1===s.length&&this._streamResponseChunks!==tp.prototype._streamResponseChunks)try{let e,r;for await(let i of(await this._streamResponseChunks(s[0],t,a?.[0]))){if(null==i.message.id){let e=a?.at(0)?.runId;null!=e&&i.message._updateId(`run-${e}`)}e=void 0===e?i:(0,tn.concat)(e,i),(0,eq.isAIMessageChunk)(i.message)&&void 0!==i.message.usage_metadata&&(r={tokenUsage:{promptTokens:i.message.usage_metadata.input_tokens,completionTokens:i.message.usage_metadata.output_tokens,totalTokens:i.message.usage_metadata.total_tokens}})}if(void 0===e)throw Error("Received empty response from chat model call.");n.push([e]),await a?.[0].handleLLMEnd({generations:n,llmOutput:r})}catch(e){throw await a?.[0].handleLLMError(e),e}else{let e=await Promise.allSettled(s.map((e,r)=>this._generate(e,{...t,promptIndex:r},a?.[r])));await Promise.all(e.map(async(e,t)=>{if("fulfilled"!==e.status)return await a?.[t]?.handleLLMError(e.reason),Promise.reject(e.reason);{let r=e.value;for(let e of r.generations){if(null==e.message.id){let t=a?.at(0)?.runId;null!=t&&e.message._updateId(`run-${t}`)}e.message.response_metadata={...e.generationInfo,...e.message.response_metadata}}return 1===r.generations.length&&(r.generations[0].message.response_metadata={...r.llmOutput,...r.generations[0].message.response_metadata}),n[t]=r.generations,o[t]=r.llmOutput,a?.[t]?.handleLLMEnd({generations:[r.generations],llmOutput:r.llmOutput})}}))}let l={generations:n,llmOutput:o.length?this._combineLLMOutput?.(...o):void 0};return Object.defineProperty(l,ts.RUN_KEY,{value:a?{runIds:a?.map(e=>e.runId)}:void 0,configurable:!0}),l}async _generateCached({messages:e,cache:t,llmStringKey:r,parsedOptions:i,handledOptions:a}){let s=e.map(e=>e.map(eq.coerceMessageLikeToMessage)),n={...a.metadata,...this.getLsParams(i)},o=await eU.CallbackManager.configure(a.callbacks,this.callbacks,a.tags,this.tags,n,this.metadata,{verbose:this.verbose}),l={options:i,invocation_params:this?.invocationParams(i),batch_size:1},c=await o?.handleChatModelStart(this.toJSON(),s.map(td),a.runId,void 0,l,void 0,void 0,a.runName),u=[],h=(await Promise.allSettled(s.map(async(e,i)=>{let a=tp._convertInputToPromptValue(e).toString(),s=await t.lookup(a,r);return null==s&&u.push(i),s}))).map((e,t)=>({result:e,runManager:c?.[t]})).filter(({result:e})=>"fulfilled"===e.status&&null!=e.value||"rejected"===e.status),d=[];await Promise.all(h.map(async({result:e,runManager:t},r)=>{if("fulfilled"!==e.status)return await t?.handleLLMError(e.reason,void 0,void 0,void 0,{cached:!0}),Promise.reject(e.reason);{let i=e.value;return d[r]=i.map(e=>("message"in e&&(0,eq.isBaseMessage)(e.message)&&(0,eq.isAIMessage)(e.message)&&(e.message.usage_metadata={input_tokens:0,output_tokens:0,total_tokens:0}),e.generationInfo={...e.generationInfo,tokenUsage:{}},e)),i.length&&await t?.handleLLMNewToken(i[0].text),t?.handleLLMEnd({generations:[i]},void 0,void 0,void 0,{cached:!0})}}));let p={generations:d,missingPromptIndices:u,startedRunManagers:c};return Object.defineProperty(p,ts.RUN_KEY,{value:c?{runIds:c?.map(e=>e.runId)}:void 0,configurable:!0}),p}async generate(e,t,r){let i;i=Array.isArray(t)?{stop:t}:t;let a=e.map(e=>e.map(eq.coerceMessageLikeToMessage)),[s,n]=this._separateRunnableConfigFromCallOptionsCompat(i);if(s.callbacks=s.callbacks??r,!this.cache)return this._generateUncached(a,n,s);let{cache:o}=this,l=this._getSerializedCacheKeyParametersForCall(n),{generations:c,missingPromptIndices:u,startedRunManagers:h}=await this._generateCached({messages:a,cache:o,llmStringKey:l,parsedOptions:n,handledOptions:s}),d={};if(u.length>0){let e=await this._generateUncached(u.map(e=>a[e]),n,s,void 0!==h?u.map(e=>h?.[e]):void 0);await Promise.all(e.generations.map(async(e,t)=>{let r=u[t];c[r]=e;let i=tp._convertInputToPromptValue(a[r]).toString();return o.update(i,l,e)})),d=e.llmOutput??{}}return{generations:c,llmOutput:d}}invocationParams(e){return{}}_modelType(){return"base_chat_model"}serialize(){return{...this.invocationParams(),_type:this._llmType(),_model:this._modelType()}}async generatePrompt(e,t,r){let i=e.map(e=>e.toChatMessages());return this.generate(i,t,r)}async call(e,t,r){return(await this.generate([e.map(eq.coerceMessageLikeToMessage)],t,r)).generations[0][0].message}async callPrompt(e,t,r){let i=e.toChatMessages();return this.call(i,t,r)}async predictMessages(e,t,r){return this.call(e,t,r)}async predict(e,t,r){let i=new eq.HumanMessage(e),a=await this.call([i],t,r);if("string"!=typeof a.content)throw Error("Cannot use predict when output is not a string.");return a.content}withStructuredOutput(e,t){let r;if("function"!=typeof this.bindTools)throw Error('Chat model must implement ".bindTools()" to use withStructuredOutput.');if(t?.strict)throw Error('"strict" mode is not supported for this model by default.');let i=t?.name,a=(0,tc.cg)(e)??"A function available to call.",s=t?.method,n=t?.includeRaw;if("jsonMode"===s)throw Error('Base withStructuredOutput implementation only supports "functionCalling" as a method.');let o=i??"extract";(0,tc.c9)(e)?r=[{type:"function",function:{name:o,description:a,parameters:(0,tu.toJsonSchema)(e)}}]:("name"in e&&(o=e.name),r=[{type:"function",function:{name:o,description:a,parameters:e}}]);let l=this.bindTools(r),c=e8.jY.from(e=>{if(!e.tool_calls||0===e.tool_calls.length)throw Error("No tool calls found in the response.");let t=e.tool_calls.find(e=>e.name===o);if(!t)throw Error(`No tool call found with name ${o}.`);return t.args});if(!n)return l.pipe(c).withConfig({runName:"StructuredOutput"});let u=tl.assign({parsed:(e,t)=>c.invoke(e.raw,t)}),h=tl.assign({parsed:()=>null}),d=u.withFallbacks({fallbacks:[h]});return e8.zZ.from([{raw:l},d]).withConfig({runName:"StructuredOutputRunnable"})}}class tm extends tp{async _generate(e,t,r){let i=await this._call(e,t,r),a=new eq.AIMessage(i);if("string"!=typeof a.content)throw Error("Cannot generate with a simple chat model when output is not a string.");return{generations:[{text:a.content,message:a}]}}}class tf extends ta{constructor({concurrency:e,...t}){super(e?{maxConcurrency:e,...t}:t),Object.defineProperty(this,"lc_namespace",{enumerable:!0,configurable:!0,writable:!0,value:["langchain","llms",this._llmType()]})}async invoke(e,t){let r=tf._convertInputToPromptValue(e);return(await this.generatePrompt([r],t,t?.callbacks)).generations[0][0].text}async *_streamResponseChunks(e,t,r){throw Error("Not implemented.")}_separateRunnableConfigFromCallOptionsCompat(e){let[t,r]=super._separateRunnableConfigFromCallOptions(e);return r.signal=t.signal,[t,r]}async *_streamIterator(e,t){if(this._streamResponseChunks===tf.prototype._streamResponseChunks)yield this.invoke(e,t);else{let r=tf._convertInputToPromptValue(e),[i,a]=this._separateRunnableConfigFromCallOptionsCompat(t),s=await eU.CallbackManager.configure(i.callbacks,this.callbacks,i.tags,this.tags,i.metadata,this.metadata,{verbose:this.verbose}),n={options:a,invocation_params:this?.invocationParams(a),batch_size:1},o=await s?.handleLLMStart(this.toJSON(),[r.toString()],i.runId,void 0,n,void 0,void 0,i.runName),l=new ts.GenerationChunk({text:""});try{for await(let e of this._streamResponseChunks(r.toString(),a,o?.[0]))l=l?l.concat(e):e,"string"==typeof e.text&&(yield e.text)}catch(e){throw await Promise.all((o??[]).map(t=>t?.handleLLMError(e))),e}await Promise.all((o??[]).map(e=>e?.handleLLMEnd({generations:[[l]]})))}}async generatePrompt(e,t,r){let i=e.map(e=>e.toString());return this.generate(i,t,r)}invocationParams(e){return{}}_flattenLLMResult(e){let t=[];for(let r=0;r<e.generations.length;r+=1){let i=e.generations[r];if(0===r)t.push({generations:[i],llmOutput:e.llmOutput});else{let r=e.llmOutput?{...e.llmOutput,tokenUsage:{}}:void 0;t.push({generations:[i],llmOutput:r})}}return t}async _generateUncached(e,t,r,i){let a,s;if(void 0!==i&&i.length===e.length)a=i;else{let i=await eU.CallbackManager.configure(r.callbacks,this.callbacks,r.tags,this.tags,r.metadata,this.metadata,{verbose:this.verbose}),s={options:t,invocation_params:this?.invocationParams(t),batch_size:e.length};a=await i?.handleLLMStart(this.toJSON(),e,r.runId,void 0,s,void 0,void 0,r?.runName)}if(a?.[0].handlers.find(eL.callbackHandlerPrefersStreaming)&&1===e.length&&this._streamResponseChunks!==tf.prototype._streamResponseChunks)try{let r;for await(let i of(await this._streamResponseChunks(e[0],t,a?.[0])))r=void 0===r?i:(0,tn.concat)(r,i);if(void 0===r)throw Error("Received empty response from chat model call.");s={generations:[[r]],llmOutput:{}},await a?.[0].handleLLMEnd(s)}catch(e){throw await a?.[0].handleLLMError(e),e}else{try{s=await this._generate(e,t,a?.[0])}catch(e){throw await Promise.all((a??[]).map(t=>t?.handleLLMError(e))),e}let r=this._flattenLLMResult(s);await Promise.all((a??[]).map((e,t)=>e?.handleLLMEnd(r[t])))}let n=a?.map(e=>e.runId)||void 0;return Object.defineProperty(s,ts.RUN_KEY,{value:n?{runIds:n}:void 0,configurable:!0}),s}async _generateCached({prompts:e,cache:t,llmStringKey:r,parsedOptions:i,handledOptions:a,runId:s}){let n=await eU.CallbackManager.configure(a.callbacks,this.callbacks,a.tags,this.tags,a.metadata,this.metadata,{verbose:this.verbose}),o={options:i,invocation_params:this?.invocationParams(i),batch_size:e.length},l=await n?.handleLLMStart(this.toJSON(),e,s,void 0,o,void 0,void 0,a?.runName),c=[],u=(await Promise.allSettled(e.map(async(e,i)=>{let a=await t.lookup(e,r);return null==a&&c.push(i),a}))).map((e,t)=>({result:e,runManager:l?.[t]})).filter(({result:e})=>"fulfilled"===e.status&&null!=e.value||"rejected"===e.status),h=[];await Promise.all(u.map(async({result:e,runManager:t},r)=>{if("fulfilled"!==e.status)return await t?.handleLLMError(e.reason,void 0,void 0,void 0,{cached:!0}),Promise.reject(e.reason);{let i=e.value;return h[r]=i.map(e=>(e.generationInfo={...e.generationInfo,tokenUsage:{}},e)),i.length&&await t?.handleLLMNewToken(i[0].text),t?.handleLLMEnd({generations:[i]},void 0,void 0,void 0,{cached:!0})}}));let d={generations:h,missingPromptIndices:c,startedRunManagers:l};return Object.defineProperty(d,ts.RUN_KEY,{value:l?{runIds:l?.map(e=>e.runId)}:void 0,configurable:!0}),d}async generate(e,t,r){let i;if(!Array.isArray(e))throw Error("Argument 'prompts' is expected to be a string[]");i=Array.isArray(t)?{stop:t}:t;let[a,s]=this._separateRunnableConfigFromCallOptionsCompat(i);if(a.callbacks=a.callbacks??r,!this.cache)return this._generateUncached(e,s,a);let{cache:n}=this,o=this._getSerializedCacheKeyParametersForCall(s),{generations:l,missingPromptIndices:c,startedRunManagers:u}=await this._generateCached({prompts:e,cache:n,llmStringKey:o,parsedOptions:s,handledOptions:a,runId:a.runId}),h={};if(c.length>0){let t=await this._generateUncached(c.map(t=>e[t]),s,a,void 0!==u?c.map(e=>u?.[e]):void 0);await Promise.all(t.generations.map(async(t,r)=>{let i=c[r];return l[i]=t,n.update(e[i],o,t)})),h=t.llmOutput??{}}return{generations:l,llmOutput:h}}async call(e,t,r){let{generations:i}=await this.generate([e],t,r);return i[0][0].text}async predict(e,t,r){return this.call(e,t,r)}async predictMessages(e,t,r){let i=(0,eq.getBufferString)(e),a=await this.call(i,t,r);return new eq.AIMessage(a)}_identifyingParams(){return{}}serialize(){return{...this._identifyingParams(),_type:this._llmType(),_model:this._modelType()}}_modelType(){return"base_llm"}}class tg extends tf{async _generate(e,t,r){return{generations:await Promise.all(e.map((e,i)=>this._call(e,{...t,promptIndex:i},r).then(e=>[{text:e}])))}}}class tb{}let ty=(e,t)=>{if(void 0!==t)return e[t];let r=Object.keys(e);if(1===r.length)return e[r[0]]},t_=(e,t)=>{let r=ty(e,t);if(!r){let t=Object.keys(e);throw Error(`input values have ${t.length} keys, you must specify an input key or pass only 1 key as input`)}return r},tw=(e,t)=>{let r=ty(e,t);if(!r&&""!==r){let t=Object.keys(e);throw Error(`output values have ${t.length} keys, you must specify an output key or pass only 1 key as output`)}return r};function tv(e,t){let r=Object.keys(e).filter(e=>!t.includes(e)&&"stop"!==e);if(1!==r.length)throw Error(`One input key expected, but got ${r.length}`);return r[0]}class tk extends e8.YN{static lc_name(){return"RouterRunnable"}constructor(e){super(e),Object.defineProperty(this,"lc_namespace",{enumerable:!0,configurable:!0,writable:!0,value:["langchain_core","runnables"]}),Object.defineProperty(this,"lc_serializable",{enumerable:!0,configurable:!0,writable:!0,value:!0}),Object.defineProperty(this,"runnables",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.runnables=e.runnables}async invoke(e,t){let{key:r,input:i}=e,a=this.runnables[r];if(void 0===a)throw Error(`No runnable associated with key "${r}".`);return a.invoke(i,(0,to.ZI)(t))}async batch(e,t,r){let i=e.map(e=>e.key),a=e.map(e=>e.input);if(void 0!==i.find(e=>void 0===this.runnables[e]))throw Error("One or more keys do not have a corresponding runnable.");let s=i.map(e=>this.runnables[e]),n=this._getOptionsList(t??{},e.length),o=n[0]?.maxConcurrency??r?.maxConcurrency,l=o&&o>0?o:e.length,c=[];for(let e=0;e<a.length;e+=l){let t=a.slice(e,e+l).map((e,t)=>s[t].invoke(e,n[t])),r=await Promise.all(t);c.push(r)}return c.flat()}async stream(e,t){let{key:r,input:i}=e,a=this.runnables[r];if(void 0===a)throw Error(`No runnable associated with key "${r}".`);return a.stream(i,t)}}class tO extends e8.YN{static lc_name(){return"RunnableBranch"}constructor(e){super(e),Object.defineProperty(this,"lc_namespace",{enumerable:!0,configurable:!0,writable:!0,value:["langchain_core","runnables"]}),Object.defineProperty(this,"lc_serializable",{enumerable:!0,configurable:!0,writable:!0,value:!0}),Object.defineProperty(this,"default",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"branches",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.branches=e.branches,this.default=e.default}static from(e){if(e.length<1)throw Error("RunnableBranch requires at least one branch");return new this({branches:e.slice(0,-1).map(([e,t])=>[(0,e8.Bp)(e),(0,e8.Bp)(t)]),default:(0,e8.Bp)(e[e.length-1])})}async _invoke(e,t,r){let i;for(let a=0;a<this.branches.length;a+=1){let[s,n]=this.branches[a];if(await s.invoke(e,(0,to.tn)(t,{callbacks:r?.getChild(`condition:${a+1}`)}))){i=await n.invoke(e,(0,to.tn)(t,{callbacks:r?.getChild(`branch:${a+1}`)}));break}}return i||(i=await this.default.invoke(e,(0,to.tn)(t,{callbacks:r?.getChild("branch:default")}))),i}async invoke(e,t={}){return this._callWithConfig(this._invoke,e,t)}async *_streamIterator(e,t){let r,i,a=await (0,to.kJ)(t),s=await a?.handleChainStart(this.toJSON(),(0,e8.GH)(e,"input"),t?.runId,void 0,void 0,void 0,t?.runName),n=!0;try{for(let a=0;a<this.branches.length;a+=1){let[o,l]=this.branches[a];if(await o.invoke(e,(0,to.tn)(t,{callbacks:s?.getChild(`condition:${a+1}`)}))){for await(let o of i=await l.stream(e,(0,to.tn)(t,{callbacks:s?.getChild(`branch:${a+1}`)})))if(yield o,n)if(void 0===r)r=o;else try{r=(0,tn.concat)(r,o)}catch(e){r=void 0,n=!1}break}}if(void 0===i){for await(let a of i=await this.default.stream(e,(0,to.tn)(t,{callbacks:s?.getChild("branch:default")})))if(yield a,n)if(void 0===r)r=a;else try{r=(0,tn.concat)(r,a)}catch(e){r=void 0,n=!1}}}catch(e){throw await s?.handleChainError(e),e}await s?.handleChainEnd(r??{})}}class tx extends e8.fJ{constructor(e){let t=e8.jY.from((e,t)=>this._enterHistory(e,t??{})).withConfig({runName:"loadHistory"}),r=e.historyMessagesKey??e.inputMessagesKey;r&&(t=tl.assign({[r]:t}).withConfig({runName:"insertHistory"})),super({...e,config:e.config??{},bound:t.pipe(e.runnable.withListeners({onEnd:(e,t)=>this._exitHistory(e,t??{})})).withConfig({runName:"RunnableWithMessageHistory"})}),Object.defineProperty(this,"runnable",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"inputMessagesKey",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"outputMessagesKey",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"historyMessagesKey",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"getMessageHistory",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.runnable=e.runnable,this.getMessageHistory=e.getMessageHistory,this.inputMessagesKey=e.inputMessagesKey,this.outputMessagesKey=e.outputMessagesKey,this.historyMessagesKey=e.historyMessagesKey}_getInputMessages(e){let t;if("object"!=typeof e||Array.isArray(e)||(0,eq.isBaseMessage)(e))t=e;else{let r;r=this.inputMessagesKey?this.inputMessagesKey:1===Object.keys(e).length?Object.keys(e)[0]:"input",t=Array.isArray(e[r])&&Array.isArray(e[r][0])?e[r][0]:e[r]}if("string"==typeof t)return[new eq.HumanMessage(t)];if(Array.isArray(t))return t;if((0,eq.isBaseMessage)(t))return[t];throw Error(`Expected a string, BaseMessage, or array of BaseMessages.
Got ${JSON.stringify(t,null,2)}`)}_getOutputMessages(e){let t;if(Array.isArray(e)||(0,eq.isBaseMessage)(e)||"string"==typeof e)t=e;else{let r;r=void 0!==this.outputMessagesKey?this.outputMessagesKey:1===Object.keys(e).length?Object.keys(e)[0]:"output",t=void 0!==e.generations?e.generations[0][0].message:e[r]}if("string"==typeof t)return[new eq.AIMessage(t)];if(Array.isArray(t))return t;if((0,eq.isBaseMessage)(t))return[t];throw Error(`Expected a string, BaseMessage, or array of BaseMessages. Received: ${JSON.stringify(t,null,2)}`)}async _enterHistory(e,t){let r=t?.configurable?.messageHistory,i=await r.getMessages();return void 0===this.historyMessagesKey?i.concat(this._getInputMessages(e)):i}async _exitHistory(e,t){let r,i=t.configurable?.messageHistory;r=Array.isArray(e.inputs)&&Array.isArray(e.inputs[0])?e.inputs[0]:e.inputs;let a=this._getInputMessages(r);if(void 0===this.historyMessagesKey){let e=await i.getMessages();a=a.slice(e.length)}let s=e.outputs;if(!s)throw Error(`Output values from 'Run' undefined. Run: ${JSON.stringify(e,null,2)}`);let n=this._getOutputMessages(s);await i.addMessages([...a,...n])}async _mergeConfig(...e){let t=await super._mergeConfig(...e);if(!t.configurable||!t.configurable.sessionId){let e={[this.inputMessagesKey??"input"]:"foo"};throw Error(`sessionId is required. Pass it in as part of the config argument to .invoke() or .stream()
eg. chain.invoke(${JSON.stringify(e)}, ${JSON.stringify({configurable:{sessionId:"123"}})})`)}let{sessionId:r}=t.configurable;return t.configurable.messageHistory=await this.getMessageHistory(r),t}}var tS=r(13346);class tC extends e8.YN{parseResultWithPrompt(e,t,r){return this.parseResult(e,r)}_baseMessageToString(e){return"string"==typeof e.content?e.content:this._baseMessageContentToString(e.content)}_baseMessageContentToString(e){return JSON.stringify(e)}async invoke(e,t){return"string"==typeof e?this._callWithConfig(async(e,t)=>this.parseResult([{text:e}],t?.callbacks),e,{...t,runType:"parser"}):this._callWithConfig(async(e,t)=>this.parseResult([{message:e,text:this._baseMessageToString(e)}],t?.callbacks),e,{...t,runType:"parser"})}}class tA extends tC{parseResult(e,t){return this.parse(e[0].text,t)}async parseWithPrompt(e,t,r){return this.parse(e,r)}_type(){throw Error("_type not implemented")}}class tT extends Error{constructor(e,t,r,i=!1){if(super(e),Object.defineProperty(this,"llmOutput",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"observation",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"sendToLLM",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.llmOutput=t,this.observation=r,this.sendToLLM=i,i&&(void 0===r||void 0===t))throw Error("Arguments 'observation' & 'llmOutput' are required if 'sendToLlm' is true");(0,tS.Y)(this,"OUTPUT_PARSING_FAILURE")}}var tP=r(17107),tE=r(72892);class tj extends tA{async *_transform(e){for await(let t of e)"string"==typeof t?yield this.parseResult([{text:t}]):yield this.parseResult([{message:t,text:this._baseMessageToString(t)}])}async *transform(e,t){yield*this._transformStreamWithConfig(e,this._transform.bind(this),{...t,runType:"parser"})}}class tI extends tj{constructor(e){super(e),Object.defineProperty(this,"diff",{enumerable:!0,configurable:!0,writable:!0,value:!1}),this.diff=e?.diff??this.diff}async *_transform(e){let t,r;for await(let i of e){let e;if("string"!=typeof i&&"string"!=typeof i.content)throw Error("Cannot handle non-string output.");if((0,tE.AJ)(i)){if("string"!=typeof i.content)throw Error("Cannot handle non-string message output.");e=new ts.ChatGenerationChunk({message:i,text:i.content})}else if((0,tE.ny)(i)){if("string"!=typeof i.content)throw Error("Cannot handle non-string message output.");e=new ts.ChatGenerationChunk({message:(0,ej.ih)(i),text:i.content})}else e=new ts.GenerationChunk({text:i});r=void 0===r?e:r.concat(e);let a=await this.parsePartialResult([r]);null==a||(0,tP.rA)(a,t)||(this.diff?yield this._diff(t,a):yield a,t=a)}}getFormatInstructions(){return""}}class tN extends tj{constructor(){super(...arguments),Object.defineProperty(this,"lc_namespace",{enumerable:!0,configurable:!0,writable:!0,value:["langchain_core","output_parsers","bytes"]}),Object.defineProperty(this,"lc_serializable",{enumerable:!0,configurable:!0,writable:!0,value:!0}),Object.defineProperty(this,"textEncoder",{enumerable:!0,configurable:!0,writable:!0,value:new TextEncoder})}static lc_name(){return"BytesOutputParser"}parse(e){return Promise.resolve(this.textEncoder.encode(e))}getFormatInstructions(){return""}}class tR extends tj{constructor(){super(...arguments),Object.defineProperty(this,"re",{enumerable:!0,configurable:!0,writable:!0,value:void 0})}async *_transform(e){let t="";for await(let r of e)if("string"==typeof r?t+=r:t+=r.content,this.re){let e=[...t.matchAll(this.re)];if(e.length>1){let r=0;for(let t of e.slice(0,-1))yield[t[1]],r+=(t.index??0)+t[0].length;t=t.slice(r)}}else{let e=await this.parse(t);if(e.length>1){for(let t of e.slice(0,-1))yield[t];t=e[e.length-1]}}for(let e of(await this.parse(t)))yield[e]}}class tM extends tR{constructor(){super(...arguments),Object.defineProperty(this,"lc_namespace",{enumerable:!0,configurable:!0,writable:!0,value:["langchain_core","output_parsers","list"]}),Object.defineProperty(this,"lc_serializable",{enumerable:!0,configurable:!0,writable:!0,value:!0})}static lc_name(){return"CommaSeparatedListOutputParser"}async parse(e){try{return e.trim().split(",").map(e=>e.trim())}catch(t){throw new tT(`Could not parse output: ${e}`,e)}}getFormatInstructions(){return"Your response should be a list of comma separated values, eg: `foo, bar, baz`"}}class tD extends tR{constructor({length:e,separator:t}){super(...arguments),Object.defineProperty(this,"lc_namespace",{enumerable:!0,configurable:!0,writable:!0,value:["langchain_core","output_parsers","list"]}),Object.defineProperty(this,"length",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"separator",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.length=e,this.separator=t||","}async parse(e){try{let t=e.trim().split(this.separator).map(e=>e.trim());if(void 0!==this.length&&t.length!==this.length)throw new tT(`Incorrect number of items. Expected ${this.length}, got ${t.length}.`);return t}catch(t){if(Object.getPrototypeOf(t)===tT.prototype)throw t;throw new tT(`Could not parse output: ${e}`)}}getFormatInstructions(){return`Your response should be a list of ${void 0===this.length?"":`${this.length} `}items separated by "${this.separator}" (eg: \`foo${this.separator} bar${this.separator} baz\`)`}}class t$ extends tR{constructor(){super(...arguments),Object.defineProperty(this,"lc_namespace",{enumerable:!0,configurable:!0,writable:!0,value:["langchain_core","output_parsers","list"]}),Object.defineProperty(this,"lc_serializable",{enumerable:!0,configurable:!0,writable:!0,value:!0}),Object.defineProperty(this,"re",{enumerable:!0,configurable:!0,writable:!0,value:/\d+\.\s([^\n]+)/g})}static lc_name(){return"NumberedListOutputParser"}getFormatInstructions(){return`Your response should be a numbered list with each item on a new line. For example: 

1. foo

2. bar

3. baz`}async parse(e){return[...e.matchAll(this.re)??[]].map(e=>e[1])}}class tL extends tR{constructor(){super(...arguments),Object.defineProperty(this,"lc_namespace",{enumerable:!0,configurable:!0,writable:!0,value:["langchain_core","output_parsers","list"]}),Object.defineProperty(this,"lc_serializable",{enumerable:!0,configurable:!0,writable:!0,value:!0}),Object.defineProperty(this,"re",{enumerable:!0,configurable:!0,writable:!0,value:/^\s*[-*]\s([^\n]+)$/gm})}static lc_name(){return"NumberedListOutputParser"}getFormatInstructions(){return`Your response should be a numbered list with each item on a new line. For example: 

1. foo

2. bar

3. baz`}async parse(e){return[...e.matchAll(this.re)??[]].map(e=>e[1])}}class tU extends tj{constructor(){super(...arguments),Object.defineProperty(this,"lc_namespace",{enumerable:!0,configurable:!0,writable:!0,value:["langchain_core","output_parsers","string"]}),Object.defineProperty(this,"lc_serializable",{enumerable:!0,configurable:!0,writable:!0,value:!0})}static lc_name(){return"StrOutputParser"}parse(e){return Promise.resolve(e)}getFormatInstructions(){return""}_textContentToString(e){return e.text}_imageUrlContentToString(e){throw Error('Cannot coerce a multimodal "image_url" message part into a string.')}_messageContentComplexToString(e){switch(e.type){case"text":case"text_delta":if("text"in e)return this._textContentToString(e);break;case"image_url":if("image_url"in e)return this._imageUrlContentToString(e);break;default:throw Error(`Cannot coerce "${e.type}" message part into a string.`)}throw Error(`Invalid content type: ${e.type}`)}_baseMessageContentToString(e){return e.reduce((e,t)=>e+this._messageContentComplexToString(t),"")}}class tF extends tA{static lc_name(){return"StructuredOutputParser"}toJSON(){return this.toJSONNotImplemented()}constructor(e){super(e),Object.defineProperty(this,"schema",{enumerable:!0,configurable:!0,writable:!0,value:e}),Object.defineProperty(this,"lc_namespace",{enumerable:!0,configurable:!0,writable:!0,value:["langchain","output_parsers","structured"]})}static fromZodSchema(e){return new this(e)}static fromNamesAndDescriptions(e){return new this(q.z.object(Object.fromEntries(Object.entries(e).map(([e,t])=>[e,q.z.string().describe(t)]))))}getFormatInstructions(){return`You must format your output as a JSON value that adheres to a given "JSON Schema" instance.

"JSON Schema" is a declarative language that allows you to annotate and validate JSON documents.

For example, the example "JSON Schema" instance {{"properties": {{"foo": {{"description": "a list of test words", "type": "array", "items": {{"type": "string"}}}}}}, "required": ["foo"]}}
would match an object with one required property, "foo". The "type" property specifies "foo" must be an "array", and the "description" property semantically describes it as "a list of test words". The items within "foo" must be strings.
Thus, the object {{"foo": ["bar", "baz"]}} is a well-formatted instance of this example "JSON Schema". The object {{"properties": {{"foo": ["bar", "baz"]}}}} is not well-formatted.

Your output will be parsed and type-checked according to the provided schema instance, so make sure all fields in your output match the schema exactly and there are no trailing commas!

Here is the JSON Schema instance your output must adhere to. Include the enclosing markdown codeblock:
\`\`\`json
${JSON.stringify((0,tu.toJsonSchema)(this.schema))}
\`\`\`
`}async parse(e){try{let t=(e.includes("```")?e.trim().split(/```(?:json)?/)[1]:e.trim()).replace(/"([^"\\]*(\\.[^"\\]*)*)"/g,(e,t)=>{let r=t.replace(/\n/g,"\\n");return`"${r}"`}).replace(/\n/g,"");return await (0,tc.hZ)(this.schema,JSON.parse(t))}catch(t){throw new tT(`Failed to parse. Text: "${e}". Error: ${t}`,e)}}}class tq extends tF{static lc_name(){return"JsonMarkdownStructuredOutputParser"}getFormatInstructions(e){let t=e?.interpolationDepth??1;if(t<1)throw Error("f string interpolation depth must be at least 1");return`Return a markdown code snippet with a JSON object formatted to look like:
\`\`\`json
${this._schemaToInstruction((0,tu.toJsonSchema)(this.schema)).replaceAll("{","{".repeat(t)).replaceAll("}","}".repeat(t))}
\`\`\``}_schemaToInstruction(e,t=2){if("type"in e){let r,i=!1;if(Array.isArray(e.type)){let t=e.type.findIndex(e=>"null"===e);-1!==t&&(i=!0,e.type.splice(t,1)),r=e.type.join(" | ")}else r=e.type;if("object"===e.type&&e.properties){let r=e.description?` // ${e.description}`:"",i=Object.entries(e.properties).map(([r,i])=>{let a=e.required?.includes(r)?"":" (optional)";return`${" ".repeat(t)}"${r}": ${this._schemaToInstruction(i,t+2)}${a}`}).join("\n");return`{
${i}
${" ".repeat(t-2)}}${r}`}if("array"===e.type&&e.items){let r=e.description?` // ${e.description}`:"";return`array[
${" ".repeat(t)}${this._schemaToInstruction(e.items,t+2)}
${" ".repeat(t-2)}] ${r}`}let a=i?" (nullable)":"",s=e.description?` // ${e.description}`:"";return`${r}${s}${a}`}if("anyOf"in e)return e.anyOf.map(e=>this._schemaToInstruction(e,t)).join(`
${" ".repeat(t-2)}`);throw Error("unsupported schema type")}static fromZodSchema(e){return new this(e)}static fromNamesAndDescriptions(e){return new this(q.z.object(Object.fromEntries(Object.entries(e).map(([e,t])=>[e,q.z.string().describe(t)]))))}}class tz extends tA{constructor({inputSchema:e}){super(...arguments),Object.defineProperty(this,"structuredInputParser",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.structuredInputParser=new tq(e)}async parse(e){let t;try{t=await this.structuredInputParser.parse(e)}catch(t){throw new tT(`Failed to parse. Text: "${e}". Error: ${t}`,e)}return this.outputProcessor(t)}getFormatInstructions(){return this.structuredInputParser.getFormatInstructions()}}var tV=r(91405),tK=r(13250);class tB extends tI{constructor(){super(...arguments),Object.defineProperty(this,"lc_namespace",{enumerable:!0,configurable:!0,writable:!0,value:["langchain_core","output_parsers"]}),Object.defineProperty(this,"lc_serializable",{enumerable:!0,configurable:!0,writable:!0,value:!0})}static lc_name(){return"JsonOutputParser"}_diff(e,t){if(t)return e?(0,tV.UD)(e,t):[{op:"replace",path:"",value:t}]}async parsePartialResult(e){return(0,tK.D)(e[0].text)}async parse(e){return(0,tK.D)(e,JSON.parse)}getFormatInstructions(){return""}}let tW=function(){let e={};e.parser=function(e,t){return new r(e,t)},e.SAXParser=r,e.SAXStream=l,e.createStream=function(e,t){return new l(e,t)},e.MAX_BUFFER_LENGTH=65536;let t=["comment","sgmlDecl","textNode","tagName","doctype","procInstName","procInstBody","entity","attribName","attribValue","cdata","script"];function r(i,a){if(!(this instanceof r))return new r(i,a);(function(e){for(var r=0,i=t.length;r<i;r++)e[t[r]]=""})(this),this.q=this.c="",this.bufferCheckPosition=e.MAX_BUFFER_LENGTH,this.opt=a||{},this.opt.lowercase=this.opt.lowercase||this.opt.lowercasetags,this.looseCase=this.opt.lowercase?"toLowerCase":"toUpperCase",this.tags=[],this.closed=this.closedRoot=this.sawRoot=!1,this.tag=this.error=null,this.strict=!!i,this.noscript=!!(i||this.opt.noscript),this.state=_.BEGIN,this.strictEntities=this.opt.strictEntities,this.ENTITIES=this.strictEntities?Object.create(e.XML_ENTITIES):Object.create(e.ENTITIES),this.attribList=[],this.opt.xmlns&&(this.ns=Object.create(h)),this.trackPosition=!1!==this.opt.position,this.trackPosition&&(this.position=this.line=this.column=0),v(this,"onready")}e.EVENTS=["text","processinginstruction","sgmldeclaration","doctype","comment","opentagstart","attribute","opentag","closetag","opencdata","cdata","closecdata","error","end","ready","script","opennamespace","closenamespace"],Object.create||(Object.create=function(e){function t(){}return t.prototype=e,new t}),Object.keys||(Object.keys=function(e){var t=[];for(var r in e)e.hasOwnProperty(r)&&t.push(r);return t}),r.prototype={end:function(){C(this)},write:function(r){if(this.error)throw this.error;if(this.closed)return S(this,"Cannot write after close. Assign an onready handler.");if(null===r)return C(this);"object"==typeof r&&(r=r.toString());for(var i=0,a="";a=R(r,i++),this.c=a,a;)switch(this.trackPosition&&(this.position++,"\n"===a?(this.line++,this.column=0):this.column++),this.state){case _.BEGIN:if(this.state=_.BEGIN_WHITESPACE,"\uFEFF"===a)continue;N(this,a);continue;case _.BEGIN_WHITESPACE:N(this,a);continue;case _.TEXT:if(this.sawRoot&&!this.closedRoot){for(var s=i-1;a&&"<"!==a&&"&"!==a;)(a=R(r,i++))&&this.trackPosition&&(this.position++,"\n"===a?(this.line++,this.column=0):this.column++);this.textNode+=r.substring(s,i-1)}"<"!==a||this.sawRoot&&this.closedRoot&&!this.strict?(g(a)||this.sawRoot&&!this.closedRoot||A(this,"Text data outside of root node."),"&"===a?this.state=_.TEXT_ENTITY:this.textNode+=a):(this.state=_.OPEN_WAKA,this.startTagPosition=this.position);continue;case _.SCRIPT:"<"===a?this.state=_.SCRIPT_ENDING:this.script+=a;continue;case _.SCRIPT_ENDING:"/"===a?this.state=_.CLOSE_TAG:(this.script+="<"+a,this.state=_.SCRIPT);continue;case _.OPEN_WAKA:"!"===a?(this.state=_.SGML_DECL,this.sgmlDecl=""):g(a)||(y(d,a)?(this.state=_.OPEN_TAG,this.tagName=a):"/"===a?(this.state=_.CLOSE_TAG,this.tagName=""):"?"===a?(this.state=_.PROC_INST,this.procInstName=this.procInstBody=""):(A(this,"Unencoded <"),this.startTagPosition+1<this.position&&(a=Array(this.position-this.startTagPosition).join(" ")+a),this.textNode+="<"+a,this.state=_.TEXT));continue;case _.SGML_DECL:"[CDATA["===(this.sgmlDecl+a).toUpperCase()?(k(this,"onopencdata"),this.state=_.CDATA,this.sgmlDecl="",this.cdata=""):this.sgmlDecl+a==="--"?(this.state=_.COMMENT,this.comment="",this.sgmlDecl=""):"DOCTYPE"===(this.sgmlDecl+a).toUpperCase()?(this.state=_.DOCTYPE,(this.doctype||this.sawRoot)&&A(this,"Inappropriately located doctype declaration"),this.doctype="",this.sgmlDecl=""):">"===a?(k(this,"onsgmldeclaration",this.sgmlDecl),this.sgmlDecl="",this.state=_.TEXT):(b(a)&&(this.state=_.SGML_DECL_QUOTED),this.sgmlDecl+=a);continue;case _.SGML_DECL_QUOTED:a===this.q&&(this.state=_.SGML_DECL,this.q=""),this.sgmlDecl+=a;continue;case _.DOCTYPE:">"===a?(this.state=_.TEXT,k(this,"ondoctype",this.doctype),this.doctype=!0):(this.doctype+=a,"["===a?this.state=_.DOCTYPE_DTD:b(a)&&(this.state=_.DOCTYPE_QUOTED,this.q=a));continue;case _.DOCTYPE_QUOTED:this.doctype+=a,a===this.q&&(this.q="",this.state=_.DOCTYPE);continue;case _.DOCTYPE_DTD:this.doctype+=a,"]"===a?this.state=_.DOCTYPE:b(a)&&(this.state=_.DOCTYPE_DTD_QUOTED,this.q=a);continue;case _.DOCTYPE_DTD_QUOTED:this.doctype+=a,a===this.q&&(this.state=_.DOCTYPE_DTD,this.q="");continue;case _.COMMENT:"-"===a?this.state=_.COMMENT_ENDING:this.comment+=a;continue;case _.COMMENT_ENDING:"-"===a?(this.state=_.COMMENT_ENDED,this.comment=x(this.opt,this.comment),this.comment&&k(this,"oncomment",this.comment),this.comment=""):(this.comment+="-"+a,this.state=_.COMMENT);continue;case _.COMMENT_ENDED:">"!==a?(A(this,"Malformed comment"),this.comment+="--"+a,this.state=_.COMMENT):this.state=_.TEXT;continue;case _.CDATA:"]"===a?this.state=_.CDATA_ENDING:this.cdata+=a;continue;case _.CDATA_ENDING:"]"===a?this.state=_.CDATA_ENDING_2:(this.cdata+="]"+a,this.state=_.CDATA);continue;case _.CDATA_ENDING_2:">"===a?(this.cdata&&k(this,"oncdata",this.cdata),k(this,"onclosecdata"),this.cdata="",this.state=_.TEXT):"]"===a?this.cdata+="]":(this.cdata+="]]"+a,this.state=_.CDATA);continue;case _.PROC_INST:"?"===a?this.state=_.PROC_INST_ENDING:g(a)?this.state=_.PROC_INST_BODY:this.procInstName+=a;continue;case _.PROC_INST_BODY:!this.procInstBody&&g(a)||("?"===a?this.state=_.PROC_INST_ENDING:this.procInstBody+=a);continue;case _.PROC_INST_ENDING:">"===a?(k(this,"onprocessinginstruction",{name:this.procInstName,body:this.procInstBody}),this.procInstName=this.procInstBody="",this.state=_.TEXT):(this.procInstBody+="?"+a,this.state=_.PROC_INST_BODY);continue;case _.OPEN_TAG:y(p,a)?this.tagName+=a:(!function(e){e.strict||(e.tagName=e.tagName[e.looseCase]());var t=e.tags[e.tags.length-1]||e,r=e.tag={name:e.tagName,attributes:{}};e.opt.xmlns&&(r.ns=t.ns),e.attribList.length=0,k(e,"onopentagstart",r)}(this),">"===a?E(this):"/"===a?this.state=_.OPEN_TAG_SLASH:(g(a)||A(this,"Invalid character in tag name"),this.state=_.ATTRIB));continue;case _.OPEN_TAG_SLASH:">"===a?(E(this,!0),j(this)):(A(this,"Forward-slash in opening tag not followed by >"),this.state=_.ATTRIB);continue;case _.ATTRIB:g(a)||(">"===a?E(this):"/"===a?this.state=_.OPEN_TAG_SLASH:y(d,a)?(this.attribName=a,this.attribValue="",this.state=_.ATTRIB_NAME):A(this,"Invalid attribute name"));continue;case _.ATTRIB_NAME:"="===a?this.state=_.ATTRIB_VALUE:">"===a?(A(this,"Attribute without value"),this.attribValue=this.attribName,P(this),E(this)):g(a)?this.state=_.ATTRIB_NAME_SAW_WHITE:y(p,a)?this.attribName+=a:A(this,"Invalid attribute name");continue;case _.ATTRIB_NAME_SAW_WHITE:if("="===a)this.state=_.ATTRIB_VALUE;else{if(g(a))continue;A(this,"Attribute without value"),this.tag.attributes[this.attribName]="",this.attribValue="",k(this,"onattribute",{name:this.attribName,value:""}),this.attribName="",">"===a?E(this):y(d,a)?(this.attribName=a,this.state=_.ATTRIB_NAME):(A(this,"Invalid attribute name"),this.state=_.ATTRIB)}continue;case _.ATTRIB_VALUE:g(a)||(b(a)?(this.q=a,this.state=_.ATTRIB_VALUE_QUOTED):(A(this,"Unquoted attribute value"),this.state=_.ATTRIB_VALUE_UNQUOTED,this.attribValue=a));continue;case _.ATTRIB_VALUE_QUOTED:if(a!==this.q){"&"===a?this.state=_.ATTRIB_VALUE_ENTITY_Q:this.attribValue+=a;continue}P(this),this.q="",this.state=_.ATTRIB_VALUE_CLOSED;continue;case _.ATTRIB_VALUE_CLOSED:g(a)?this.state=_.ATTRIB:">"===a?E(this):"/"===a?this.state=_.OPEN_TAG_SLASH:y(d,a)?(A(this,"No whitespace between attributes"),this.attribName=a,this.attribValue="",this.state=_.ATTRIB_NAME):A(this,"Invalid attribute name");continue;case _.ATTRIB_VALUE_UNQUOTED:if(!(">"===(n=a)||g(n))){"&"===a?this.state=_.ATTRIB_VALUE_ENTITY_U:this.attribValue+=a;continue}P(this),">"===a?E(this):this.state=_.ATTRIB;continue;case _.CLOSE_TAG:this.tagName?">"===a?j(this):y(p,a)?this.tagName+=a:this.script?(this.script+="</"+this.tagName,this.tagName="",this.state=_.SCRIPT):(g(a)||A(this,"Invalid tagname in closing tag"),this.state=_.CLOSE_TAG_SAW_WHITE):g(a)||(y(d,a)?this.tagName=a:this.script?(this.script+="</"+a,this.state=_.SCRIPT):A(this,"Invalid tagname in closing tag."));continue;case _.CLOSE_TAG_SAW_WHITE:if(g(a))continue;">"===a?j(this):A(this,"Invalid characters in closing tag");continue;case _.TEXT_ENTITY:case _.ATTRIB_VALUE_ENTITY_Q:case _.ATTRIB_VALUE_ENTITY_U:switch(this.state){case _.TEXT_ENTITY:o=_.TEXT,l="textNode";break;case _.ATTRIB_VALUE_ENTITY_Q:o=_.ATTRIB_VALUE_QUOTED,l="attribValue";break;case _.ATTRIB_VALUE_ENTITY_U:o=_.ATTRIB_VALUE_UNQUOTED,l="attribValue"}if(";"===a)if(this.opt.unparsedEntities){var n,o,l,c=I(this);this.entity="",this.state=o,this.write(c)}else this[l]+=I(this),this.entity="",this.state=o;else y(this.entity.length?f:m,a)?this.entity+=a:(A(this,"Invalid character in entity name"),this[l]+="&"+this.entity+a,this.entity="",this.state=o);continue;default:throw Error(this,"Unknown state: "+this.state)}return this.position>=this.bufferCheckPosition&&function(r){for(var i=Math.max(e.MAX_BUFFER_LENGTH,10),a=0,s=0,n=t.length;s<n;s++){var o=r[t[s]].length;if(o>i)switch(t[s]){case"textNode":O(r);break;case"cdata":k(r,"oncdata",r.cdata),r.cdata="";break;case"script":k(r,"onscript",r.script),r.script="";break;default:S(r,"Max buffer length exceeded: "+t[s])}a=Math.max(a,o)}r.bufferCheckPosition=e.MAX_BUFFER_LENGTH-a+r.position}(this),this},resume:function(){return this.error=null,this},close:function(){return this.write(null)},flush:function(){O(this),""!==this.cdata&&(k(this,"oncdata",this.cdata),this.cdata=""),""!==this.script&&(k(this,"onscript",this.script),this.script="")}};var i,a,s,n=ReadableStream;n||(n=function(){});var o=e.EVENTS.filter(function(e){return"error"!==e&&"end"!==e});function l(e,t){if(!(this instanceof l))return new l(e,t);n.apply(this),this._parser=new r(e,t),this.writable=!0,this.readable=!0;var i=this;this._parser.onend=function(){i.emit("end")},this._parser.onerror=function(e){i.emit("error",e),i._parser.error=null},this._decoder=null,o.forEach(function(e){Object.defineProperty(i,"on"+e,{get:function(){return i._parser["on"+e]},set:function(t){if(!t)return i.removeAllListeners(e),i._parser["on"+e]=t,t;i.on(e,t)},enumerable:!0,configurable:!1})})}l.prototype=Object.create(n.prototype,{constructor:{value:l}}),l.prototype.write=function(e){return this._parser.write(e.toString()),this.emit("data",e),!0},l.prototype.end=function(e){return e&&e.length&&this.write(e),this._parser.end(),!0},l.prototype.on=function(e,t){var r=this;return r._parser["on"+e]||-1===o.indexOf(e)||(r._parser["on"+e]=function(){var t=1==arguments.length?[arguments[0]]:Array.apply(null,arguments);t.splice(0,0,e),r.emit.apply(r,t)}),n.prototype.on.call(r,e,t)};var c="http://www.w3.org/XML/1998/namespace",u="http://www.w3.org/2000/xmlns/",h={xml:c,xmlns:u},d=/[:_A-Za-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD]/,p=/[:_A-Za-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\u00B7\u0300-\u036F\u203F-\u2040.\d-]/,m=/[#:_A-Za-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD]/,f=/[#:_A-Za-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\u00B7\u0300-\u036F\u203F-\u2040.\d-]/;function g(e){return" "===e||"\n"===e||"\r"===e||"	"===e}function b(e){return'"'===e||"'"===e}function y(e,t){return e.test(t)}var _=0;for(var w in e.STATE={BEGIN:_++,BEGIN_WHITESPACE:_++,TEXT:_++,TEXT_ENTITY:_++,OPEN_WAKA:_++,SGML_DECL:_++,SGML_DECL_QUOTED:_++,DOCTYPE:_++,DOCTYPE_QUOTED:_++,DOCTYPE_DTD:_++,DOCTYPE_DTD_QUOTED:_++,COMMENT_STARTING:_++,COMMENT:_++,COMMENT_ENDING:_++,COMMENT_ENDED:_++,CDATA:_++,CDATA_ENDING:_++,CDATA_ENDING_2:_++,PROC_INST:_++,PROC_INST_BODY:_++,PROC_INST_ENDING:_++,OPEN_TAG:_++,OPEN_TAG_SLASH:_++,ATTRIB:_++,ATTRIB_NAME:_++,ATTRIB_NAME_SAW_WHITE:_++,ATTRIB_VALUE:_++,ATTRIB_VALUE_QUOTED:_++,ATTRIB_VALUE_CLOSED:_++,ATTRIB_VALUE_UNQUOTED:_++,ATTRIB_VALUE_ENTITY_Q:_++,ATTRIB_VALUE_ENTITY_U:_++,CLOSE_TAG:_++,CLOSE_TAG_SAW_WHITE:_++,SCRIPT:_++,SCRIPT_ENDING:_++},e.XML_ENTITIES={amp:"&",gt:">",lt:"<",quot:'"',apos:"'"},e.ENTITIES={amp:"&",gt:">",lt:"<",quot:'"',apos:"'",AElig:198,Aacute:193,Acirc:194,Agrave:192,Aring:197,Atilde:195,Auml:196,Ccedil:199,ETH:208,Eacute:201,Ecirc:202,Egrave:200,Euml:203,Iacute:205,Icirc:206,Igrave:204,Iuml:207,Ntilde:209,Oacute:211,Ocirc:212,Ograve:210,Oslash:216,Otilde:213,Ouml:214,THORN:222,Uacute:218,Ucirc:219,Ugrave:217,Uuml:220,Yacute:221,aacute:225,acirc:226,aelig:230,agrave:224,aring:229,atilde:227,auml:228,ccedil:231,eacute:233,ecirc:234,egrave:232,eth:240,euml:235,iacute:237,icirc:238,igrave:236,iuml:239,ntilde:241,oacute:243,ocirc:244,ograve:242,oslash:248,otilde:245,ouml:246,szlig:223,thorn:254,uacute:250,ucirc:251,ugrave:249,uuml:252,yacute:253,yuml:255,copy:169,reg:174,nbsp:160,iexcl:161,cent:162,pound:163,curren:164,yen:165,brvbar:166,sect:167,uml:168,ordf:170,laquo:171,not:172,shy:173,macr:175,deg:176,plusmn:177,sup1:185,sup2:178,sup3:179,acute:180,micro:181,para:182,middot:183,cedil:184,ordm:186,raquo:187,frac14:188,frac12:189,frac34:190,iquest:191,times:215,divide:247,OElig:338,oelig:339,Scaron:352,scaron:353,Yuml:376,fnof:402,circ:710,tilde:732,Alpha:913,Beta:914,Gamma:915,Delta:916,Epsilon:917,Zeta:918,Eta:919,Theta:920,Iota:921,Kappa:922,Lambda:923,Mu:924,Nu:925,Xi:926,Omicron:927,Pi:928,Rho:929,Sigma:931,Tau:932,Upsilon:933,Phi:934,Chi:935,Psi:936,Omega:937,alpha:945,beta:946,gamma:947,delta:948,epsilon:949,zeta:950,eta:951,theta:952,iota:953,kappa:954,lambda:955,mu:956,nu:957,xi:958,omicron:959,pi:960,rho:961,sigmaf:962,sigma:963,tau:964,upsilon:965,phi:966,chi:967,psi:968,omega:969,thetasym:977,upsih:978,piv:982,ensp:8194,emsp:8195,thinsp:8201,zwnj:8204,zwj:8205,lrm:8206,rlm:8207,ndash:8211,mdash:8212,lsquo:8216,rsquo:8217,sbquo:8218,ldquo:8220,rdquo:8221,bdquo:8222,dagger:8224,Dagger:8225,bull:8226,hellip:8230,permil:8240,prime:8242,Prime:8243,lsaquo:8249,rsaquo:8250,oline:8254,frasl:8260,euro:8364,image:8465,weierp:8472,real:8476,trade:8482,alefsym:8501,larr:8592,uarr:8593,rarr:8594,darr:8595,harr:8596,crarr:8629,lArr:8656,uArr:8657,rArr:8658,dArr:8659,hArr:8660,forall:8704,part:8706,exist:8707,empty:8709,nabla:8711,isin:8712,notin:8713,ni:8715,prod:8719,sum:8721,minus:8722,lowast:8727,radic:8730,prop:8733,infin:8734,ang:8736,and:8743,or:8744,cap:8745,cup:8746,int:8747,there4:8756,sim:8764,cong:8773,asymp:8776,ne:8800,equiv:8801,le:8804,ge:8805,sub:8834,sup:8835,nsub:8836,sube:8838,supe:8839,oplus:8853,otimes:8855,perp:8869,sdot:8901,lceil:8968,rceil:8969,lfloor:8970,rfloor:8971,lang:9001,rang:9002,loz:9674,spades:9824,clubs:9827,hearts:9829,diams:9830},Object.keys(e.ENTITIES).forEach(function(t){var r=e.ENTITIES[t],i="number"==typeof r?String.fromCharCode(r):r;e.ENTITIES[t]=i}),e.STATE)e.STATE[e.STATE[w]]=w;function v(e,t,r){e[t]&&e[t](r)}function k(e,t,r){e.textNode&&O(e),v(e,t,r)}function O(e){e.textNode=x(e.opt,e.textNode),e.textNode&&v(e,"ontext",e.textNode),e.textNode=""}function x(e,t){return e.trim&&(t=t.trim()),e.normalize&&(t=t.replace(/\s+/g," ")),t}function S(e,t){return O(e),e.trackPosition&&(t+="\nLine: "+e.line+"\nColumn: "+e.column+"\nChar: "+e.c),e.error=t=Error(t),v(e,"onerror",t),e}function C(e){return e.sawRoot&&!e.closedRoot&&A(e,"Unclosed root tag"),e.state!==_.BEGIN&&e.state!==_.BEGIN_WHITESPACE&&e.state!==_.TEXT&&S(e,"Unexpected end"),O(e),e.c="",e.closed=!0,v(e,"onend"),r.call(e,e.strict,e.opt),e}function A(e,t){if("object"!=typeof e||!(e instanceof r))throw Error("bad call to strictFail");e.strict&&S(e,t)}function T(e,t){var r=0>e.indexOf(":")?["",e]:e.split(":"),i=r[0],a=r[1];return t&&"xmlns"===e&&(i="xmlns",a=""),{prefix:i,local:a}}function P(e){if(e.strict||(e.attribName=e.attribName[e.looseCase]()),-1!==e.attribList.indexOf(e.attribName)||e.tag.attributes.hasOwnProperty(e.attribName)){e.attribName=e.attribValue="";return}if(e.opt.xmlns){var t=T(e.attribName,!0),r=t.prefix,i=t.local;if("xmlns"===r)if("xml"===i&&e.attribValue!==c)A(e,"xml: prefix must be bound to "+c+"\nActual: "+e.attribValue);else if("xmlns"===i&&e.attribValue!==u)A(e,"xmlns: prefix must be bound to "+u+"\nActual: "+e.attribValue);else{var a=e.tag,s=e.tags[e.tags.length-1]||e;a.ns===s.ns&&(a.ns=Object.create(s.ns)),a.ns[i]=e.attribValue}e.attribList.push([e.attribName,e.attribValue])}else e.tag.attributes[e.attribName]=e.attribValue,k(e,"onattribute",{name:e.attribName,value:e.attribValue});e.attribName=e.attribValue=""}function E(e,t){if(e.opt.xmlns){var r=e.tag,i=T(e.tagName);r.prefix=i.prefix,r.local=i.local,r.uri=r.ns[i.prefix]||"",r.prefix&&!r.uri&&(A(e,"Unbound namespace prefix: "+JSON.stringify(e.tagName)),r.uri=i.prefix);var a=e.tags[e.tags.length-1]||e;r.ns&&a.ns!==r.ns&&Object.keys(r.ns).forEach(function(t){k(e,"onopennamespace",{prefix:t,uri:r.ns[t]})});for(var s=0,n=e.attribList.length;s<n;s++){var o=e.attribList[s],l=o[0],c=o[1],u=T(l,!0),h=u.prefix,d=u.local,p=""===h?"":r.ns[h]||"",m={name:l,value:c,prefix:h,local:d,uri:p};h&&"xmlns"!==h&&!p&&(A(e,"Unbound namespace prefix: "+JSON.stringify(h)),m.uri=h),e.tag.attributes[l]=m,k(e,"onattribute",m)}e.attribList.length=0}e.tag.isSelfClosing=!!t,e.sawRoot=!0,e.tags.push(e.tag),k(e,"onopentag",e.tag),t||(e.noscript||"script"!==e.tagName.toLowerCase()?e.state=_.TEXT:e.state=_.SCRIPT,e.tag=null,e.tagName=""),e.attribName=e.attribValue="",e.attribList.length=0}function j(e){if(!e.tagName){A(e,"Weird empty close tag."),e.textNode+="</>",e.state=_.TEXT;return}if(e.script){if("script"!==e.tagName){e.script+="</"+e.tagName+">",e.tagName="",e.state=_.SCRIPT;return}k(e,"onscript",e.script),e.script=""}var t=e.tags.length,r=e.tagName;e.strict||(r=r[e.looseCase]());for(var i=r;t--;)if(e.tags[t].name!==i)A(e,"Unexpected close tag");else break;if(t<0){A(e,"Unmatched closing tag: "+e.tagName),e.textNode+="</"+e.tagName+">",e.state=_.TEXT;return}e.tagName=r;for(var a=e.tags.length;a-- >t;){var s=e.tag=e.tags.pop();e.tagName=e.tag.name,k(e,"onclosetag",e.tagName);var n={};for(var o in s.ns)n[o]=s.ns[o];var l=e.tags[e.tags.length-1]||e;e.opt.xmlns&&s.ns!==l.ns&&Object.keys(s.ns).forEach(function(t){var r=s.ns[t];k(e,"onclosenamespace",{prefix:t,uri:r})})}0===t&&(e.closedRoot=!0),e.tagName=e.attribValue=e.attribName="",e.attribList.length=0,e.state=_.TEXT}function I(e){var t,r=e.entity,i=r.toLowerCase(),a="";return e.ENTITIES[r]?e.ENTITIES[r]:e.ENTITIES[i]?e.ENTITIES[i]:("#"===(r=i).charAt(0)&&(a="x"===r.charAt(1)?(t=parseInt(r=r.slice(2),16)).toString(16):(t=parseInt(r=r.slice(1),10)).toString(10)),r=r.replace(/^0+/,""),isNaN(t)||a.toLowerCase()!==r)?(A(e,"Invalid character entity"),"&"+e.entity+";"):String.fromCodePoint(t)}function N(e,t){"<"===t?(e.state=_.OPEN_WAKA,e.startTagPosition=e.position):g(t)||(A(e,"Non-whitespace before first tag."),e.textNode=t,e.state=_.TEXT)}function R(e,t){var r="";return t<e.length&&(r=e.charAt(t)),r}return _=e.STATE,String.fromCodePoint||(i=String.fromCharCode,a=Math.floor,s=function(){var e,t,r=[],s=-1,n=arguments.length;if(!n)return"";for(var o="";++s<n;){var l=Number(arguments[s]);if(!isFinite(l)||l<0||l>1114111||a(l)!==l)throw RangeError("Invalid code point: "+l);l<=65535?r.push(l):(l-=65536,e=(l>>10)+55296,t=l%1024+56320,r.push(e,t)),(s+1===n||r.length>16384)&&(o+=i.apply(null,r),r.length=0)}return o},Object.defineProperty?Object.defineProperty(String,"fromCodePoint",{value:s,configurable:!0,writable:!0}):String.fromCodePoint=s),e}(),tJ=`The output should be formatted as a XML file.
1. Output should conform to the tags below. 
2. If tags are not given, make them on your own.
3. Remember to always open and close all the tags.

As an example, for the tags ["foo", "bar", "baz"]:
1. String "<foo>
   <bar>
      <baz></baz>
   </bar>
</foo>" is a well-formatted instance of the schema. 
2. String "<foo>
   <bar>
   </foo>" is a badly-formatted instance.
3. String "<foo>
   <tag>
   </tag>
</foo>" is a badly-formatted instance.

Here are the output tags:
\`\`\`
{tags}
\`\`\``;class tG extends tI{constructor(e){super(e),Object.defineProperty(this,"tags",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"lc_namespace",{enumerable:!0,configurable:!0,writable:!0,value:["langchain_core","output_parsers"]}),Object.defineProperty(this,"lc_serializable",{enumerable:!0,configurable:!0,writable:!0,value:!0}),this.tags=e?.tags}static lc_name(){return"XMLOutputParser"}_diff(e,t){if(t)return e?(0,tV.UD)(e,t):[{op:"replace",path:"",value:t}]}async parsePartialResult(e){return tX(e[0].text)}async parse(e){return tX(e)}getFormatInstructions(){return this.tags&&this.tags.length>0?tJ.replace("{tags}",this.tags?.join(", ")??""):tJ}}let tY=e=>e.split("\n").map(e=>e.replace(/^\s+/,"")).join("\n").trim(),tH=e=>{if(0===Object.keys(e).length)return{};let t={};return e.children.length>0?t[e.name]=e.children.map(tH):t[e.name]=e.text??void 0,t};function tX(e){let t=tY(e),r=tW.parser(!0),i={},a=[];r.onopentag=e=>{let t={name:e.name,attributes:e.attributes,children:[],text:"",isSelfClosing:e.isSelfClosing};a.length>0?a[a.length-1].children.push(t):i=t,e.isSelfClosing||a.push(t)},r.onclosetag=()=>{if(a.length>0){let e=a.pop();0===a.length&&e&&(i=e)}},r.ontext=e=>{if(a.length>0){let t=a[a.length-1];t.text+=e}},r.onattribute=e=>{a.length>0&&(a[a.length-1].attributes[e.name]=e.value)};let s=/```(xml)?(.*)```/s.exec(t),n=s?s[2]:t;return r.write(n).close(),i&&"?xml"===i.name&&(i=i.children[0]),tH(i)}var tZ=r(60799),tQ=r(58356),t0=r(46625);class t1 extends tZ.m{static lc_name(){return"PipelinePromptTemplate"}constructor(e){super({...e,inputVariables:[]}),Object.defineProperty(this,"pipelinePrompts",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"finalPrompt",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.pipelinePrompts=e.pipelinePrompts,this.finalPrompt=e.finalPrompt,this.inputVariables=this.computeInputValues()}computeInputValues(){let e=this.pipelinePrompts.map(e=>e.name);return[...new Set(this.pipelinePrompts.map(t=>t.prompt.inputVariables.filter(t=>!e.includes(t))).flat())]}static extractRequiredInputValues(e,t){return t.reduce((t,r)=>(t[r]=e[r],t),{})}async formatPipelinePrompts(e){let t=await this.mergePartialAndUserVariables(e);for(let{name:e,prompt:r}of this.pipelinePrompts){let i=t1.extractRequiredInputValues(t,r.inputVariables);r instanceof tQ.RZ?t[e]=await r.formatMessages(i):t[e]=await r.format(i)}return t1.extractRequiredInputValues(t,this.finalPrompt.inputVariables)}async formatPromptValue(e){return this.finalPrompt.formatPromptValue(await this.formatPipelinePrompts(e))}async format(e){return this.finalPrompt.format(await this.formatPipelinePrompts(e))}async partial(e){let t={...this};return t.inputVariables=this.inputVariables.filter(t=>!(t in e)),t.partialVariables={...this.partialVariables??{},...e},new t1(t)}serialize(){throw Error("Not implemented.")}_getPromptType(){return"pipeline"}}var t2=r(33892),t5=r(58111),t3=r(99366),t4=r(1025);function t8(e){return"object"==typeof e&&null!=e&&"withStructuredOutput"in e&&"function"==typeof e.withStructuredOutput}class t6 extends tQ.RZ{get lc_aliases(){return{...super.lc_aliases,schema:"schema_"}}constructor(e){super(e),Object.defineProperty(this,"schema",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"method",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"lc_namespace",{enumerable:!0,configurable:!0,writable:!0,value:["langchain_core","prompts","structured"]}),this.schema=e.schema,this.method=e.method}pipe(e){if(t8(e))return super.pipe(e.withStructuredOutput(this.schema));if("object"==typeof e&&null!=e&&"lc_id"in e&&Array.isArray(e.lc_id)&&"langchain_core/runnables/RunnableBinding"===e.lc_id.join("/")&&t8(e.bound))return super.pipe(new e8.fJ({bound:e.bound.withStructuredOutput(this.schema,...this.method?[{method:this.method}]:[]),kwargs:e.kwargs??{},config:e.config,configFactories:e.configFactories}));throw Error('Structured prompts need to be piped to a language model that supports the "withStructuredOutput()" method.')}static fromMessagesAndSchema(e,t,r){return t6.fromMessages(e,{schema:t,method:r})}}var t9=r(42298);class t7 extends e8.YN{constructor(e){super(e),Object.defineProperty(this,"callbacks",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"tags",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"metadata",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"verbose",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.callbacks=e?.callbacks,this.tags=e?.tags??[],this.metadata=e?.metadata??{},this.verbose=e?.verbose??!1}_getRelevantDocuments(e,t){throw Error("Not implemented!")}async invoke(e,t){return this.getRelevantDocuments(e,(0,to.ZI)(t))}async getRelevantDocuments(e,t){let r=(0,to.ZI)((0,eU.parseCallbackConfigArg)(t)),i=await eU.CallbackManager.configure(r.callbacks,this.callbacks,r.tags,this.tags,r.metadata,this.metadata,{verbose:this.verbose}),a=await i?.handleRetrieverStart(this.toJSON(),e,r.runId,void 0,void 0,void 0,r.runName);try{let t=await this._getRelevantDocuments(e,a);return await a?.handleRetrieverEnd(t),t}catch(e){throw await a?.handleRetrieverError(e),e}}}class re extends em.Serializable{}class rt extends re{constructor(){super(...arguments),Object.defineProperty(this,"lc_namespace",{enumerable:!0,configurable:!0,writable:!0,value:["langchain","storage"]}),Object.defineProperty(this,"store",{enumerable:!0,configurable:!0,writable:!0,value:{}})}async mget(e){return e.map(e=>this.store[e])}async mset(e){for(let[t,r]of e)this.store[t]=r}async mdelete(e){for(let t of e)delete this.store[t]}async *yieldKeys(e){for(let t of Object.keys(this.store))(void 0===e||t.startsWith(e))&&(yield t)}}var rr=r(28895),ri=r(48457);function ra(e){return void 0!==e&&Array.isArray(e.lc_namespace)}function rs(e){return void 0!==e&&e8.YN.isRunnable(e)&&"lc_name"in e.constructor&&"function"==typeof e.constructor.lc_name&&"RunnableToolLike"===e.constructor.lc_name()}function rn(e){return!!e&&"object"==typeof e&&"name"in e&&"schema"in e&&((0,tc.c9)(e.schema)||null!=e.schema&&"object"==typeof e.schema&&"type"in e.schema&&"string"==typeof e.schema.type&&["null","boolean","object","array","number","string"].includes(e.schema.type))}function ro(e){return rn(e)||rs(e)||ra(e)}class rl extends ti{get lc_namespace(){return["langchain","tools"]}constructor(e){super(e??{}),Object.defineProperty(this,"returnDirect",{enumerable:!0,configurable:!0,writable:!0,value:!1}),Object.defineProperty(this,"verboseParsingErrors",{enumerable:!0,configurable:!0,writable:!0,value:!1}),Object.defineProperty(this,"responseFormat",{enumerable:!0,configurable:!0,writable:!0,value:"content"}),this.verboseParsingErrors=e?.verboseParsingErrors??this.verboseParsingErrors,this.responseFormat=e?.responseFormat??this.responseFormat}async invoke(e,t){let r,i=(0,to.ZI)(t);return(0,ri.Ky)(e)?(r=e.args,i={...i,toolCall:e}):r=e,this.call(r,i)}async call(e,t,r){let i,a,s,n,o,l=(0,ri.Ky)(e)?e.args:e;if((0,tc.c9)(this.schema))try{i=await (0,tc.hZ)(this.schema,l)}catch(r){let t="Received tool input did not match expected schema";throw this.verboseParsingErrors&&(t=`${t}
Details: ${r.message}`),new ri.qe(t,JSON.stringify(e))}else{let t=(0,tP.tf)(l,this.schema);if(!t.valid){let r="Received tool input did not match expected schema";throw this.verboseParsingErrors&&(r=`${r}
Details: ${t.errors.map(e=>`${e.keywordLocation}: ${e.error}`).join("\n")}`),new ri.qe(r,JSON.stringify(e))}i=l}let c=(0,eU.parseCallbackConfigArg)(t),u=eU.CallbackManager.configure(c.callbacks,this.callbacks,c.tags||r,this.tags,c.metadata,this.metadata,{verbose:this.verbose}),h=await u?.handleToolStart(this.toJSON(),"string"==typeof e?e:JSON.stringify(e),c.runId,void 0,void 0,void 0,c.runName);delete c.runId;try{a=await this._call(i,h,c)}catch(e){throw await h?.handleToolError(e),e}if("content_and_artifact"===this.responseFormat)if(Array.isArray(a)&&2===a.length)[s,n]=a;else throw Error(`Tool response format is "content_and_artifact" but the output was not a two-tuple.
Result: ${JSON.stringify(a)}`);else s=a;(0,ri.Ky)(e)&&(o=e.id),!o&&(0,ri.hR)(c)&&(o=c.toolCall.id);let d=function(e){let{content:t,artifact:r,toolCallId:i}=e;return!i||(0,rr.fK)(t)?t:new rr.uf("string"==typeof t||Array.isArray(t)&&t.every(e=>"object"==typeof e)?{content:t,artifact:r,tool_call_id:i,name:e.name}:{content:function(e){try{return JSON.stringify(e,null,2)??""}catch(t){return`${e}`}}(t),artifact:r,tool_call_id:i,name:e.name})}({content:s,artifact:n,toolCallId:o,name:this.name});return await h?.handleToolEnd(d),d}}class rc extends rl{constructor(e){super(e),Object.defineProperty(this,"schema",{enumerable:!0,configurable:!0,writable:!0,value:q.z.object({input:q.z.string().optional()}).transform(e=>e.input)})}call(e,t){return super.call("string"==typeof e||null==e?{input:e}:e,t)}}class ru extends rc{static lc_name(){return"DynamicTool"}constructor(e){super(e),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"description",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"func",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.name=e.name,this.description=e.description,this.func=e.func,this.returnDirect=e.returnDirect??this.returnDirect}async call(e,t){let r=(0,eU.parseCallbackConfigArg)(t);return void 0===r.runName&&(r.runName=this.name),super.call(e,r)}async _call(e,t,r){return this.func(e,t,r)}}class rh extends rl{static lc_name(){return"DynamicStructuredTool"}constructor(e){super(e),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"description",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"func",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"schema",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.name=e.name,this.description=e.description,this.func=e.func,this.returnDirect=e.returnDirect??this.returnDirect,this.schema=e.schema}async call(e,t,r){let i=(0,eU.parseCallbackConfigArg)(t);return void 0===i.runName&&(i.runName=this.name),super.call(e,i,r)}_call(e,t,r){return this.func(e,t,r)}}class rd{getTools(){return this.tools}}function rp(e,t){let r=(0,tc.yQ)(t.schema),i=(0,tu.validatesOnlyStrings)(t.schema);if(!t.schema||r||i)return new ru({...t,description:t.description??t.schema?.description??`${t.name} tool`,func:async(t,r,i)=>new Promise((a,s)=>{let n=(0,to.tn)(i,{callbacks:r?.getChild()});V.Nx.runWithConfig((0,to.DY)(n),async()=>{try{a(e(t,n))}catch(e){s(e)}})})});let a=t.schema,s=t.description??t.schema.description??`${t.name} tool`;return new rh({...t,description:s,schema:a,func:async(t,r,i)=>new Promise((a,s)=>{let n=(0,to.tn)(i,{callbacks:r?.getChild()});V.Nx.runWithConfig((0,to.DY)(n),async()=>{try{a(e(t,n))}catch(e){s(e)}})})})}var rm=r(65164),rf=r(52832),rg=r(76242),rb=r(56301);class ry extends rm.BaseTracer{constructor(){super(),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"langchain_tracer"}),Object.defineProperty(this,"endpoint",{enumerable:!0,configurable:!0,writable:!0,value:(0,rb.getEnvironmentVariable)("LANGCHAIN_ENDPOINT")||"http://localhost:1984"}),Object.defineProperty(this,"headers",{enumerable:!0,configurable:!0,writable:!0,value:{"Content-Type":"application/json"}}),Object.defineProperty(this,"session",{enumerable:!0,configurable:!0,writable:!0,value:void 0});let e=(0,rb.getEnvironmentVariable)("LANGCHAIN_API_KEY");e&&(this.headers["x-api-key"]=e)}async newSession(e){let t={start_time:Date.now(),name:e},r=await this.persistSession(t);return this.session=r,r}async loadSession(e){let t=`${this.endpoint}/sessions?name=${e}`;return this._handleSessionResponse(t)}async loadDefaultSession(){let e=`${this.endpoint}/sessions?name=default`;return this._handleSessionResponse(e)}async convertV2RunToRun(e){let t,r=this.session??await this.loadDefaultSession(),i=e.serialized;if("llm"===e.run_type){let a=e.inputs.prompts?e.inputs.prompts:e.inputs.messages.map(e=>(0,ej.Sw)(e));t={uuid:e.id,start_time:e.start_time,end_time:e.end_time,execution_order:e.execution_order,child_execution_order:e.child_execution_order,serialized:i,type:e.run_type,session_id:r.id,prompts:a,response:e.outputs}}else if("chain"===e.run_type){let a=await Promise.all(e.child_runs.map(e=>this.convertV2RunToRun(e)));t={uuid:e.id,start_time:e.start_time,end_time:e.end_time,execution_order:e.execution_order,child_execution_order:e.child_execution_order,serialized:i,type:e.run_type,session_id:r.id,inputs:e.inputs,outputs:e.outputs,child_llm_runs:a.filter(e=>"llm"===e.type),child_chain_runs:a.filter(e=>"chain"===e.type),child_tool_runs:a.filter(e=>"tool"===e.type)}}else if("tool"===e.run_type){let a=await Promise.all(e.child_runs.map(e=>this.convertV2RunToRun(e)));t={uuid:e.id,start_time:e.start_time,end_time:e.end_time,execution_order:e.execution_order,child_execution_order:e.child_execution_order,serialized:i,type:e.run_type,session_id:r.id,tool_input:e.inputs.input,output:e.outputs?.output,action:JSON.stringify(i),child_llm_runs:a.filter(e=>"llm"===e.type),child_chain_runs:a.filter(e=>"chain"===e.type),child_tool_runs:a.filter(e=>"tool"===e.type)}}else throw Error(`Unknown run type: ${e.run_type}`);return t}async persistRun(e){let t,r;t="llm"===(r=void 0!==e.run_type?await this.convertV2RunToRun(e):e).type?`${this.endpoint}/llm-runs`:"chain"===r.type?`${this.endpoint}/chain-runs`:`${this.endpoint}/tool-runs`;let i=await fetch(t,{method:"POST",headers:this.headers,body:JSON.stringify(r)});i.ok||console.error(`Failed to persist run: ${i.status} ${i.statusText}`)}async persistSession(e){let t=`${this.endpoint}/sessions`,r=await fetch(t,{method:"POST",headers:this.headers,body:JSON.stringify(e)});return r.ok?{id:(await r.json()).id,...e}:(console.error(`Failed to persist session: ${r.status} ${r.statusText}, using default session.`),{id:1,...e})}async _handleSessionResponse(e){let t,r=await fetch(e,{method:"GET",headers:this.headers});if(!r.ok)return console.error(`Failed to load session: ${r.status} ${r.statusText}`),t={id:1,start_time:Date.now()},this.session=t,t;let i=await r.json();return 0===i.length?t={id:1,start_time:Date.now()}:[t]=i,this.session=t,t}}async function r_(e){let t=new ry;return e?await t.loadSession(e):await t.loadDefaultSession(),t}async function rw(){return new rg.LangChainTracer}var rv=r(28506);class rk extends rm.BaseTracer{constructor({exampleId:e}={}){super({_awaitHandler:!0}),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"run_collector"}),Object.defineProperty(this,"exampleId",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"tracedRuns",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.exampleId=e,this.tracedRuns=[]}async persistRun(e){let t={...e};t.reference_example_id=this.exampleId,this.tracedRuns.push(t)}}let rO=(e,t)=>e.reduce((e,r,i)=>{let a=Math.floor(i/t),s=e[a]||[];return e[a]=s.concat([r]),e},[]);function rx(e,t){let r="number"==typeof t?void 0:t;return{name:e.name,description:e.description,parameters:(0,tu.toJsonSchema)(e.schema),...r?.strict!==void 0?{strict:r.strict}:{}}}function rS(e,t){let r,i="number"==typeof t?void 0:t;return r=ro(e)?{type:"function",function:rx(e)}:e,i?.strict!==void 0&&(r.function.strict=i.strict),r}function rC(e,t){let r=0,i=0,a=0;for(let s=0;s<e.length;s++)r+=e[s]*t[s],i+=e[s]*e[s],a+=t[s]*t[s];return r/(Math.sqrt(i)*Math.sqrt(a))}function rA(e,t){let r=0;for(let i=0;i<e.length;i++)r+=e[i]*t[i];return r}function rT(e,t){return Math.sqrt(function(e,t){let r=0;for(let i=0;i<e.length;i++)r+=(e[i]-t[i])*(e[i]-t[i]);return r}(e,t))}function rP(e,t,r){if(0===e.length||0===e[0].length||0===t.length||0===t[0].length)return[[]];if(e[0].length!==t[0].length)throw Error(`Number of columns in X and Y must be the same. X has shape ${[e.length,e[0].length]} and Y has shape ${[t.length,t[0].length]}.`);return e.map(e=>t.map(t=>r(e,t)).map(e=>Number.isNaN(e)?0:e))}function rE(e,t=!1){let r=e.reduce((e,t)=>Math.max(e,rM(t).maxValue),0);return e.map(e=>e.map(e=>t?1-e/r:e/r))}function rj(e,t){return rP(e,t,rC)}function rI(e,t){return rP(e,t,rA)}function rN(e,t){return rP(e,t,rT)}function rR(e,t,r=.5,i=4){if(0>=Math.min(i,t.length))return[];let a=rj(Array.isArray(e[0])?e:[e],t)[0],s=rM(a).maxIndex,n=[t[s]],o=[s];for(;o.length<Math.min(i,t.length);){let e=-1/0,i=-1,s=rj(t,n);a.forEach((t,a)=>{if(o.includes(a))return;let n=r*t-(1-r)*Math.max(...s[a]);n>e&&(e=n,i=a)}),n.push(t[i]),o.push(i)}return o}function rM(e){if(0===e.length)return{maxIndex:-1,maxValue:NaN};let t=e[0],r=0;for(let i=1;i<e.length;i+=1)e[i]>t&&(r=i,t=e[i]);return{maxIndex:r,maxValue:t}}class rD extends t7{static lc_name(){return"VectorStoreRetriever"}get lc_namespace(){return["langchain_core","vectorstores"]}_vectorstoreType(){return this.vectorStore._vectorstoreType()}constructor(e){super(e),Object.defineProperty(this,"vectorStore",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"k",{enumerable:!0,configurable:!0,writable:!0,value:4}),Object.defineProperty(this,"searchType",{enumerable:!0,configurable:!0,writable:!0,value:"similarity"}),Object.defineProperty(this,"searchKwargs",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"filter",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.vectorStore=e.vectorStore,this.k=e.k??this.k,this.searchType=e.searchType??this.searchType,this.filter=e.filter,"mmr"===e.searchType&&(this.searchKwargs=e.searchKwargs)}async _getRelevantDocuments(e,t){if("mmr"===this.searchType){if("function"!=typeof this.vectorStore.maxMarginalRelevanceSearch)throw Error(`The vector store backing this retriever, ${this._vectorstoreType()} does not support max marginal relevance search.`);return this.vectorStore.maxMarginalRelevanceSearch(e,{k:this.k,filter:this.filter,...this.searchKwargs},t?.getChild("vectorstore"))}return this.vectorStore.similaritySearch(e,this.k,this.filter,t?.getChild("vectorstore"))}async addDocuments(e,t){return this.vectorStore.addDocuments(e,t)}}class r$ extends em.Serializable{constructor(e,t){super(t),Object.defineProperty(this,"lc_namespace",{enumerable:!0,configurable:!0,writable:!0,value:["langchain","vectorstores",this._vectorstoreType()]}),Object.defineProperty(this,"embeddings",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.embeddings=e}async delete(e){throw Error("Not implemented.")}async similaritySearch(e,t=4,r,i){return(await this.similaritySearchVectorWithScore(await this.embeddings.embedQuery(e),t,r)).map(e=>e[0])}async similaritySearchWithScore(e,t=4,r,i){return this.similaritySearchVectorWithScore(await this.embeddings.embedQuery(e),t,r)}static fromTexts(e,t,r,i){throw Error("the Langchain vectorstore implementation you are using forgot to override this, please report a bug")}static fromDocuments(e,t,r){throw Error("the Langchain vectorstore implementation you are using forgot to override this, please report a bug")}asRetriever(e,t,r,i,a,s){if("number"==typeof e)return new rD({vectorStore:this,k:e,filter:t,tags:[...i??[],this._vectorstoreType()],metadata:a,verbose:s,callbacks:r});{let t={vectorStore:this,k:e?.k,filter:e?.filter,tags:[...e?.tags??[],this._vectorstoreType()],metadata:e?.metadata,verbose:e?.verbose,callbacks:e?.callbacks,searchType:e?.searchType};return new rD(e?.searchType==="mmr"?{...t,searchKwargs:e.searchKwargs}:{...t})}}}class rL extends r${static load(e,t){throw Error("Not implemented")}}class rU extends tA{constructor(){super(...arguments),Object.defineProperty(this,"lc_namespace",{enumerable:!0,configurable:!0,writable:!0,value:["tests","fake"]})}getFormatInstructions(){return""}async parse(e){return e.split(",").map(e=>e.trim())}}class rF extends e8.YN{constructor(e){super(e),Object.defineProperty(this,"lc_namespace",{enumerable:!0,configurable:!0,writable:!0,value:["tests","fake"]}),Object.defineProperty(this,"returnOptions",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.returnOptions=e.returnOptions}async invoke(e,t){return this.returnOptions?t??{}:{input:e}}}class rq extends tg{constructor(e){super(e),Object.defineProperty(this,"response",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"thrownErrorString",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.response=e.response,this.thrownErrorString=e.thrownErrorString}_llmType(){return"fake"}async _call(e,t,r){if(this.thrownErrorString)throw Error(this.thrownErrorString);let i=this.response??e;return await r?.handleLLMNewToken(i),i}}class rz extends tg{constructor(e){super(e),Object.defineProperty(this,"sleep",{enumerable:!0,configurable:!0,writable:!0,value:50}),Object.defineProperty(this,"responses",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"thrownErrorString",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.sleep=e.sleep??this.sleep,this.responses=e.responses,this.thrownErrorString=e.thrownErrorString}_llmType(){return"fake"}async _call(e){if(this.thrownErrorString)throw Error(this.thrownErrorString);let t=this.responses?.[0];return this.responses=this.responses?.slice(1),t??e}async *_streamResponseChunks(e,t,r){if(this.thrownErrorString)throw Error(this.thrownErrorString);let i=this.responses?.[0];for(let t of(this.responses=this.responses?.slice(1),i??e))await new Promise(e=>setTimeout(e,this.sleep)),yield{text:t,generationInfo:{}},await r?.handleLLMNewToken(t)}}class rV extends tp{_combineLLMOutput(){return[]}_llmType(){return"fake"}async _generate(e,t,r){if(t?.stop?.length)return{generations:[{message:new eq.AIMessage(t.stop[0]),text:t.stop[0]}]};let i=e.map(e=>"string"==typeof e.content?e.content:JSON.stringify(e.content,null,2)).join("\n");return await r?.handleLLMNewToken(i),{generations:[{message:new eq.AIMessage(i),text:i}],llmOutput:{}}}}class rK extends tp{constructor({sleep:e=50,responses:t=[],chunks:r=[],toolStyle:i="openai",thrownErrorString:a,...s}){super(s),Object.defineProperty(this,"sleep",{enumerable:!0,configurable:!0,writable:!0,value:50}),Object.defineProperty(this,"responses",{enumerable:!0,configurable:!0,writable:!0,value:[]}),Object.defineProperty(this,"chunks",{enumerable:!0,configurable:!0,writable:!0,value:[]}),Object.defineProperty(this,"toolStyle",{enumerable:!0,configurable:!0,writable:!0,value:"openai"}),Object.defineProperty(this,"thrownErrorString",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"tools",{enumerable:!0,configurable:!0,writable:!0,value:[]}),this.sleep=e,this.responses=t,this.chunks=r,this.toolStyle=i,this.thrownErrorString=a}_llmType(){return"fake"}bindTools(e){let t=[...this.tools,...e],r=t.map(e=>{switch(this.toolStyle){case"openai":return{type:"function",function:{name:e.name,description:e.description,parameters:(0,tu.toJsonSchema)(e.schema)}};case"anthropic":return{name:e.name,description:e.description,input_schema:(0,tu.toJsonSchema)(e.schema)};case"bedrock":return{toolSpec:{name:e.name,description:e.description,inputSchema:(0,tu.toJsonSchema)(e.schema)}};case"google":return{name:e.name,description:e.description,parameters:(0,tu.toJsonSchema)(e.schema)};default:throw Error(`Unsupported tool style: ${this.toolStyle}`)}}),i="google"===this.toolStyle?[{functionDeclarations:r}]:r,a=new rK({sleep:this.sleep,responses:this.responses,chunks:this.chunks,toolStyle:this.toolStyle,thrownErrorString:this.thrownErrorString});return a.tools=t,a.withConfig({tools:i})}async _generate(e,t,r){if(this.thrownErrorString)throw Error(this.thrownErrorString);let i=this.responses?.[0]?.content??e[0].content??"";return{generations:[{text:"",message:new eq.AIMessage({content:i,tool_calls:this.chunks?.[0]?.tool_calls})}]}}async *_streamResponseChunks(e,t,r){if(this.thrownErrorString)throw Error(this.thrownErrorString);if(this.chunks?.length){for(let e of this.chunks){let t=new ts.ChatGenerationChunk({message:new eq.AIMessageChunk({content:e.content,tool_calls:e.tool_calls,additional_kwargs:e.additional_kwargs??{}}),text:e.content?.toString()??""});yield t,await r?.handleLLMNewToken(e.content,void 0,void 0,void 0,void 0,{chunk:t})}return}let i=this.responses?.[0]??new eq.AIMessage("string"==typeof e[0].content?e[0].content:"");for(let e of"string"==typeof i.content?i.content:""){await new Promise(e=>setTimeout(e,this.sleep));let t=new ts.ChatGenerationChunk({message:new eq.AIMessageChunk({content:e}),text:e});yield t,await r?.handleLLMNewToken(e,void 0,void 0,void 0,void 0,{chunk:t})}}}class rB extends t7{constructor(e){super(),Object.defineProperty(this,"lc_namespace",{enumerable:!0,configurable:!0,writable:!0,value:["test","fake"]}),Object.defineProperty(this,"output",{enumerable:!0,configurable:!0,writable:!0,value:[new e1.y({pageContent:"foo"}),new e1.y({pageContent:"bar"})]}),this.output=e?.output??this.output}async _getRelevantDocuments(e){return this.output}}class rW extends tp{static lc_name(){return"FakeListChatModel"}constructor(e){super(e),Object.defineProperty(this,"lc_serializable",{enumerable:!0,configurable:!0,writable:!0,value:!0}),Object.defineProperty(this,"responses",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"i",{enumerable:!0,configurable:!0,writable:!0,value:0}),Object.defineProperty(this,"sleep",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"emitCustomEvent",{enumerable:!0,configurable:!0,writable:!0,value:!1});let{responses:t,sleep:r,emitCustomEvent:i}=e;this.responses=t,this.sleep=r,this.emitCustomEvent=i??this.emitCustomEvent}_combineLLMOutput(){return[]}_llmType(){return"fake-list"}async _generate(e,t,r){if(await this._sleepIfRequested(),t?.thrownErrorString)throw Error(t.thrownErrorString);if(this.emitCustomEvent&&await r?.handleCustomEvent("some_test_event",{someval:!0}),t?.stop?.length)return{generations:[this._formatGeneration(t.stop[0])]};{let e=this._currentResponse();return this._incrementResponse(),{generations:[this._formatGeneration(e)],llmOutput:{}}}}_formatGeneration(e){return{message:new eq.AIMessage(e),text:e}}async *_streamResponseChunks(e,t,r){let i=this._currentResponse();for await(let e of(this._incrementResponse(),this.emitCustomEvent&&await r?.handleCustomEvent("some_test_event",{someval:!0}),i)){if(await this._sleepIfRequested(),t?.thrownErrorString)throw Error(t.thrownErrorString);let i=this._createResponseChunk(e);yield i,r?.handleLLMNewToken(e)}}async _sleepIfRequested(){void 0!==this.sleep&&await this._sleep()}async _sleep(){return new Promise(e=>{setTimeout(()=>e(),this.sleep)})}_createResponseChunk(e){return new ts.ChatGenerationChunk({message:new eq.AIMessageChunk({content:e}),text:e})}_currentResponse(){return this.responses[this.i]}_incrementResponse(){this.i<this.responses.length-1?this.i+=1:this.i=0}withStructuredOutput(e,t){return e8.jY.from(async e=>{let t=await this.invoke(e);if(t.tool_calls?.[0]?.args)return t.tool_calls[0].args;if("string"==typeof t.content)return JSON.parse(t.content);throw Error("No structured output found")})}}class rJ extends ez{constructor(){super(),Object.defineProperty(this,"lc_namespace",{enumerable:!0,configurable:!0,writable:!0,value:["langchain_core","message","fake"]}),Object.defineProperty(this,"messages",{enumerable:!0,configurable:!0,writable:!0,value:[]})}async getMessages(){return this.messages}async addMessage(e){this.messages.push(e)}async addUserMessage(e){this.messages.push(new eq.HumanMessage(e))}async addAIChatMessage(e){this.messages.push(new eq.AIMessage(e))}async clear(){this.messages=[]}}class rG extends eV{constructor(){super(),Object.defineProperty(this,"lc_namespace",{enumerable:!0,configurable:!0,writable:!0,value:["langchain_core","message","fake"]}),Object.defineProperty(this,"messages",{enumerable:!0,configurable:!0,writable:!0,value:[]})}async addMessage(e){this.messages.push(e)}async getMessages(){return this.messages}}class rY extends rm.BaseTracer{constructor(){super(),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"fake_tracer"}),Object.defineProperty(this,"runs",{enumerable:!0,configurable:!0,writable:!0,value:[]})}persistRun(e){return this.runs.push(e),Promise.resolve()}}class rH extends rl{constructor(e){super(e),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"description",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"schema",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.name=e.name,this.description=e.description,this.schema=e.schema}async _call(e,t){return JSON.stringify(e)}}class rX extends eJ{constructor(e){super(e??{})}embedDocuments(e){return Promise.resolve(e.map(()=>[.1,.2,.3,.4]))}embedQuery(e){return Promise.resolve([.1,.2,.3,.4])}}class rZ extends eJ{constructor(e){super(e??{}),Object.defineProperty(this,"vectorSize",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.vectorSize=e?.vectorSize??4}async embedDocuments(e){return Promise.all(e.map(e=>this.embedQuery(e)))}async embedQuery(e){let t=e,r=(t=t.toLowerCase().replaceAll(/[^a-z ]/g,"")).length%this.vectorSize,i=0===r?0:this.vectorSize-r,a=t.length+i,s=(t=t.padEnd(a," ")).length/this.vectorSize,n=[];for(let e=0;e<t.length;e+=s)n.push(t.slice(e,e+s));return n.map(e=>{let t=0;for(let r=0;r<e.length;r+=1)t+=" "===e?0:e.charCodeAt(r);return t%26/26})}}class rQ extends rm.BaseTracer{constructor(){super(),Object.defineProperty(this,"runPromiseResolver",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"runPromise",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"single_run_extractor"}),this.runPromise=new Promise(e=>{this.runPromiseResolver=e})}async persistRun(e){this.runPromiseResolver(e)}async extract(){return this.runPromise}}class r0 extends r${_vectorstoreType(){return"memory"}constructor(e,{similarity:t,...r}={}){super(e,r),Object.defineProperty(this,"memoryVectors",{enumerable:!0,configurable:!0,writable:!0,value:[]}),Object.defineProperty(this,"similarity",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.similarity=t??rC}async addDocuments(e){let t=e.map(({pageContent:e})=>e);return this.addVectors(await this.embeddings.embedDocuments(t),e)}async addVectors(e,t){let r=e.map((e,r)=>({content:t[r].pageContent,embedding:e,metadata:t[r].metadata}));this.memoryVectors=this.memoryVectors.concat(r)}async similaritySearchVectorWithScore(e,t,r){let i=this.memoryVectors.filter(e=>!r||r(new e1.y({metadata:e.metadata,pageContent:e.content})));return i.map((t,r)=>({similarity:this.similarity(e,t.embedding),index:r})).sort((e,t)=>e.similarity>t.similarity?-1:0).slice(0,t).map(e=>[new e1.y({metadata:i[e.index].metadata,pageContent:i[e.index].content}),e.similarity])}static async fromTexts(e,t,r,i){let a=[];for(let r=0;r<e.length;r+=1){let i=Array.isArray(t)?t[r]:t,s=new e1.y({pageContent:e[r],metadata:i});a.push(s)}return r0.fromDocuments(a,r,i)}static async fromDocuments(e,t,r){let i=new this(t,r);return await i.addDocuments(e),i}static async fromExistingIndex(e,t){return new this(e,t)}}var r1=r(97386);async function r2(e){let{optionalImportsMap:t={},optionalImportEntrypoints:r=[],importMap:i={},secretsMap:a={},path:s=["$"]}=this,n=s.join(".");if("object"==typeof e&&null!==e&&!Array.isArray(e)&&"lc"in e&&"type"in e&&"id"in e&&1===e.lc&&"secret"===e.type){let[t]=e.id;if(t in a)return a[t];{let e=(0,rb.getEnvironmentVariable)(t);if(e)return e;throw Error(`Missing key "${t}" for ${n} in load(secretsMap={})`)}}if("object"==typeof e&&null!==e&&!Array.isArray(e)&&"lc"in e&&"type"in e&&"id"in e&&1===e.lc&&"not_implemented"===e.type){let t=JSON.stringify(e);throw Error(`Trying to load an object that doesn't implement serialization: ${n} -> ${t}`)}if("object"==typeof e&&null!==e&&!Array.isArray(e)&&"lc"in e&&"type"in e&&"id"in e&&"kwargs"in e&&1===e.lc){let a=JSON.stringify(e),[o,...l]=e.id.slice().reverse(),c=l.reverse(),u=null,h=[c.join("/")];"langchain_community"===c[0]&&h.push(["langchain",...c.slice(1)].join("/"));let d=h.find(e=>e in t);if(ef.concat(r).includes(c.join("/"))||d)if(void 0!==d)u=await t[d];else throw Error(`Missing key "${c.join("/")}" for ${n} in load(optionalImportsMap={})`);else{let e,t;if("langchain"===c[0]||"langchain_core"===c[0])e=({langchain_core:j,langchain:i})[c[0]],c.shift();else throw Error(`Invalid namespace: ${n} -> ${a}`);if(0===c.length)throw Error(`Invalid namespace: ${n} -> ${a}`);do{if((t=c.join("__"))in e)break;c.pop()}while(c.length>0);t in e&&(u=e[t])}if("object"!=typeof u||null===u)throw Error(`Invalid namespace: ${n} -> ${a}`);let p=u[o]??Object.values(u).find(e=>"function"==typeof e&&(0,em.get_lc_unique_name)(e)===o);if("function"!=typeof p)throw Error(`Invalid identifer: ${n} -> ${a}`);let m=await r2.call({...this,path:[...s,"kwargs"]},e.kwargs);if("constructor"===e.type){let e=new p((0,r1.d4)(m,r1.O3,function(e){let t={};for(let r=e;r&&r.prototype;r=Object.getPrototypeOf(r))Object.assign(t,Reflect.get(r.prototype,"lc_aliases"));return Object.entries(t).reduce((e,[t,r])=>(e[r]=t,e),{})}(p)));return Object.defineProperty(e.constructor,"name",{value:o}),e}throw Error(`Invalid type: ${n} -> ${a}`)}if("object"==typeof e&&null!==e)if(Array.isArray(e))return Promise.all(e.map((e,t)=>r2.call({...this,path:[...s,`${t}`]},e)));else return Object.fromEntries(await Promise.all(Object.entries(e).map(async([e,t])=>[e,await r2.call({...this,path:[...s,e]},t)])));return e}async function r5(e,t){let r=JSON.parse(e);return r2.call({...t},r)}var r3=[],r4=[];function r8(e,t,r,i){var a=Object.getOwnPropertyDescriptor(i,r);void 0!==a.get?a.configurable?(Object.defineProperty(i,r,{value:e}),r3.push([i,r,t,a])):r4.push([t,r,e]):(i[r]=e,r3.push([i,r,t]))}async function r6(e){if(e&&"object"==typeof e)if(Array.isArray(e))return await Promise.all(e.map(e=>r6(e)));else{let t={};for(let[r,i]of Object.entries(e))t[r]=await r6(i);if(2===t.lc&&"undefined"===t.type)return;if(2===t.lc&&"constructor"===t.type&&Array.isArray(t.id))try{let e;switch(t.id[t.id.length-1]){case"Set":e=Set;break;case"Map":e=Map;break;case"RegExp":e=RegExp;break;case"Error":e=Error;break;default:return t}if(t.method)return e[t.method](...t.args||[]);return new e(...t.args||[])}catch(e){}else if(null!==t&&1===t.lc&&"constructor"===t.type&&Array.isArray(t.id))return r5(JSON.stringify(t));return t}return e}function r9(e,t,r,i){return{lc:2,type:"constructor",id:[e.name],method:t??null,args:r??[],kwargs:i??{}}}class r7{_dumps(e){return new TextEncoder().encode(function(e,t,r,i){void 0===i&&(i={depthLimit:Number.MAX_SAFE_INTEGER,edgesLimit:Number.MAX_SAFE_INTEGER}),function e(t,r,i,a,s,n,o){if(n+=1,"object"==typeof t&&null!==t){for(l=0;l<a.length;l++)if(a[l]===t)return void r8("[Circular]",t,r,s);if(void 0!==o.depthLimit&&n>o.depthLimit||void 0!==o.edgesLimit&&i+1>o.edgesLimit)return void r8("[...]",t,r,s);if(a.push(t),Array.isArray(t))for(l=0;l<t.length;l++)e(t[l],l,l,a,t,n,o);else{var l,c=Object.keys(t);for(l=0;l<c.length;l++){var u=c[l];e(t[u],u,l,a,t,n,o)}}a.pop()}}(e,"",0,[],void 0,0,i);try{var a;s=0===r4.length?JSON.stringify(e,t,void 0):JSON.stringify(e,(a=t,a=void 0!==a?a:function(e,t){return t},function(e,t){if(r4.length>0)for(var r=0;r<r4.length;r++){var i=r4[r];if(i[1]===e&&i[0]===t){t=i[2],r4.splice(r,1);break}}return a.call(this,e,t)}),void 0)}catch(e){return JSON.stringify("[unable to serialize, circular reference is too complex to analyze]")}finally{for(;0!==r3.length;){var s,n=r3.pop();4===n.length?Object.defineProperty(n[0],n[1],n[3]):n[0][n[1]]=n[2]}}return s}(e,(e,t)=>(function(e){if(void 0===e)return{lc:2,type:"undefined"};if(e instanceof Set||e instanceof Map)return r9(e.constructor,void 0,[Array.from(e)]);if(e instanceof RegExp)return r9(RegExp,void 0,[e.source,e.flags]);if(e instanceof Error)return r9(e.constructor,void 0,[e.message]);if(e?.lg_name==="Send")return{node:e.node,args:e.args};else return e})(t)))}dumpsTyped(e){return e instanceof Uint8Array?["bytes",e]:["json",this._dumps(e)]}async _loads(e){return r6(JSON.parse(e))}async loadsTyped(e,t){if("bytes"===e)return"string"==typeof t?new TextEncoder().encode(t):t;if("json"===e)return this._loads("string"==typeof t?t:new TextDecoder().decode(t));throw Error(`Unknown serialization type: ${e}`)}}function ie(e){if("object"!=typeof e||null===e)return e;let t=Array.isArray(e)?[]:{};for(let r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=ie(e[r]));return t}function it(){return{v:1,id:eh(-2),ts:new Date().toISOString(),channel_values:{},channel_versions:{},versions_seen:{},pending_sends:[]}}function ir(e){return{v:e.v,id:e.id,ts:e.ts,channel_values:{...e.channel_values},channel_versions:{...e.channel_versions},versions_seen:ie(e.versions_seen),pending_sends:[...e.pending_sends]}}function ii(e,t){return"number"==typeof e&&"number"==typeof t?Math.sign(e-t):String(e).localeCompare(String(t))}function ia(...e){return e.reduce((e,t,r)=>0===r?t:ii(e,t)>=0?e:t)}let is={__error__:-1,[ep]:-2,__interrupt__:-3,__resume__:-4};class io extends Error{constructor(e){super(e),this.name="InvalidNamespaceError"}}class il{async get(e,t){return(await this.batch([{namespace:e,key:t}]))[0]}async search(e,t={}){let{filter:r,limit:i=10,offset:a=0,query:s}=t;return(await this.batch([{namespacePrefix:e,filter:r,limit:i,offset:a,query:s}]))[0]}async put(e,t,r,i){if(0===e.length)throw new io("Namespace cannot be empty.");for(let t of e){if("string"!=typeof t)throw new io(`Invalid namespace label '${t}' found in ${e}. Namespace labels must be strings, but got ${typeof t}.`);if(t.includes("."))throw new io(`Invalid namespace label '${t}' found in ${e}. Namespace labels cannot contain periods ('.').`);if(""===t)throw new io(`Namespace labels cannot be empty strings. Got ${t} in ${e}`)}if("langgraph"===e[0])throw new io(`Root label for namespace cannot be "langgraph". Got: ${e}`);await this.batch([{namespace:e,key:t,value:r,index:i}])}async delete(e,t){await this.batch([{namespace:e,key:t,value:null}])}async listNamespaces(e={}){let{prefix:t,suffix:r,maxDepth:i,limit:a=100,offset:s=0}=e,n=[];return t&&n.push({matchType:"prefix",path:t}),r&&n.push({matchType:"suffix",path:r}),(await this.batch([{matchConditions:n.length?n:void 0,maxDepth:i,limit:a,offset:s}]))[0]}start(){}stop(){}}let ic=e=>"lg_name"in e&&"AsyncBatchedStore"===e.lg_name?e.store:e;class iu extends il{constructor(e){super(),Object.defineProperty(this,"lg_name",{enumerable:!0,configurable:!0,writable:!0,value:"AsyncBatchedStore"}),Object.defineProperty(this,"store",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"queue",{enumerable:!0,configurable:!0,writable:!0,value:new Map}),Object.defineProperty(this,"nextKey",{enumerable:!0,configurable:!0,writable:!0,value:0}),Object.defineProperty(this,"running",{enumerable:!0,configurable:!0,writable:!0,value:!1}),Object.defineProperty(this,"processingTask",{enumerable:!0,configurable:!0,writable:!0,value:null}),this.store=ic(e)}get isRunning(){return this.running}async batch(e){throw Error("The `batch` method is not implemented on `AsyncBatchedStore`.\n Instead, it calls the `batch` method on the wrapped store.\n If you are seeing this error, something is wrong.")}async get(e,t){return this.enqueueOperation({namespace:e,key:t})}async search(e,t){let{filter:r,limit:i=10,offset:a=0,query:s}=t||{};return this.enqueueOperation({namespacePrefix:e,filter:r,limit:i,offset:a,query:s})}async put(e,t,r){return this.enqueueOperation({namespace:e,key:t,value:r})}async delete(e,t){return this.enqueueOperation({namespace:e,key:t,value:null})}start(){this.running||(this.running=!0,this.processingTask=this.processBatchQueue())}async stop(){this.running=!1,this.processingTask&&await this.processingTask}enqueueOperation(e){return new Promise((t,r)=>{let i=this.nextKey;this.nextKey+=1,this.queue.set(i,{operation:e,resolve:t,reject:r})})}async processBatchQueue(){for(;this.running;){if(await new Promise(e=>{setTimeout(e,0)}),0===this.queue.size)continue;let e=new Map(this.queue);this.queue.clear();try{let t=Array.from(e.values()).map(({operation:e})=>e),r=await this.store.batch(t);e.forEach(({resolve:t},i)=>{let a=Array.from(e.keys()).indexOf(i);t(r[a])})}catch(t){e.forEach(({reject:e})=>{e(t)})}}}toJSON(){return{queue:this.queue,nextKey:this.nextKey,running:this.running,store:"[LangGraphStore]"}}}class ih{constructor(e){Object.defineProperty(this,"serde",{enumerable:!0,configurable:!0,writable:!0,value:new r7}),this.serde=e||this.serde}}function id(e){return null!=e&&!0===e.lg_is_channel}class ip{constructor(){Object.defineProperty(this,"ValueType",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"UpdateType",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"lg_is_channel",{enumerable:!0,configurable:!0,writable:!0,value:!0})}consume(){return!1}finish(){return!1}isAvailable(){try{return this.get(),!0}catch(e){if(e.name===et.unminifiable_name)return!1;throw e}}}function im(e,t){let r=Object.fromEntries(Object.entries(e).filter(([,e])=>id(e))),i={};for(let e in r)if(Object.prototype.hasOwnProperty.call(r,e)){let a=t.channel_values[e];i[e]=r[e].fromCheckpoint(a)}return i}function ig(e,t,r){let i;if(void 0===t)i=e.channel_values;else for(let e of(i={},Object.keys(t)))try{i[e]=t[e].checkpoint()}catch(e){if(e.name===et.unminifiable_name);else throw e}return{v:1,id:eh(r),ts:new Date().toISOString(),channel_values:i,channel_versions:{...e.channel_versions},versions_seen:ie(e.versions_seen),pending_sends:e.pending_sends??[]}}class ib extends ip{constructor(e,t){super(),Object.defineProperty(this,"lc_graph_name",{enumerable:!0,configurable:!0,writable:!0,value:"BinaryOperatorAggregate"}),Object.defineProperty(this,"value",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"operator",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"initialValueFactory",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.operator=e,this.initialValueFactory=t,this.value=t?.()}fromCheckpoint(e){let t=new ib(this.operator,this.initialValueFactory);return void 0!==e&&(t.value=e),t}update(e){let t=e;if(!t.length)return!1;for(let e of(void 0===this.value&&([this.value]=t,t=t.slice(1)),t))void 0!==this.value&&(this.value=this.operator(this.value,e));return!0}get(){if(void 0===this.value)throw new et;return this.value}checkpoint(){if(void 0===this.value)throw new et;return this.value}isAvailable(){return void 0!==this.value}}class iy extends ip{constructor(){super(...arguments),Object.defineProperty(this,"lc_graph_name",{enumerable:!0,configurable:!0,writable:!0,value:"LastValue"}),Object.defineProperty(this,"value",{enumerable:!0,configurable:!0,writable:!0,value:[]})}fromCheckpoint(e){let t=new iy;return void 0!==e&&(t.value=[e]),t}update(e){if(0===e.length)return!1;if(1!==e.length)throw new er("LastValue can only receive one value per step.",{lc_error_code:"INVALID_CONCURRENT_GRAPH_UPDATE"});return this.value=[e[e.length-1]],!0}get(){if(0===this.value.length)throw new et;return this.value[0]}checkpoint(){if(0===this.value.length)throw new et;return this.value[0]}isAvailable(){return 0!==this.value.length}}class i_ extends ip{constructor(){super(...arguments),Object.defineProperty(this,"lc_graph_name",{enumerable:!0,configurable:!0,writable:!0,value:"LastValueAfterFinish"}),Object.defineProperty(this,"value",{enumerable:!0,configurable:!0,writable:!0,value:[]}),Object.defineProperty(this,"finished",{enumerable:!0,configurable:!0,writable:!0,value:!1})}fromCheckpoint(e){let t=new i_;if(void 0!==e){let[r,i]=e;t.value=[r],t.finished=i}return t}update(e){return 0!==e.length&&(this.finished=!1,this.value=[e[e.length-1]],!0)}get(){if(0===this.value.length||!this.finished)throw new et;return this.value[0]}checkpoint(){if(0!==this.value.length)return[this.value[0],this.finished]}consume(){return!!this.finished&&(this.finished=!1,this.value=[],!0)}finish(){return!this.finished&&this.value.length>0&&(this.finished=!0,!0)}isAvailable(){return 0!==this.value.length&&this.finished}}let iw="__start__",iv="__end__",ik="__input__",iO="__error__",ix="__pregel_ns_writes",iS="__pregel_send",iC="__pregel_call",iA="__pregel_read",iT="__pregel_checkpointer",iP="__pregel_resuming",iE="__pregel_task_id",ij="__pregel_stream",iI="__pregel_scratchpad",iN="__pregel_previous",iR="checkpoint_ns",iM="checkpoint_map",iD="__pregel_abort_signals",i$="__interrupt__",iL="__resume__",iU="__no_writes__",iF="__return__",iq="__previous__",iz="__pregel_runtime_placeholder__",iV="langsmith:hidden",iK="__self__",iB="__pregel_tasks",iW="__pregel_push",iJ="__pregel_pull",iG="00000000-0000-0000-0000-000000000000",iY=[iV,ik,i$,iL,iO,iU,iB,iS,iA,iT,ij,iP,iE,iC,"__pregel_resume_value",iI,iN,iM,iR,"checkpoint_id"];function iH(e){return null!=e&&"string"==typeof e.node&&void 0!==e.args}class iX{constructor(e,t){Object.defineProperty(this,"lg_name",{enumerable:!0,configurable:!0,writable:!0,value:"Send"}),Object.defineProperty(this,"node",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"args",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.node=e,this.args=i1(t)}toJSON(){return{lg_name:this.lg_name,node:this.node,args:this.args}}}function iZ(e){return e instanceof iX}class iQ{constructor(e){Object.defineProperty(this,"lg_name",{enumerable:!0,configurable:!0,writable:!0,value:"Command"}),Object.defineProperty(this,"lc_direct_tool_output",{enumerable:!0,configurable:!0,writable:!0,value:!0}),Object.defineProperty(this,"graph",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"update",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"resume",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"goto",{enumerable:!0,configurable:!0,writable:!0,value:[]}),this.resume=e.resume,this.graph=e.graph,this.update=e.update,e.goto&&(this.goto=Array.isArray(e.goto)?i1(e.goto):[i1(e.goto)])}_updateAsTuples(){return this.update&&"object"==typeof this.update&&!Array.isArray(this.update)?Object.entries(this.update):Array.isArray(this.update)&&this.update.every(e=>Array.isArray(e)&&2===e.length&&"string"==typeof e[0])?this.update:[["__root__",this.update]]}toJSON(){let e;return e="string"==typeof this.goto?this.goto:iZ(this.goto)?this.goto.toJSON():this.goto?.map(e=>"string"==typeof e?e:e.toJSON()),{lg_name:this.lg_name,update:this.update,resume:this.resume,goto:e}}}function i0(e){return"object"==typeof e&&null!=e&&"lg_name"in e&&"Command"===e.lg_name}function i1(e,t=new Map){if(null!=e&&"object"==typeof e){let r;if(t.has(e))return t.get(e);if(Array.isArray(e))r=[],t.set(e,r),e.forEach((e,i)=>{r[i]=i1(e,t)});else if(!i0(e)||e instanceof iQ)if(!iH(e)||e instanceof iX)if(i0(e)||iZ(e))r=e,t.set(e,r);else if("lc_serializable"in e&&e.lc_serializable)r=e,t.set(e,r);else for(let[i,a]of(r={},t.set(e,r),Object.entries(e)))r[i]=i1(a,t);else r=new iX(e.node,e.args),t.set(e,r);else r=new iQ(e),t.set(e,r);return r}return e}Object.defineProperty(iQ,"PARENT",{enumerable:!0,configurable:!0,writable:!0,value:"__parent__"});class i2{constructor(e,t){Object.defineProperty(this,"runtime",{enumerable:!0,configurable:!0,writable:!0,value:!1}),Object.defineProperty(this,"config",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"_promises",{enumerable:!0,configurable:!0,writable:!0,value:[]}),Object.defineProperty(this,"lg_is_managed_value",{enumerable:!0,configurable:!0,writable:!0,value:!0}),this.config=e}static async initialize(e,t){throw Error("Not implemented")}async promises(){return Promise.all(this._promises)}addPromise(e){this._promises.push(e)}}class i5 extends Map{constructor(e){super(e?Array.from(e):void 0)}replaceRuntimeValues(e,t){if(0!==this.size&&t&&!Array.from(this.values()).every(e=>!e.runtime))if("object"!=typeof t||Array.isArray(t)){if("object"==typeof t&&"constructor"in t)for(let r of Object.getOwnPropertyNames(Object.getPrototypeOf(t)))try{let i=t[r];for(let[a,s]of this.entries())s.runtime&&s.call(e)===i&&(t[r]={[iz]:a})}catch(e){if(e.name!==TypeError.name)throw e}}else for(let[r,i]of Object.entries(t))for(let[a,s]of this.entries())s.runtime&&s.call(e)===i&&(t[r]={[iz]:a})}replaceRuntimePlaceholders(e,t){if(0!==this.size&&t&&!Array.from(this.values()).every(e=>!e.runtime)){if("object"!=typeof t||Array.isArray(t)){if("object"==typeof t&&"constructor"in t)for(let r of Object.getOwnPropertyNames(Object.getPrototypeOf(t)))try{let i=t[r];if("object"==typeof i&&null!==i&&iz in i){let a=this.get(i[iz]);a&&(t[r]=a.call(e))}}catch(e){if(e.name!==TypeError.name)throw e}}else for(let[r,i]of Object.entries(t))if("object"==typeof i&&null!==i&&iz in i){let a=i[iz];"string"==typeof a&&(t[r]=this.get(a)?.call(e))}}}}function i3(e){return"object"==typeof e&&!!e&&"cls"in e&&"params"in e}class i4 extends i2{call(){}static async initialize(e,t){return Promise.resolve(new i4(e))}}class i8{constructor(e){Object.defineProperty(this,"lc_graph_name",{enumerable:!0,configurable:!0,writable:!0,value:"AnnotationRoot"}),Object.defineProperty(this,"spec",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.spec=e}}let i6=function(e){return i3(e)?e:e?i9(e):new iy};function i9(e){return"object"==typeof e&&e&&"reducer"in e&&e.reducer?new ib(e.reducer,e.default):"object"==typeof e&&e&&"value"in e&&e.value?new ib(e.value,e.default):new iy}i6.Root=e=>new i8(e);var i7=r(82506),ae=r(73726);let at=["tags","metadata","callbacks","configurable"],ar=["tags","metadata","callbacks","runName","maxConcurrency","recursionLimit","configurable","runId","outputKeys","streamMode","store","writer","interruptBefore","interruptAfter","signal"];function ai(...e){let t={tags:[],metadata:{},callbacks:void 0,recursionLimit:25,configurable:{}},r=V.Nx.getRunnableConfig();if(void 0!==r){for(let[e,i]of Object.entries(r))if(void 0!==i)if(at.includes(e)){let r;r=Array.isArray(i)?[...i]:"object"==typeof i?"callbacks"===e&&"copy"in i&&"function"==typeof i.copy?i.copy():{...i}:i,t[e]=r}else t[e]=i}for(let r of e)if(void 0!==r)for(let[e,i]of Object.entries(r))void 0!==i&&ar.includes(e)&&(t[e]=i);for(let[e,r]of Object.entries(t.configurable))t.metadata=t.metadata??{},e.startsWith("__")||"string"!=typeof r&&"number"!=typeof r&&"boolean"!=typeof r||e in t.metadata||(t.metadata[e]=r);return t}function aa(e){return e.split("|").filter(e=>!e.match(/^\d+$/)).map(e=>e.split(":")[0]).join("|")}class as extends e8.YN{constructor(e){super(),Object.defineProperty(this,"lc_namespace",{enumerable:!0,configurable:!0,writable:!0,value:["langgraph"]}),Object.defineProperty(this,"func",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"tags",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"config",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"trace",{enumerable:!0,configurable:!0,writable:!0,value:!0}),Object.defineProperty(this,"recurse",{enumerable:!0,configurable:!0,writable:!0,value:!0}),this.name=e.name??e.func.name,this.func=e.func,this.config=e.tags?{tags:e.tags}:void 0,this.trace=e.trace??this.trace,this.recurse=e.recurse??this.recurse}async _tracedInvoke(e,t,r){return new Promise((i,a)=>{let s=(0,to.tn)(t,{callbacks:r?.getChild()});V.Nx.runWithConfig(s,async()=>{try{let t=await this.func(e,s);i(t)}catch(e){a(e)}})})}async invoke(e,t){let r,i=ai(t),a=(0,to.SV)(this.config,i);return(r=this.trace?await this._callWithConfig(this._tracedInvoke,e,a):await V.Nx.runWithConfig(a,async()=>this.func(e,a)),e8.YN.isRunnable(r)&&this.recurse)?await V.Nx.runWithConfig(a,async()=>r.invoke(e,a)):r}}function*an(e,t){if(void 0===t)yield*e;else for(let r of e)yield[t,r]}async function ao(e){let t=[];for await(let r of(await e))t.push(r);return t}function al(e){let t=[];for(let r of e)t.push(r);return t}function ac(e,t){return e?"configurable"in e?{...e,configurable:{...e.configurable,...t}}:{...e,configurable:t}:{configurable:t}}Symbol.for("LG_SKIP_WRITE");let au={[Symbol.for("LG_PASSTHROUGH")]:!0};function ah(e){return"object"==typeof e&&e?.[Symbol.for("LG_PASSTHROUGH")]!==void 0}let ad=Symbol("IS_WRITER");class ap extends as{constructor(e,t){super({writes:e,name:`ChannelWrite<${e.map(e=>iZ(e)?e.node:"channel"in e?e.channel:"...").join(",")}>`,tags:t,func:async(e,t)=>this._write(e,t??{})}),Object.defineProperty(this,"writes",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.writes=e}async _write(e,t){let r=this.writes.map(t=>af(t)&&ah(t.value)?{mapper:t.mapper,value:e}:am(t)&&ah(t.value)?{channel:t.channel,value:e,skipNone:t.skipNone,mapper:t.mapper}:t);return await ap.doWrite(t,r),e}static async doWrite(e,t){for(let e of t){if(am(e)){if(e.channel===iB)throw new er("Cannot write to the reserved channel TASKS");if(ah(e.value))throw new er("PASSTHROUGH value must be replaced")}if(af(e)&&ah(e.value))throw new er("PASSTHROUGH value must be replaced")}let r=[];for(let i of t)if(iZ(i))r.push([iB,i]);else if(af(i)){let t=await i.mapper.invoke(i.value,e);null!=t&&t.length>0&&r.push(...t)}else if(am(i)){let t=void 0!==i.mapper?await i.mapper.invoke(i.value,e):i.value;if("object"==typeof t&&t?.[Symbol.for("LG_SKIP_WRITE")]!==void 0||i.skipNone&&void 0===t)continue;r.push([i.channel,t])}else throw Error(`Invalid write entry: ${JSON.stringify(i)}`);(e.configurable?.[iS])(r)}static isWriter(e){return e instanceof ap||ad in e&&!!e[ad]}static registerWriter(e){return Object.defineProperty(e,ad,{value:!0})}}function am(e){return void 0!==e&&"string"==typeof e.channel}function af(e){return void 0!==e&&!am(e)&&e8.YN.isRunnable(e.mapper)}class ag extends as{constructor(e,t,r=!1){super({func:(e,t)=>ag.doRead(t,this.channel,this.fresh,this.mapper)}),Object.defineProperty(this,"lc_graph_name",{enumerable:!0,configurable:!0,writable:!0,value:"ChannelRead"}),Object.defineProperty(this,"channel",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"fresh",{enumerable:!0,configurable:!0,writable:!0,value:!1}),Object.defineProperty(this,"mapper",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.fresh=r,this.mapper=t,this.channel=e,this.name=Array.isArray(e)?`ChannelRead<${e.join(",")}>`:`ChannelRead<${e}>`}static doRead(e,t,r,i){let a=e.configurable?.[iA];if(!a)throw Error("Runnable is not configured with a read function. Make sure to call in the context of a Pregel process");return i?i(a(t,r)):a(t,r)}}let ab=new tl;class ay extends e8.fJ{constructor(e){let{channels:t,triggers:r,mapper:i,writers:a,bound:s,kwargs:n,metadata:o,retryPolicy:l,cachePolicy:c,tags:u,subgraphs:h,ends:d}=e,p=[...e.config?.tags?e.config.tags:[],...u??[]];super({...e,bound:e.bound??ab,config:{...e.config?e.config:{},tags:p}}),Object.defineProperty(this,"lc_graph_name",{enumerable:!0,configurable:!0,writable:!0,value:"PregelNode"}),Object.defineProperty(this,"channels",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"triggers",{enumerable:!0,configurable:!0,writable:!0,value:[]}),Object.defineProperty(this,"mapper",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"writers",{enumerable:!0,configurable:!0,writable:!0,value:[]}),Object.defineProperty(this,"bound",{enumerable:!0,configurable:!0,writable:!0,value:ab}),Object.defineProperty(this,"kwargs",{enumerable:!0,configurable:!0,writable:!0,value:{}}),Object.defineProperty(this,"metadata",{enumerable:!0,configurable:!0,writable:!0,value:{}}),Object.defineProperty(this,"tags",{enumerable:!0,configurable:!0,writable:!0,value:[]}),Object.defineProperty(this,"retryPolicy",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"cachePolicy",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"subgraphs",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"ends",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.channels=t,this.triggers=r,this.mapper=i,this.writers=a??this.writers,this.bound=s??this.bound,this.kwargs=n??this.kwargs,this.metadata=o??this.metadata,this.tags=p,this.retryPolicy=l,this.cachePolicy=c,this.subgraphs=h,this.ends=d}getWriters(){let e=[...this.writers];for(;e.length>1&&e[e.length-1]instanceof ap&&e[e.length-2]instanceof ap;){let t=e.slice(-2),r=t[0].writes.concat(t[1].writes);e[e.length-2]=new ap(r,t[0].config?.tags),e.pop()}return e}getNode(){let e=this.getWriters();if(this.bound!==ab||0!==e.length)return this.bound===ab&&1===e.length?e[0]:this.bound===ab?new e8.zZ({first:e[0],middle:e.slice(1,e.length-1),last:e[e.length-1],omitSequenceTags:!0}):e.length>0?new e8.zZ({first:this.bound,middle:e.slice(0,e.length-1),last:e[e.length-1],omitSequenceTags:!0}):this.bound}join(e){if(!Array.isArray(e))throw Error("channels must be a list");if("object"!=typeof this.channels)throw Error("all channels must be named when using .join()");return new ay({channels:{...this.channels,...Object.fromEntries(e.map(e=>[e,e]))},triggers:this.triggers,mapper:this.mapper,writers:this.writers,bound:this.bound,kwargs:this.kwargs,config:this.config,retryPolicy:this.retryPolicy,cachePolicy:this.cachePolicy})}pipe(e){return new ay(ap.isWriter(e)?{channels:this.channels,triggers:this.triggers,mapper:this.mapper,writers:[...this.writers,e],bound:this.bound,config:this.config,kwargs:this.kwargs,retryPolicy:this.retryPolicy,cachePolicy:this.cachePolicy}:this.bound===ab?{channels:this.channels,triggers:this.triggers,mapper:this.mapper,writers:this.writers,bound:(0,e8.Bp)(e),config:this.config,kwargs:this.kwargs,retryPolicy:this.retryPolicy,cachePolicy:this.cachePolicy}:{channels:this.channels,triggers:this.triggers,mapper:this.mapper,writers:this.writers,bound:this.bound.pipe(e),config:this.config,kwargs:this.kwargs,retryPolicy:this.retryPolicy,cachePolicy:this.cachePolicy})}}function a_(e,t,r=!0,i=!1){try{return e[t].get()}catch(e){if(e.name===et.unminifiable_name){if(i)return e;else if(r)return null}throw e}}function aw(e,t,r=!0){if(!Array.isArray(t))return a_(e,t);{let i={};for(let a of t)try{i[a]=a_(e,a,!r)}catch(e){if(e.name===et.unminifiable_name)continue}return i}}function*av(e,t){if(null!=t)if(Array.isArray(e)&&"object"==typeof t&&!Array.isArray(t))for(let r in t)e.includes(r)&&(yield[r,t[r]]);else if(Array.isArray(e))throw Error('Input chunk must be an object when "inputChannels" is an array');else yield[e,t]}function*ak(e,t,r){Array.isArray(e)?(!0===t||t.find(([t,r])=>e.includes(t)))&&(yield aw(r,e)):(!0===t||t.some(([t,r])=>t===e))&&(yield a_(r,e))}function*aO(e,t,r){let i,a=t.filter(([e,t])=>(void 0===e.config||!e.config.tags?.includes(iV))&&t[0][0]!==iO&&t[0][0]!==i$);if(!a.length)return;i=a.some(([e])=>e.writes.some(([e,t])=>e===iF))?a.flatMap(([e])=>e.writes.filter(([e,t])=>e===iF).map(([t,r])=>[e.name,r])):Array.isArray(e)?a.flatMap(([t])=>{let{writes:r}=t,i={};for(let[t]of r)e.includes(t)&&(i[t]=(i[t]||0)+1);return Object.values(i).some(e=>e>1)?r.filter(([t])=>e.includes(t)).map(([e,r])=>[t.name,{[e]:r}]):[[t.name,Object.fromEntries(r.filter(([t])=>e.includes(t)))]]}):a.flatMap(([t])=>t.writes.filter(([t,r])=>t===e).map(([e,r])=>[t.name,r]));let s={};for(let[e,t]of i)e in s||(s[e]=[]),s[e].push(t);let n={};for(let e in s)if(1===s[e].length){let[t]=s[e];n[e]=t}else n[e]=s[e];r&&(n.__metadata__={cached:r}),yield n}class ax{constructor({func:e,name:t,input:r,retry:i,cache:a,callbacks:s}){Object.defineProperty(this,"func",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"input",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"retry",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"cache",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"callbacks",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"__lg_type",{enumerable:!0,configurable:!0,writable:!0,value:"call"}),this.func=e,this.name=t,this.input=r,this.retry=i,this.cache=a,this.callbacks=s}}function aS(e){let t,r=Object.values(e),i=r.length>0?typeof r[0]:void 0;return"number"===i?t=0:"string"===i&&(t=""),t}function aC(e,t){if(!(Object.keys(e).length>0))return t;{let r=aS(t);return Object.fromEntries(Object.entries(t).filter(([t,i])=>i>(e[t]??r)))}}function aA(e,t){return null===e?{configurable:t}:e?.configurable===void 0?{...e,configurable:t}:{...e,configurable:{...e.configurable,...t}}}function aT(e,t){let r=t?.parents??{};return Object.keys(r).length>0?aA(e,{[iM]:{...r,[e.configurable?.checkpoint_ns??""]:e.configurable?.checkpoint_id}}):e}function aP(...e){if(1===e.length)return e[0];let t=new AbortController,r=()=>{t.abort(),e.forEach(e=>e.removeEventListener("abort",r))};return e.forEach(e=>e.addEventListener("abort",r)),e.some(e=>e.aborted)&&t.abort(),t.signal}let aE=(e,t)=>{if(e||t)return e?t?Array.isArray(e)&&Array.isArray(t)?[...e,...t]:Array.isArray(e)?[...e,t]:Array.isArray(t)?[e,...t]:[e,t]:e:t},aj=e=>void 0!==e?e+1:1;function aI(e,t,r){let i,a=Object.values(e.channel_versions),s=a.length>0?typeof a[0]:void 0;"number"===s?i=0:"string"===s&&(i="");let n=e.versions_seen[i$]??{},o=Object.entries(e.channel_versions).some(([e,t])=>t>(n[e]??i)),l=r.some(e=>"*"===t?!e.config?.tags?.includes(iV):t.includes(e.name));return o&&l}function aN(e,t,r,i,a,s,n=!1){let o,l=[],c=new Set;if(Array.isArray(s))l=s.filter(e=>i.get(e)),c=new Set((s=s.filter(e=>!i.get(e))).filter(e=>a.writes.some(([t,r])=>t===e)));else{for(let[e]of a.writes)if(e===s){c=new Set([e]);break}c=c||new Set}if(n&&c.size>0){let e=Object.fromEntries(Object.entries(r).filter(([e,t])=>c.has(e))),i=ig(t,e,-1),n=im(e,i);aD(ir(i),n,[a],void 0,void 0),o=aw({...r,...n},s)}else o=aw(r,s);if(l.length>0)for(let t of l){let r=i.get(t);if(r){let i=r.call(e);o[t]=i}}return o}function aR(e,t,r,i,a){for(let[t,s]of a)if([iW,iB].includes(t)&&null!=s){if(!iZ(s))throw new er(`Invalid packet type, expected SendProtocol, got ${JSON.stringify(s)}`);if(!(s.node in r))throw new er(`Invalid node name "${s.node}" in Send packet`);i.replaceRuntimeValues(e,s.args)}t(a)}let aM=new Set([iU,iW,iL,i$,iF,iO]);function aD(e,t,r,i,a){let s;r.sort((e,t)=>{let r=e.path?.slice(0,3)||[],i=t.path?.slice(0,3)||[];for(let e=0;e<Math.min(r.length,i.length);e+=1){if(r[e]<i[e])return -1;if(r[e]>i[e])return 1}return r.length-i.length});let n=r.some(e=>e.triggers.length>0),o=Object.fromEntries(Object.entries(t).filter(([e,t])=>id(t)));for(let t of r)for(let r of(e.versions_seen[t.name]??={},t.triggers))r in e.channel_versions&&(e.versions_seen[t.name][r]=e.channel_versions[r]);for(let t of(Object.keys(e.channel_versions).length>0&&(s=ia(...Object.values(e.channel_versions))),new Set(r.flatMap(e=>e.triggers).filter(e=>!iY.includes(e)))))t in o&&o[t].consume()&&void 0!==i&&(e.channel_versions[t]=i(s,o[t]));e.pending_sends?.length&&n&&(e.pending_sends=[]);let l={},c={};for(let t of r)for(let[r,i]of t.writes)aM.has(r)||(r===iB?e.pending_sends.push({node:i.node,args:i.args}):r in o?(l[r]??=[],l[r].push(i)):(c[r]??=[],c[r].push(i)));s=void 0,Object.keys(e.channel_versions).length>0&&(s=ia(...Object.values(e.channel_versions)));let u=new Set;for(let[t,r]of Object.entries(l))if(t in o){let a;try{a=o[t].update(r)}catch(e){if(e.name===er.unminifiable_name){let i=new er(`Invalid update for channel "${t}" with values ${JSON.stringify(r)}: ${e.message}`);throw i.lc_error_code=e.lc_error_code,i}throw e}a&&void 0!==i&&(e.channel_versions[t]=i(s,o[t]),o[t].isAvailable()&&u.add(t))}if(n)for(let t of Object.keys(o))o[t].isAvailable()&&!u.has(t)&&o[t].update([])&&void 0!==i&&(e.channel_versions[t]=i(s,o[t]),o[t].isAvailable()&&u.add(t));if(n&&0===e.pending_sends.length&&!Object.keys(a??{}).some(e=>u.has(e)))for(let t of Object.keys(o))o[t].finish()&&void 0!==i&&(e.channel_versions[t]=i(s,o[t]),o[t].isAvailable()&&u.add(t));return c}function a$(e,t,r,i,a,s,n,o){let l={};for(let c=0;c<e.pending_sends.length;c+=1){let u=aL([iW,c],e,t,r,i,a,s,n,o);void 0!==u&&(l[u.id]=u)}for(let c of Object.keys(r)){let u=aL([iJ,c],e,t,r,i,a,s,n,o);void 0!==u&&(l[u.id]=u)}return l}function aL(e,t,r,i,a,s,n,o,l){var c;let{step:u,checkpointer:h,manager:d}=l,p=n.configurable??{},m=p.checkpoint_ns??"";if(e[0]===iW&&"object"==typeof(c=e[e.length-1])&&null!==c&&"__lg_type"in c&&"call"===c.__lg_type){let c=e[e.length-1],f=function(e,t){let r=new as({func:e=>t(...e),name:e,trace:!1,recurse:!1});return new e8.zZ({name:e,first:r,last:new ap([{channel:iF,value:au}],[iV])})}(c.name,c.func),g=[iW],b=""===m?c.name:`${m}|${c.name}`,y=ed(JSON.stringify([b,u.toString(),c.name,iW,e[1],e[2]]),t.id),_=`${b}:${y}`,w={langgraph_step:u,langgraph_node:c.name,langgraph_triggers:g,langgraph_path:e.slice(0,3),langgraph_checkpoint_ns:_};if(!o)return{id:y,name:c.name,interrupts:[],path:e.slice(0,3)};{let o=[];return{name:c.name,input:c.input,proc:f,writes:o,config:(0,to.tn)((0,to.SV)(n,{metadata:w,store:l.store??n.store}),{runName:c.name,callbacks:d?.getChild(`graph:step:${u}`),configurable:{[iE]:y,[iS]:e=>aR(u,e=>o.push(...e),i,s,e),[iA]:(r,i=!1)=>aN(u,t,a,s,{name:c.name,writes:o,triggers:g,path:e.slice(0,3)},r,i),[iT]:h??p[iT],[iM]:{...p[iM],[m]:t.id},[iI]:aU({pendingWrites:r??[],taskId:y,currentTaskInput:c.input}),[iN]:t.channel_values[iq],checkpoint_id:void 0,checkpoint_ns:_}}),triggers:g,retry_policy:c.retry,cache_key:c.cache?{key:(c.cache.keyFunc??JSON.stringify)([c.input]),ns:[ix,c.name??"__dynamic__"],ttl:c.cache.ttl}:void 0,id:y,path:e.slice(0,3),writers:[]}}}if(e[0]===iW){let c="number"==typeof e[1]?e[1]:parseInt(e[1],10);if(c>=t.pending_sends.length)return;let f=iH(t.pending_sends[c])&&!iZ(t.pending_sends[c])?new iX(t.pending_sends[c].node,t.pending_sends[c].args):t.pending_sends[c];if(!iH(f))return void console.warn(`Ignoring invalid packet ${JSON.stringify(f)} in pending sends.`);if(!(f.node in i))return void console.warn(`Ignoring unknown node name ${f.node} in pending sends.`);let g=[iW],b=""===m?f.node:`${m}|${f.node}`,y=ed(JSON.stringify([b,u.toString(),f.node,iW,c.toString()]),t.id),_=`${b}:${y}`,w={langgraph_step:u,langgraph_node:f.node,langgraph_triggers:g,langgraph_path:e.slice(0,3),langgraph_checkpoint_ns:_};if(!o)return{id:y,name:f.node,interrupts:[],path:e};{let o=i[f.node],c=o.getNode();if(void 0!==c){s.replaceRuntimePlaceholders(u,f.args),void 0!==o.metadata&&(w={...w,...o.metadata});let b=[];return{name:f.node,input:f.args,proc:c,subgraphs:o.subgraphs,writes:b,config:(0,to.tn)((0,to.SV)(n,{metadata:w,tags:o.tags,store:l.store??n.store}),{runName:f.node,callbacks:d?.getChild(`graph:step:${u}`),configurable:{[iE]:y,[iS]:e=>aR(u,e=>b.push(...e),i,s,e),[iA]:(r,i=!1)=>aN(u,t,a,s,{name:f.node,writes:b,triggers:g,path:e},r,i),[iT]:h??p[iT],[iM]:{...p[iM],[m]:t.id},[iI]:aU({pendingWrites:r??[],taskId:y,currentTaskInput:f.args}),[iN]:t.channel_values[iq],checkpoint_id:void 0,checkpoint_ns:_}}),triggers:g,retry_policy:o.retryPolicy,cache_key:o.cachePolicy?{key:(o.cachePolicy.keyFunc??JSON.stringify)([f.args]),ns:[ix,o.name??"__dynamic__",f.node],ttl:o.cachePolicy.ttl}:void 0,id:y,path:e,writers:o.getWriters()}}}}else if(e[0]===iJ){let c=e[1].toString(),f=i[c];if(void 0===f)return;if(r?.length){let e=ed(JSON.stringify([""===m?c:`${m}|${c}`,u.toString(),c,iJ,c]),t.id);if(r.some(t=>t[0]===e&&t[1]!==iO))return}let g=aS(t.channel_versions);if(void 0===g)return;let b=t.versions_seen[c]??{},y=f.triggers.filter(e=>{let r=a_(a,e,!1,!0);return!(r instanceof Error&&r.name===et.unminifiable_name)&&(t.channel_versions[e]??g)>(b[e]??g)}).sort();if(y.length>0){let g=function(e,t,r,i,a){let s;if("object"!=typeof t.channels||Array.isArray(t.channels))if(Array.isArray(t.channels)){let e=!1;for(let r of t.channels)try{s=a_(i,r,!1),e=!0;break}catch(e){if(e.name===et.unminifiable_name)continue;throw e}if(!e)return}else throw Error(`Invalid channels type, expected list or dict, got ${t.channels}`);else for(let[a,n]of(s={},Object.entries(t.channels)))if(t.triggers.includes(n))try{s[a]=a_(i,n,!1)}catch(e){if(e.name===et.unminifiable_name)return;throw e}else if(n in i)try{s[a]=a_(i,n,!1)}catch(e){if(e.name===et.unminifiable_name)continue;throw e}else s[a]=r.get(a)?.call(e);return a&&void 0!==t.mapper&&(s=t.mapper(s)),s}(u,f,s,a,o);if(void 0===g)return;let b=""===m?c:`${m}|${c}`,_=ed(JSON.stringify([b,u.toString(),c,iJ,y]),t.id),w=`${b}:${_}`,v={langgraph_step:u,langgraph_node:c,langgraph_triggers:y,langgraph_path:e,langgraph_checkpoint_ns:w};if(!o)return{id:_,name:c,interrupts:[],path:e};{let o=f.getNode();if(void 0!==o){void 0!==f.metadata&&(v={...v,...f.metadata});let b=[];return{name:c,input:g,proc:o,subgraphs:f.subgraphs,writes:b,config:(0,to.tn)((0,to.SV)(n,{metadata:v,tags:f.tags,store:l.store??n.store}),{runName:c,callbacks:d?.getChild(`graph:step:${u}`),configurable:{[iE]:_,[iS]:e=>aR(u,e=>{b.push(...e)},i,s,e),[iA]:(r,i=!1)=>aN(u,t,a,s,{name:c,writes:b,triggers:y,path:e},r,i),[iT]:h??p[iT],[iM]:{...p[iM],[m]:t.id},[iI]:aU({pendingWrites:r??[],taskId:_,currentTaskInput:g}),[iN]:t.channel_values[iq],checkpoint_id:void 0,checkpoint_ns:w}}),triggers:y,retry_policy:f.retryPolicy,cache_key:f.cachePolicy?{key:(f.cachePolicy.keyFunc??JSON.stringify)([g]),ns:[ix,f.name??"__dynamic__",c],ttl:f.cachePolicy.ttl}:void 0,id:_,path:e,writers:f.getWriters()}}}}}}function aU({pendingWrites:e,taskId:t,currentTaskInput:r}){let i=e.find(([e,t])=>e===iG&&t===iL)?.[2],a={callCounter:0,interruptCounter:-1,resume:e.filter(([e,r])=>e===t&&r===iL).flatMap(([e,t,r])=>r),nullResume:i,subgraphCounter:0,currentTaskInput:r,consumeNullResume:()=>{if(a.nullResume)return delete a.nullResume,e.splice(e.findIndex(([e,t])=>e===iG&&t===iL),1),i}};return a}function aF(e){return"lg_is_pregel"in e&&!0===e.lg_is_pregel}function aq(e){let t=[e];for(let e of t)if(aF(e))return e;else"steps"in e&&Array.isArray(e.steps)&&t.push(...e.steps)}let az={blue:{start:"\x1b[34m",end:"\x1b[0m"},green:{start:"\x1b[32m",end:"\x1b[0m"},yellow:{start:"\x1b[33;1m",end:"\x1b[0m"}},aV=(e,t)=>`${e.start}${t}${e.end}`;function*aK(e,t){let r=new Date().toISOString();for(let{id:i,name:a,input:s,config:n,triggers:o,writes:l}of t){if(n?.tags?.includes(iV))continue;let t=l.filter(([e,t])=>e===i&&t===i$).map(([,e])=>e);yield{type:"task",timestamp:r,step:e,payload:{id:i,name:a,input:s,triggers:o,interrupts:t}}}}function aB(e,t,r){return e.map(e=>{let i=t.find(([t,r])=>t===e.id&&r===iO)?.[2],a=t.filter(([t,r])=>t===e.id&&r===i$).map(([,,e])=>e);if(i)return{id:e.id,name:e.name,path:e.path,error:i,interrupts:a};let s=r?.[e.id];return{id:e.id,name:e.name,path:e.path,interrupts:a,...void 0!==s?{state:s}:{}}})}function aW(e,t){let r=t.length;console.log([`${aV(az.blue,`[${e}:tasks]`)}`,`\x1b[1m Starting step ${e} with ${r} task${1===r?"":"s"}:\x1b[0m
`,t.map(e=>`- ${aV(az.green,String(e.name))} -> ${JSON.stringify(e.input,null,2)}`).join("\n")].join(""))}class aJ extends tn.IterableReadableStream{constructor(e,t){let r=e.getReader(),i=t??new AbortController;super({start:e=>(function t(){return r.read().then(({done:r,value:i})=>r?void e.close():(e.enqueue(i),t()))})()}),Object.defineProperty(this,"_abortController",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"_reader",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this._abortController=i,this._reader=r}async cancel(e){this._abortController.abort(e),this._reader.releaseLock()}get signal(){return this._abortController.signal}}class aG extends tn.IterableReadableStream{get closed(){return this._closed}constructor(e){let t,r=new Promise(e=>{t=e});super({start:e=>{t(e)}}),Object.defineProperty(this,"modes",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"controller",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"passthroughFn",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"_closed",{enumerable:!0,configurable:!0,writable:!0,value:!1}),r.then(e=>{this.controller=e}),this.passthroughFn=e.passthroughFn,this.modes=e.modes}push(e){this.passthroughFn?.(e),this.controller.enqueue(e)}close(){try{this.controller.close()}catch(e){}finally{this._closed=!0}}error(e){this.controller.error(e)}}let aY=Symbol.for("INPUT_DONE"),aH=Symbol.for("INPUT_RESUMING");class aX extends ih{constructor(e){super(),Object.defineProperty(this,"cache",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"queue",{enumerable:!0,configurable:!0,writable:!0,value:Promise.resolve()}),this.cache=e}async get(e){return this.enqueueOperation("get",e)}async set(e){return this.enqueueOperation("set",e)}async clear(e){return this.enqueueOperation("clear",e)}async stop(){await this.queue}enqueueOperation(e,...t){let r=this.queue.then(()=>this.cache[e](...t));return this.queue=r.then(()=>void 0,()=>void 0),r}}class aZ{get isResuming(){let e=0!==Object.keys(this.checkpoint.channel_versions).length,t=this.config.configurable?.[iP]!==void 0&&this.config.configurable?.[iP],r=null===this.input||void 0===this.input,i=i0(this.input)&&null!=this.input.resume,a=this.input===aH;return e&&(t||r||i||a)}constructor(e){Object.defineProperty(this,"input",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"output",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"config",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"checkpointer",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"checkpointerGetNextVersion",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"channels",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"managed",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"checkpoint",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"checkpointConfig",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"checkpointMetadata",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"checkpointNamespace",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"checkpointPendingWrites",{enumerable:!0,configurable:!0,writable:!0,value:[]}),Object.defineProperty(this,"checkpointPreviousVersions",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"step",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"stop",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"outputKeys",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"streamKeys",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"nodes",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"skipDoneTasks",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"prevCheckpointConfig",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"status",{enumerable:!0,configurable:!0,writable:!0,value:"pending"}),Object.defineProperty(this,"tasks",{enumerable:!0,configurable:!0,writable:!0,value:{}}),Object.defineProperty(this,"stream",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"checkpointerPromises",{enumerable:!0,configurable:!0,writable:!0,value:[]}),Object.defineProperty(this,"isNested",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"_checkpointerChainedPromise",{enumerable:!0,configurable:!0,writable:!0,value:Promise.resolve()}),Object.defineProperty(this,"store",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"cache",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"manager",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"interruptAfter",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"interruptBefore",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"toInterrupt",{enumerable:!0,configurable:!0,writable:!0,value:[]}),Object.defineProperty(this,"debug",{enumerable:!0,configurable:!0,writable:!0,value:!1}),Object.defineProperty(this,"triggerToNodes",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.input=e.input,this.checkpointer=e.checkpointer,void 0!==this.checkpointer?this.checkpointerGetNextVersion=this.checkpointer.getNextVersion.bind(this.checkpointer):this.checkpointerGetNextVersion=aj,this.checkpoint=e.checkpoint,this.checkpointMetadata=e.checkpointMetadata,this.checkpointPreviousVersions=e.checkpointPreviousVersions,this.channels=e.channels,this.managed=e.managed,this.checkpointPendingWrites=e.checkpointPendingWrites,this.step=e.step,this.stop=e.stop,this.config=e.config,this.checkpointConfig=e.checkpointConfig,this.isNested=e.isNested,this.manager=e.manager,this.outputKeys=e.outputKeys,this.streamKeys=e.streamKeys,this.nodes=e.nodes,this.skipDoneTasks=e.skipDoneTasks,this.store=e.store,this.cache=e.cache?new aX(e.cache):void 0,this.stream=e.stream,this.checkpointNamespace=e.checkpointNamespace,this.prevCheckpointConfig=e.prevCheckpointConfig,this.interruptAfter=e.interruptAfter,this.interruptBefore=e.interruptBefore,this.debug=e.debug,this.triggerToNodes=e.triggerToNodes}static async initialize(e){let{config:t,stream:r}=e;void 0!==r&&t.configurable?.[ij]!==void 0&&(r=function(...e){return new aG({passthroughFn:t=>{for(let r of e)r.modes.has(t[1])&&r.push(t)},modes:new Set(e.flatMap(e=>Array.from(e.modes)))})}(r,t.configurable[ij]));let i=!t.configurable||!("checkpoint_id"in t.configurable),a=t.configurable?.[iI];t.configurable&&a&&(a.subgraphCounter>0&&(t=aA(t,{[iR]:[t.configurable[iR],a.subgraphCounter.toString()].join("|")})),a.subgraphCounter+=1);let s=iA in(t.configurable??{});s||t.configurable?.checkpoint_ns===void 0||t.configurable?.checkpoint_ns===""||(t=aA(t,{checkpoint_ns:"",checkpoint_id:void 0}));let n=t;t.configurable?.[iM]!==void 0&&t.configurable?.[iM]?.[t.configurable?.checkpoint_ns]&&(n=aA(t,{checkpoint_id:t.configurable[iM][t.configurable?.checkpoint_ns]}));let o=t.configurable?.checkpoint_ns?.split("|")??[],l=await e.checkpointer?.getTuple(n)??{config:t,checkpoint:it(),metadata:{source:"input",step:-2,writes:null,parents:{}},pendingWrites:[]};n={...t,...l.config,configurable:{checkpoint_ns:"",...t.configurable,...l.config.configurable}};let c=l.parentConfig,u=ir(l.checkpoint),h={...l.metadata},d=l.pendingWrites??[],p=im(e.channelSpecs,u),m=(h.step??0)+1,f=m+(t.recursionLimit??25)+1,g={...u.channel_versions},b=e.store?new iu(e.store):void 0;return b&&b.start(),new aZ({input:e.input,config:t,checkpointer:e.checkpointer,checkpoint:u,checkpointMetadata:h,checkpointConfig:n,prevCheckpointConfig:c,checkpointNamespace:o,channels:p,managed:e.managed,isNested:s,manager:e.manager,skipDoneTasks:i,step:m,stop:f,checkpointPreviousVersions:g,checkpointPendingWrites:d,outputKeys:e.outputKeys??[],streamKeys:e.streamKeys??[],nodes:e.nodes,stream:r,store:b,cache:e.cache,interruptAfter:e.interruptAfter,interruptBefore:e.interruptBefore,debug:e.debug,triggerToNodes:e.triggerToNodes})}_checkpointerPutAfterPrevious(e){this._checkpointerChainedPromise=this._checkpointerChainedPromise.then(()=>this.checkpointer?.put(e.config,e.checkpoint,e.metadata,e.newVersions)),this.checkpointerPromises.push(this._checkpointerChainedPromise)}async updateManagedValues(e,t){let r=this.managed.get(e);r&&"update"in r&&"function"==typeof r.update&&await r.update(t)}putWrites(e,t){let r=t;if(0===r.length)return;for(let[t,i]of(r.every(([e])=>e in is)&&(r=Array.from(new Map(r.map(e=>[e[0],e])).values())),r)){let r=this.checkpointPendingWrites.findIndex(r=>r[0]===e&&r[1]===t);t in is&&-1!==r?this.checkpointPendingWrites[r]=[e,t,i]:this.checkpointPendingWrites.push([e,t,i])}let i=this.checkpointer?.putWrites({...this.checkpointConfig,configurable:{...this.checkpointConfig.configurable,checkpoint_ns:this.config.configurable?.checkpoint_ns??"",checkpoint_id:this.checkpoint.id}},r,e);if(void 0!==i&&this.checkpointerPromises.push(i),this.tasks&&this._outputWrites(e,r),!t.length||!this.cache||!this.tasks)return;let a=this.tasks[e];null!=a&&null!=a.cache_key&&t[0][0]!==iO&&t[0][0]!==i$&&this.cache.set([{key:[a.cache_key.ns,a.cache_key.key],value:a.writes,ttl:a.cache_key.ttl}])}_outputWrites(e,t,r=!1){let i=this.tasks[e];if(void 0!==i){if(void 0!==i.config&&(i.config.tags??[]).includes(iV))return;t.length>0&&t[0][0]!==iO&&t[0][0]!==i$&&this._emit(al(an(aO(this.outputKeys,[[i,t]],r),"updates"))),r||this._emit(al(an(function*(e,t,r){let i=new Date().toISOString();for(let[{id:a,name:s,config:n},o]of t)n?.tags?.includes(iV)||(yield{type:"task_result",timestamp:i,step:e,payload:{id:a,name:s,result:o.filter(([e])=>Array.isArray(r)?r.includes(e):e===r),interrupts:o.filter(e=>e[0]===i$).map(e=>e[1])}})}(this.step,[[i,t]],this.streamKeys),"debug")))}}async _matchCachedWrites(){if(!this.cache)return[];let e=[],t=([e,t])=>`ns:${e.join(",")}|key:${t}`,r=[],i={};for(let e of Object.values(this.tasks))null==e.cache_key||e.writes.length||(r.push([e.cache_key.ns,e.cache_key.key]),i[t([e.cache_key.ns,e.cache_key.key])]=e);if(0===r.length)return[];for(let{key:a,value:s}of(await this.cache.get(r))){let r=i[t(a)];null!=r&&(r.writes.push(...s),e.push({task:r,result:s}))}return e}async tick(e){this.store&&!this.store.isRunning&&this.store?.start();let{inputKeys:t=[]}=e;if("pending"!==this.status)throw Error(`Cannot tick when status is no longer "pending". Current status: "${this.status}"`);if([aY,aH].includes(this.input))if(this.toInterrupt.length>0)throw this.status="interrupt_before",new Y;else{if(!Object.values(this.tasks).every(e=>e.writes.length>0))return!1;let e=Object.values(this.tasks).flatMap(e=>e.writes);for(let[e,t]of Object.entries(aD(this.checkpoint,this.channels,Object.values(this.tasks),this.checkpointerGetNextVersion,this.triggerToNodes)))await this.updateManagedValues(e,t);let t=await ao(an(ak(this.outputKeys,e,this.channels),"values"));if(this._emit(t),this.checkpointPendingWrites=[],await this._putCheckpoint({source:"loop",writes:aO(this.outputKeys,Object.values(this.tasks).map(e=>[e,e.writes])).next().value??null}),aI(this.checkpoint,this.interruptAfter,Object.values(this.tasks)))throw this.status="interrupt_after",new Y;this.config.configurable?.[iP]!==void 0&&delete this.config.configurable?.[iP]}else await this._first(t);if(this.step>this.stop)return this.status="out_of_steps",!1;let r=a$(this.checkpoint,this.checkpointPendingWrites,this.nodes,this.channels,this.managed,this.config,!0,{step:this.step,checkpointer:this.checkpointer,isResuming:this.isResuming,manager:this.manager,store:this.store,stream:this.stream});if(this.tasks=r,this.checkpointer&&this._emit(await ao(an(function*(e,t,r,i,a,s,n,o){function l(e){let t={};return null!=e.callbacks&&(t.callbacks=e.callbacks),null!=e.configurable&&(t.configurable=e.configurable),null!=e.maxConcurrency&&(t.max_concurrency=e.maxConcurrency),null!=e.metadata&&(t.metadata=e.metadata),null!=e.recursionLimit&&(t.recursion_limit=e.recursionLimit),null!=e.runId&&(t.run_id=e.runId),null!=e.runName&&(t.run_name=e.runName),null!=e.tags&&(t.tags=e.tags),t}let c=t.configurable?.checkpoint_ns,u={};for(let e of s){if(!(e.subgraphs?.length?e.subgraphs:[e.proc]).find(aq))continue;let r=`${e.name}:${e.id}`;c&&(r=`${c}|${r}`),u[e.id]={configurable:{thread_id:t.configurable?.thread_id,checkpoint_ns:r}}}let h=new Date().toISOString();yield{type:"checkpoint",timestamp:h,step:e,payload:{config:l(t),values:aw(r,i),metadata:a,next:s.map(e=>e.name),tasks:aB(s,n,u),parentConfig:o?l(o):void 0}}}(this.step-1,this.checkpointConfig,this.channels,this.streamKeys,this.checkpointMetadata,Object.values(this.tasks),this.checkpointPendingWrites,this.prevCheckpointConfig),"debug"))),0===Object.values(this.tasks).length)return this.status="done",!1;if(this.skipDoneTasks&&this.checkpointPendingWrites.length>0){for(let[e,t,r]of this.checkpointPendingWrites){if(t===iO||t===i$||t===iL)continue;let i=Object.values(this.tasks).find(t=>t.id===e);i&&i.writes.push([t,r])}for(let e of Object.values(this.tasks))e.writes.length>0&&this._outputWrites(e.id,e.writes,!0)}if(Object.values(this.tasks).every(e=>e.writes.length>0))return this.tick({inputKeys:t});if(aI(this.checkpoint,this.interruptBefore,Object.values(this.tasks)))throw this.status="interrupt_before",new Y;let i=await ao(an(aK(this.step,Object.values(this.tasks)),"debug"));return this._emit(i),!0}async finishAndHandleError(e){let t=this._suppressInterrupt(e);if((t||void 0===e)&&(this.output=aw(this.channels,this.outputKeys)),t){if(void 0!==this.tasks&&this.checkpointPendingWrites.length>0&&Object.values(this.tasks).some(e=>e.writes.length>0)){for(let[e,t]of Object.entries(aD(this.checkpoint,this.channels,Object.values(this.tasks),this.checkpointerGetNextVersion,this.triggerToNodes)))await this.updateManagedValues(e,t);this._emit(al(an(ak(this.outputKeys,Object.values(this.tasks).flatMap(e=>e.writes),this.channels),"values")))}let t={[i$]:e.interrupts};this._emit([["updates",t],["values",t]])}return t}async acceptPush(e,t,r){if(this.interruptAfter?.length>0&&aI(this.checkpoint,this.interruptAfter,[e]))return void this.toInterrupt.push(e);let i=aL([iW,e.path??[],t,e.id,r],this.checkpoint,this.checkpointPendingWrites,this.nodes,this.channels,this.managed,e.config??{},!0,{step:this.step,checkpointer:this.checkpointer,manager:this.manager,store:this.store,stream:this.stream});if(i){if(this.interruptBefore?.length>0&&aI(this.checkpoint,this.interruptBefore,[i]))return void this.toInterrupt.push(i);for(let{task:e}of(this._emit(al(an(aK(this.step,[i]),"debug"))),this.debug&&aW(this.step,[i]),this.tasks[i.id]=i,this.skipDoneTasks&&this._matchWrites({[i.id]:i}),await this._matchCachedWrites()))this._outputWrites(e.id,e.writes,!0);return i}}_suppressInterrupt(e){return Q(e)&&!this.isNested}async _first(e){let{configurable:t}=this.config,r=t?.[iI];if(r&&void 0!==r.nullResume&&this.putWrites(iG,[[iL,r.nullResume]]),i0(this.input)){if(null!=this.input.resume&&null==this.checkpointer)throw Error("Cannot use Command(resume=...) without checkpointer");let e={};for(let[t,r,i]of function*(e,t){if(e.graph===iQ.PARENT)throw new er("There is no parent graph.");if(e.goto){let t;for(let t of Array.isArray(e.goto)?e.goto:[e.goto])if(iZ(t))yield[iG,iB,t];else if("string"==typeof t)yield[iG,`branch:to:${t}`,"__start__"];else throw Error(`In Command.send, expected Send or string, got ${typeof t}`)}if(e.resume)if("object"==typeof e.resume&&Object.keys(e.resume).length&&Object.keys(e.resume).every(ae.A))for(let[r,i]of Object.entries(e.resume)){let e=t.filter(e=>e[0]===r&&e[1]===iL).map(e=>e[2]).slice(0,1)??[];e.push(i),yield[r,iL,e]}else yield[iG,iL,e.resume];if(e.update){if("object"!=typeof e.update||!e.update)throw Error("Expected cmd.update to be a dict mapping channel names to update values");if(Array.isArray(e.update))for(let[t,r]of e.update)yield[iG,t,r];else for(let[t,r]of Object.entries(e.update))yield[iG,t,r]}}(this.input,this.checkpointPendingWrites))void 0===e[t]&&(e[t]=[]),e[t].push([r,i]);if(0===Object.keys(e).length)throw new ee("Received empty Command input");for(let[t,r]of Object.entries(e))this.putWrites(t,r)}let i=(this.checkpointPendingWrites??[]).filter(e=>e[0]===iG).map(e=>e.slice(1));i.length>0&&aD(this.checkpoint,this.channels,[{name:ik,writes:i,triggers:[]}],this.checkpointerGetNextVersion,this.triggerToNodes);let a=i0(this.input)&&i.length>0;if(this.isResuming||a){for(let e of Object.keys(this.channels))if(void 0!==this.checkpoint.channel_versions[e]){let t=this.checkpoint.channel_versions[e];this.checkpoint.versions_seen[i$]={...this.checkpoint.versions_seen[i$],[e]:t}}let e=await ao(an(ak(this.outputKeys,!0,this.channels),"values"));this._emit(e)}if(this.isResuming)this.input=aH;else if(a)await this._putCheckpoint({source:"input",writes:{}}),this.input=aY;else{let t=await ao(av(e,this.input));if(t.length>0){let e=a$(this.checkpoint,this.checkpointPendingWrites,this.nodes,this.channels,this.managed,this.config,!0,{step:this.step});aD(this.checkpoint,this.channels,Object.values(e).concat([{name:ik,writes:t,triggers:[]}]),this.checkpointerGetNextVersion,this.triggerToNodes),await this._putCheckpoint({source:"input",writes:Object.fromEntries(t)}),this.input=aY}else if(iP in(this.config.configurable??{}))this.input=aY;else throw new ee(`Received no input writes for ${JSON.stringify(e,null,2)}`)}this.isNested||(this.config=aA(this.config,{[iP]:this.isResuming}))}_emit(e){for(let t of e)this.stream.modes.has(t[0])&&this.stream.push([this.checkpointNamespace,...t])}async _putCheckpoint(e){let t={...e,step:this.step,parents:this.config.configurable?.[iM]??{}};if(void 0!==this.checkpointer){this.prevCheckpointConfig=this.checkpointConfig?.configurable?.checkpoint_id?this.checkpointConfig:void 0,this.checkpointMetadata=t,this.checkpoint=ig(this.checkpoint,this.channels,this.step),this.checkpointConfig={...this.checkpointConfig,configurable:{...this.checkpointConfig.configurable,checkpoint_ns:this.config.configurable?.checkpoint_ns??""}};let e={...this.checkpoint.channel_versions},r=aC(this.checkpointPreviousVersions,e);this.checkpointPreviousVersions=e,this._checkpointerPutAfterPrevious({config:{...this.checkpointConfig},checkpoint:ir(this.checkpoint),metadata:{...this.checkpointMetadata},newVersions:r}),this.checkpointConfig={...this.checkpointConfig,configurable:{...this.checkpointConfig.configurable,checkpoint_id:this.checkpoint.id}}}this.step+=1}_matchWrites(e){for(let[t,r,i]of this.checkpointPendingWrites){if(r===iO||r===i$||r===iL)continue;let a=Object.values(e).find(e=>e.id===t);a&&a.writes.push([r,i])}for(let t of Object.values(e))t.writes.length>0&&this._outputWrites(t.id,t.writes,!0)}}class aQ extends eL.BaseCallbackHandler{constructor(e){super(),Object.defineProperty(this,"name",{enumerable:!0,configurable:!0,writable:!0,value:"StreamMessagesHandler"}),Object.defineProperty(this,"streamFn",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"metadatas",{enumerable:!0,configurable:!0,writable:!0,value:{}}),Object.defineProperty(this,"seen",{enumerable:!0,configurable:!0,writable:!0,value:{}}),Object.defineProperty(this,"emittedChatModelRunIds",{enumerable:!0,configurable:!0,writable:!0,value:{}}),Object.defineProperty(this,"stableMessageIdMap",{enumerable:!0,configurable:!0,writable:!0,value:{}}),Object.defineProperty(this,"lc_prefer_streaming",{enumerable:!0,configurable:!0,writable:!0,value:!0}),this.streamFn=e}_emit(e,t,r,i=!1){if(i&&void 0!==t.id&&void 0!==this.seen[t.id])return;let a=t.id;null!=r&&((0,eq.isToolMessage)(t)?a??=`run-${r}-tool-${t.tool_call_id}`:((null==a||a===`run-${r}`)&&(a=this.stableMessageIdMap[r]??a??`run-${r}`),this.stableMessageIdMap[r]??=a)),a!==t.id&&(t.id=a,t.lc_kwargs.id=a),null!=t.id&&(this.seen[t.id]=t),this.streamFn([e[0],"messages",[t,e[1]]])}handleChatModelStart(e,t,r,i,a,s,n,o){!n||s&&(s.includes("langsmith:nostream")||s.includes("nostream"))||(this.metadatas[r]=[n.langgraph_checkpoint_ns.split("|"),{tags:s,name:o,...n}])}handleLLMNewToken(e,t,r,i,a,s){let n=s?.chunk;(this.emittedChatModelRunIds[r]=!0,void 0!==this.metadatas[r])&&((0,eq.isBaseMessage)(n?.message)?this._emit(this.metadatas[r],n.message,r):this._emit(this.metadatas[r],new eq.AIMessageChunk({content:e}),r))}handleLLMEnd(e,t){if(!this.emittedChatModelRunIds[t]){let r=e.generations?.[0]?.[0];(0,eq.isBaseMessage)(r?.message)&&this._emit(this.metadatas[t],r?.message,t,!0),delete this.emittedChatModelRunIds[t]}delete this.metadatas[t],delete this.stableMessageIdMap[t]}handleLLMError(e,t){delete this.metadatas[t]}handleChainStart(e,t,r,i,a,s,n,o){if(void 0!==s&&o===s.langgraph_node&&(void 0===a||!a.includes(iV))&&(this.metadatas[r]=[s.langgraph_checkpoint_ns.split("|"),{tags:a,name:o,...s}],"object"==typeof t)){for(let e of Object.values(t))if(((0,eq.isBaseMessage)(e)||(0,eq.isBaseMessageChunk)(e))&&void 0!==e.id)this.seen[e.id]=e;else if(Array.isArray(e))for(let t of e)((0,eq.isBaseMessage)(t)||(0,eq.isBaseMessageChunk)(t))&&void 0!==t.id&&(this.seen[t.id]=t)}}handleChainEnd(e,t){let r=this.metadatas[t];if(delete this.metadatas[t],void 0!==r){if((0,eq.isBaseMessage)(e))this._emit(r,e,t,!0);else if(Array.isArray(e))for(let i of e)(0,eq.isBaseMessage)(i)&&this._emit(r,i,t,!0);else if(null!=e&&"object"==typeof e){for(let i of Object.values(e))if((0,eq.isBaseMessage)(i))this._emit(r,i,t,!0);else if(Array.isArray(i))for(let e of i)(0,eq.isBaseMessage)(e)&&this._emit(r,e,t,!0)}}}handleChainError(e,t){delete this.metadatas[t]}}let a0=[400,401,402,403,404,405,406,407,409],a1=e=>{if(e.message.startsWith("Cancel")||e.message.startsWith("AbortError")||"AbortError"===e.name||e?.code==="ECONNABORTED")return!1;let t=e?.response?.status??e?.status;return!(t&&a0.includes(+t))&&e?.error?.code!=="insufficient_quota"};async function a2(e,t,r,i){let a,s,n=e.retry_policy??t,o=void 0!==n?n.initialInterval??500:0,l=0,{config:c}=e;for(r&&(c=aA(c,r)),c={...c,signal:i};!i?.aborted;){e.writes.splice(0,e.writes.length),a=void 0;try{s=await e.proc.invoke(e.input,c);break}catch(i){var u;if((a=i).pregelTaskId=e.id,void 0!==(u=a)&&u.name===X.unminifiable_name){let t=c?.configurable?.checkpoint_ns,r=a.command;if(r.graph===t){for(let t of e.writers)await t.invoke(r,c);a=void 0;break}if(r.graph===iQ.PARENT){let e=function(e){let t=e.split("|");for(;t.length>1&&t[t.length-1].match(/^\d+$/);)t.pop();return t.slice(0,-1).join("|")}(t);a.command=new iQ({...a.command,graph:e})}}if(Z(a)||void 0===n||(l+=1)>=(n.maxAttempts??3)||!(n.retryOn??a1)(a))break;o=Math.min(n.maxInterval??128e3,o*(n.backoffFactor??2));let t=n.jitter?Math.floor(o+1e3*Math.random()):o;await new Promise(e=>setTimeout(e,t));let r=a.name??a.constructor.unminifiable_name??a.constructor.name;(n?.logWarning??!0)&&console.log(`Retrying task "${String(e.name)}" after ${o.toFixed(2)}ms (attempt ${l}) after ${r}: ${a}`),c=aA(c,{[iP]:!0})}}return{task:e,result:s,error:a,signalAborted:i?.aborted}}let a5=Symbol.for("promiseAdded");class a3{constructor({loop:e,nodeFinished:t}){Object.defineProperty(this,"nodeFinished",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"loop",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.loop=e,this.nodeFinished=t}async tick(e={}){let t,{timeout:r,retryPolicy:i,onStepWrite:a,maxConcurrency:s}=e,n=new Set,o=new AbortController,l=Object.values(this.loop.tasks).filter(e=>0===e.writes.length),c=this._initializeAbortSignals({exceptionSignalController:o,timeout:r,signal:e.signal});for await(let{task:e,error:r,signalAborted:a}of this._executeTasksWithRetry(l,{signals:c,retryPolicy:i,maxConcurrency:s}))this._commit(e,r),Q(r)||Z(r)&&!Q(t)?t=r:r&&(0===n.size||!a)&&(o.abort(),n.add(r));if(a?.(this.loop.step,Object.values(this.loop.tasks).map(e=>e.writes).flat()),1===n.size)throw Array.from(n)[0];if(n.size>1)throw AggregateError(Array.from(n),`Multiple errors occurred during superstep ${this.loop.step}. See the "errors" field of this exception for more details.`);if(Q(t)||Z(t)&&this.loop.isNested)throw t}_initializeAbortSignals({exceptionSignalController:e,timeout:t,signal:r}){let i=this.loop.config.configurable?.[iD]??{},a=r&&i.composedAbortSignal&&r!==i.composedAbortSignal?aP(i.externalAbortSignal,r):i.externalAbortSignal??r,s=i.errorAbortSignal?aP(i.errorAbortSignal,e.signal):e.signal,n=t?AbortSignal.timeout(t):void 0,o=aP(...a?[a]:[],...n?[n]:[],s),l={externalAbortSignal:a,errorAbortSignal:s,timeoutAbortSignal:n,composedAbortSignal:o};return this.loop.config=aA(this.loop.config,{[iD]:l}),l}async *_executeTasksWithRetry(e,t){let r,{retryPolicy:i,maxConcurrency:a,signals:s}=t??{},n=function(){let e={next:()=>void 0,wait:Promise.resolve(a5)};return e.wait=new Promise(function t(r){e.next=()=>{e.wait=new Promise(t),r(a5)}}),e}(),o={},l={executingTasksMap:o,barrier:n,retryPolicy:i,scheduleTask:async(e,t,r)=>this.loop.acceptPush(e,t,r)};if(s?.composedAbortSignal?.aborted)throw Error("Abort");let c=0,u=s?.externalAbortSignal||s?.timeoutAbortSignal?aP(...s.externalAbortSignal?[s.externalAbortSignal]:[],...s.timeoutAbortSignal?[s.timeoutAbortSignal]:[]):void 0,h=u?new Promise((e,t)=>{r=()=>t(Error("Abort")),u.addEventListener("abort",r,{once:!0})}):void 0;for(;(0===c||Object.keys(o).length>0)&&e.length;){for(;Object.values(o).length<(a??e.length)&&c<e.length;c+=1){let t=e[c];o[t.id]=a2(t,i,{[iC]:a4?.bind(l,this,t)},s?.composedAbortSignal).catch(e=>({task:t,error:e,signalAborted:s?.composedAbortSignal?.aborted}))}let t=await Promise.race([...Object.values(o),...h?[h]:[],n.wait]);t!==a5&&(yield t,delete o[t.task.id])}}_commit(e,t){if(void 0!==t)if(Q(t)){if(t.interrupts.length){let r=t.interrupts.map(e=>[i$,e]),i=e.writes.filter(e=>e[0]===iL);i.length&&r.push(...i),this.loop.putWrites(e.id,r)}}else Z(t)&&e.writes.length?this.loop.putWrites(e.id,e.writes):this.loop.putWrites(e.id,[[iO,{message:t.message,name:t.name}]]);else this.nodeFinished&&(e.config?.tags==null||!e.config.tags.includes(iV))&&this.nodeFinished(String(e.name)),0===e.writes.length&&e.writes.push([iU,null]),this.loop.putWrites(e.id,e.writes)}}async function a4(e,t,r,i,a,s={}){let n=t.config?.configurable?.[iI];if(!n)throw Error(`BUG: No scratchpad found on task ${t.name}__${t.id}`);let o=n.callCounter;n.callCounter+=1;let l=new ax({func:r,name:i,input:a,cache:s.cache,retry:s.retry,callbacks:s.callbacks}),c=await this.scheduleTask(t,o,l);if(!c)return;let u=this.executingTasksMap[c.id];if(void 0!==u)return u;if(c.writes.length>0){let e=c.writes.filter(([e])=>e===iF),t=c.writes.filter(([e])=>e===iO);if(e.length>0){if(1===e.length)return Promise.resolve(e[0][1]);throw Error(`BUG: multiple returns found for task ${c.name}__${c.id}`)}if(t.length>0){if(1===t.length){let e=t[0][1];return Promise.reject(e instanceof Error?e:Error(String(e)))}throw Error(`BUG: multiple errors found for task ${c.name}__${c.id}`)}return}{let t=a2(c,s.retry,{[iC]:a4.bind(this,e,c)});return this.executingTasksMap[c.id]=t,this.barrier.next(),t.then(({result:e,error:t})=>t?Promise.reject(t):e)}}class a8 extends Error{constructor(e){super(e),this.name="GraphValidationError"}}function a6(e,t){if(Array.isArray(e)){for(let r of e)if(!(r in t))throw Error(`Key ${String(r)} not found in channels`)}else if(!(e in t))throw Error(`Key ${String(e)} not found in channels`)}class a9{static subscribeTo(e,t){let{key:r,tags:i}={key:void 0,tags:void 0,...t??{}};if(Array.isArray(e)&&void 0!==r)throw Error("Can't specify a key when subscribing to multiple channels");return new ay({channels:"string"==typeof e?r?{[r]:e}:[e]:Object.fromEntries(e.map(e=>[e,e])),triggers:Array.isArray(e)?e:[e],tags:i})}static writeTo(e,t){let r=[];for(let t of e)r.push({channel:t,value:au,skipNone:!1});for(let[e,i]of Object.entries(t??{}))e8.YN.isRunnable(i)||"function"==typeof i?r.push({channel:e,value:au,skipNone:!0,mapper:(0,e8.Bp)(i)}):r.push({channel:e,value:i,skipNone:!1});return new ap(r)}}class a7 extends e8.YN{constructor(){super(...arguments),Object.defineProperty(this,"lc_namespace",{enumerable:!0,configurable:!0,writable:!0,value:["langgraph","pregel"]})}invoke(e,t){throw Error("Not implemented")}withConfig(e){return super.withConfig(e)}stream(e,t){return super.stream(e,t)}}class se extends a7{static lc_name(){return"LangGraph"}constructor(e){super(e),Object.defineProperty(this,"lc_namespace",{enumerable:!0,configurable:!0,writable:!0,value:["langgraph","pregel"]}),Object.defineProperty(this,"lg_is_pregel",{enumerable:!0,configurable:!0,writable:!0,value:!0}),Object.defineProperty(this,"nodes",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"channels",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"inputChannels",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"outputChannels",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"autoValidate",{enumerable:!0,configurable:!0,writable:!0,value:!0}),Object.defineProperty(this,"streamMode",{enumerable:!0,configurable:!0,writable:!0,value:["values"]}),Object.defineProperty(this,"streamChannels",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"interruptAfter",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"interruptBefore",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"stepTimeout",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"debug",{enumerable:!0,configurable:!0,writable:!0,value:!1}),Object.defineProperty(this,"checkpointer",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"retryPolicy",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"config",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"store",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"triggerToNodes",{enumerable:!0,configurable:!0,writable:!0,value:{}}),Object.defineProperty(this,"cache",{enumerable:!0,configurable:!0,writable:!0,value:void 0});let{streamMode:t}=e;null==t||Array.isArray(t)||(t=[t]),this.nodes=e.nodes,this.channels=e.channels,this.autoValidate=e.autoValidate??this.autoValidate,this.streamMode=t??this.streamMode,this.inputChannels=e.inputChannels,this.outputChannels=e.outputChannels,this.streamChannels=e.streamChannels??this.streamChannels,this.interruptAfter=e.interruptAfter,this.interruptBefore=e.interruptBefore,this.stepTimeout=e.stepTimeout??this.stepTimeout,this.debug=e.debug??this.debug,this.checkpointer=e.checkpointer,this.retryPolicy=e.retryPolicy,this.config=e.config,this.store=e.store,this.cache=e.cache,this.name=e.name,this.autoValidate&&this.validate()}withConfig(e){let t=(0,to.SV)(this.config,e);return new this.constructor({...this,config:t})}validate(){for(let[e,t]of(!function({nodes:e,channels:t,inputChannels:r,outputChannels:i,streamChannels:a,interruptAfterNodes:s,interruptBeforeNodes:n}){if(!t)throw new a8("Channels not provided");let o=new Set,l=new Set;for(let[t,r]of Object.entries(e)){if(t===i$)throw new a8(`"Node name ${i$} is reserved"`);if(r.constructor===ay)r.triggers.forEach(e=>o.add(e));else throw new a8(`Invalid node type ${typeof r}, expected PregelNode`)}for(let e of o)if(!(e in t))throw new a8(`Subcribed channel '${String(e)}' not in channels`);if(Array.isArray(r)){if(r.every(e=>!o.has(e)))throw new a8(`None of the input channels ${r} are subscribed to by any node`)}else if(!o.has(r))throw new a8(`Input channel ${String(r)} is not subscribed to by any node`);for(let e of(Array.isArray(i)?i.forEach(e=>l.add(e)):l.add(i),a&&!Array.isArray(a)?l.add(a):Array.isArray(a)&&a.forEach(e=>l.add(e)),l))if(!(e in t))throw new a8(`Output channel '${String(e)}' not in channels`);if(s&&"*"!==s){for(let t of s)if(!(t in e))throw new a8(`Node ${String(t)} not in nodes`)}if(n&&"*"!==n){for(let t of n)if(!(t in e))throw new a8(`Node ${String(t)} not in nodes`)}}({nodes:this.nodes,channels:this.channels,outputChannels:this.outputChannels,inputChannels:this.inputChannels,streamChannels:this.streamChannels,interruptAfterNodes:this.interruptAfter,interruptBeforeNodes:this.interruptBefore}),Object.entries(this.nodes)))for(let r of t.triggers)this.triggerToNodes[r]??=[],this.triggerToNodes[r].push(e);return this}get streamChannelsList(){return Array.isArray(this.streamChannels)?this.streamChannels:this.streamChannels?[this.streamChannels]:Object.keys(this.channels)}get streamChannelsAsIs(){return this.streamChannels?this.streamChannels:Object.keys(this.channels)}async getGraphAsync(e){return this.getGraph(e)}*getSubgraphs(e,t){for(let[r,i]of Object.entries(this.nodes))if(void 0===e||e.startsWith(r))for(let a of i.subgraphs?.length?i.subgraphs:[i.bound]){let i=aq(a);if(void 0!==i){if(r===e)return void(yield[r,i]);if(void 0===e&&(yield[r,i]),t){let a=e;for(let[s,n]of(void 0!==e&&(a=e.slice(r.length+1)),i.getSubgraphs(a,t)))yield[`${r}|${s}`,n]}}}}async *getSubgraphsAsync(e,t){yield*this.getSubgraphs(e,t)}async _prepareStateSnapshot({config:e,saved:t,subgraphCheckpointer:r,applyPendingWrites:i=!1}){if(void 0===t)return{values:{},next:[],config:e,tasks:[]};let{managed:a}=await this.prepareSpecs(e,{skipManaged:!0}),s=im(this.channels,t.checkpoint);if(t.pendingWrites?.length){let e=t.pendingWrites.filter(([e,t])=>e===iG).map(([e,t,r])=>[String(t),r]);e.length>0&&aD(t.checkpoint,s,[{name:ik,writes:e,triggers:[]}],void 0,this.triggerToNodes)}let n=Object.values(a$(t.checkpoint,t.pendingWrites,this.nodes,s,a,t.config,!0,{step:(t.metadata?.step??-1)+1,store:this.store})),o=await ao(this.getSubgraphsAsync()),l=t.config.configurable?.checkpoint_ns??"",c={};for(let e of n){let i=o.find(([t])=>t===e.name);if(!i)continue;let a=`${String(e.name)}:${e.id}`;if(l&&(a=`${l}|${a}`),void 0===r){let r={configurable:{thread_id:t.config.configurable?.thread_id,checkpoint_ns:a}};c[e.id]=r}else{let s={configurable:{[iT]:r,thread_id:t.config.configurable?.thread_id,checkpoint_ns:a}},n=i[1];c[e.id]=await n.getState(s,{subgraphs:!0})}}if(i&&t.pendingWrites?.length){let e=Object.fromEntries(n.map(e=>[e.id,e]));for(let[r,i,a]of t.pendingWrites)![iO,i$,ep].includes(i)&&r in e&&e[r].writes.push([String(i),a]);let r=n.filter(e=>e.writes.length>0);r.length>0&&aD(t.checkpoint,s,r,void 0,this.triggerToNodes)}let u=t?.metadata;u&&t?.config?.configurable?.thread_id&&(u={...u,thread_id:t.config.configurable.thread_id});let h=n.filter(e=>0===e.writes.length).map(e=>e.name);return{values:aw(s,this.streamChannelsAsIs),next:h,tasks:aB(n,t?.pendingWrites??[],c),metadata:u,config:aT(t.config,t.metadata),createdAt:t.checkpoint.ts,parentConfig:t.parentConfig}}async getState(e,t){let r=e.configurable?.[iT]??this.checkpointer;if(!r)throw new G("No checkpointer set");let i=e.configurable?.checkpoint_ns??"";if(""!==i&&e.configurable?.[iT]===void 0){let a=aa(i);for await(let[i,s]of this.getSubgraphsAsync(a,!0))if(i===a)return await s.getState(ac(e,{[iT]:r}),{subgraphs:t?.subgraphs});throw Error(`Subgraph with namespace "${a}" not found.`)}let a=(0,to.SV)(this.config,e),s=await r.getTuple(e);return await this._prepareStateSnapshot({config:a,saved:s,subgraphCheckpointer:t?.subgraphs?r:void 0,applyPendingWrites:!e.configurable?.checkpoint_id})}async *getStateHistory(e,t){let r=e.configurable?.[iT]??this.checkpointer;if(!r)throw Error("No checkpointer set");let i=e.configurable?.checkpoint_ns??"";if(""!==i&&e.configurable?.[iT]===void 0){let a=aa(i);for await(let[i,s]of this.getSubgraphsAsync(a,!0))if(i===a)return void(yield*s.getStateHistory(ac(e,{[iT]:r}),t));throw Error(`Subgraph with namespace "${a}" not found.`)}let a=(0,to.SV)(this.config,e,{configurable:{checkpoint_ns:i}});for await(let e of r.list(a,t))yield this._prepareStateSnapshot({config:e.config,saved:e})}async bulkUpdateState(e,t){let r=e.configurable?.[iT]??this.checkpointer;if(!r)throw new G("No checkpointer set");if(0===t.length)throw Error("No supersteps provided");if(t.some(e=>0===e.updates.length))throw Error("No updates provided");let i=e.configurable?.checkpoint_ns??"";if(""!==i&&e.configurable?.[iT]===void 0){let a=aa(i);for await(let[,i]of this.getSubgraphsAsync(a,!0))return await i.bulkUpdateState(ac(e,{[iT]:r}),t);throw Error(`Subgraph "${a}" not found`)}let a=async(e,t)=>{let i=this.config?(0,to.SV)(this.config,e):e,a=await r.getTuple(i),s=void 0!==a?ir(a.checkpoint):it(),n={...a?.checkpoint.channel_versions},o=a?.metadata?.step??-1,l=ac(i,{checkpoint_ns:i.configurable?.checkpoint_ns??""}),c=i.metadata??{};a?.config.configurable&&(l=ac(i,a.config.configurable),c={...a.metadata,...c});let{values:u,asNode:h}=t[0];if(null==u&&void 0===h){if(t.length>1)throw new er("Cannot create empty checkpoint with multiple updates");return aT(await r.put(l,ig(s,void 0,o),{source:"update",step:o+1,writes:{},parents:a?.metadata?.parents??{}},{}),a?a.metadata:void 0)}let d=im(this.channels,s),{managed:p}=await this.prepareSpecs(i,{skipManaged:!0});if(null===u&&h===iv){if(t.length>1)throw new er("Cannot apply multiple updates when clearing state");if(a){let e=a$(s,a.pendingWrites||[],this.nodes,d,p,a.config,!0,{step:(a.metadata?.step??-1)+1,checkpointer:this.checkpointer||void 0,store:this.store}),t=(a.pendingWrites||[]).filter(e=>e[0]===iG).map(e=>e.slice(1));for(let[r,i,s]of(t.length>0&&aD(a.checkpoint,d,[{name:ik,writes:t,triggers:[]}],void 0,this.triggerToNodes),a.pendingWrites||[]))![iO,i$,ep].includes(i)&&r in e&&e[r].writes.push([i,s]);aD(s,d,Object.values(e),void 0,this.triggerToNodes)}return aT(await r.put(l,ig(s,void 0,o),{...c,source:"update",step:o+1,writes:{},parents:a?.metadata?.parents??{}},{}),a?a.metadata:void 0)}if(null==u&&"__copy__"===h){if(t.length>1)throw new er("Cannot copy checkpoint with multiple updates");return aT(await r.put(a?.parentConfig??l,ig(s,void 0,o),{source:"fork",step:o+1,writes:{},parents:a?.metadata?.parents??{}},{}),a?a.metadata:void 0)}if(h===ik){if(t.length>1)throw new er("Cannot apply multiple updates when updating as input");let e=await ao(av(this.inputChannels,u));if(0===e.length)throw new er(`Received no input writes for ${JSON.stringify(this.inputChannels,null,2)}`);aD(s,d,[{name:ik,writes:e,triggers:[]}],r.getNextVersion.bind(this.checkpointer),this.triggerToNodes);let i=a?.metadata?.step!=null?a.metadata.step+1:-1,o=await r.put(l,ig(s,d,i),{source:"input",step:i,writes:Object.fromEntries(e),parents:a?.metadata?.parents??{}},aC(n,s.channel_versions));return await r.putWrites(o,e,ed(ik,s.id)),aT(o,a?a.metadata:void 0)}if(i.configurable?.checkpoint_id===void 0&&a?.pendingWrites!==void 0&&a.pendingWrites.length>0){let e=a$(s,a.pendingWrites,this.nodes,d,p,a.config,!0,{store:this.store,checkpointer:this.checkpointer,step:(a.metadata?.step??-1)+1}),t=(a.pendingWrites??[]).filter(e=>e[0]===iG).map(e=>e.slice(1));for(let[r,i,s]of(t.length>0&&aD(a.checkpoint,d,[{name:ik,writes:t,triggers:[]}],void 0,this.triggerToNodes),a.pendingWrites))[iO,i$,ep].includes(i)||void 0===e[r]||e[r].writes.push([i,s]);let r=Object.values(e).filter(e=>e.writes.length>0);r.length>0&&aD(s,d,r,void 0,this.triggerToNodes)}let m=Object.values(s.versions_seen).map(e=>Object.values(e)).flat().find(e=>!!e),f=[];if(1===t.length){let{values:e,asNode:r}=t[0];if(void 0===r&&1===Object.keys(this.nodes).length)[r]=Object.keys(this.nodes);else if(void 0===r&&void 0===m)"string"==typeof this.inputChannels&&void 0!==this.nodes[this.inputChannels]&&(r=this.inputChannels);else if(void 0===r){let e=Object.entries(s.versions_seen).map(([e,t])=>Object.values(t).map(t=>[t,e])).flat().sort(([e],[t])=>ii(e,t));e&&(1===e.length?r=e[0][1]:e[e.length-1][0]!==e[e.length-2][0]&&(r=e[e.length-1][1]))}if(void 0===r)throw new er('Ambiguous update, specify "asNode"');f.push({values:e,asNode:r})}else for(let{asNode:e,values:r}of t){if(null==e)throw new er('"asNode" is required when applying multiple updates');f.push({values:r,asNode:e})}let g=[];for(let{asNode:e,values:t}of f){if(void 0===this.nodes[e])throw new er(`Node "${e.toString()}" does not exist`);let r=this.nodes[e].getWriters();if(!r.length)throw new er(`No writers found for node "${e.toString()}"`);g.push({name:e,input:t,proc:r.length>1?e8.zZ.from(r,{omitSequenceTags:!0}):r[0],writes:[],triggers:[i$],id:ed(i$,s.id),writers:[]})}for(let e of g)await e.proc.invoke(e.input,(0,to.tn)({...i,store:i?.store??this.store},{runName:i.runName??`${this.getName()}UpdateState`,configurable:{[iS]:t=>e.writes.push(...t),[iA]:(t,r=!1)=>aN(o,s,d,p,e,t,r)}}));for(let e of g){let t=e.writes.filter(e=>e[0]!==iW);void 0!==a&&t.length>0&&await r.putWrites(l,t,e.id)}aD(s,d,g,r.getNextVersion.bind(this.checkpointer),this.triggerToNodes);let b=aC(n,s.channel_versions),y=await r.put(l,ig(s,d,o+1),{source:"update",step:o+1,writes:Object.fromEntries(f.map(e=>[e.asNode,e.values])),parents:a?.metadata?.parents??{}},b);for(let e of g){let t=e.writes.filter(e=>e[0]===iW);t.length>0&&await r.putWrites(y,t,e.id)}return aT(y,a?a.metadata:void 0)},s=e;for(let{updates:e}of t)s=await a(s,e);return s}async updateState(e,t,r){return this.bulkUpdateState(e,[{updates:[{values:t,asNode:r}]}])}_defaults(e){let t,{debug:r,streamMode:i,inputKeys:a,outputKeys:s,interruptAfter:n,interruptBefore:o,...l}=e,c=!0,u=void 0!==r?r:this.debug,h=s;void 0===h?h=this.streamChannelsAsIs:a6(h,this.channels);let d=a;void 0===d?d=this.inputChannels:a6(d,this.channels);let p=o??this.interruptBefore??[],m=n??this.interruptAfter??[];return void 0!==i?(t=Array.isArray(i)?i:[i],c="string"==typeof i):(t=this.streamMode,c=!0),e.configurable?.[iE]!==void 0&&(t=["values"]),[u,t,d,h,l,p,m,!1===this.checkpointer?void 0:void 0!==e&&e.configurable?.[iT]!==void 0?e.configurable[iT]:this.checkpointer,e.store??this.store,c,e.cache??this.cache]}async stream(e,t){let r=new AbortController,i={recursionLimit:this.config?.recursionLimit,...t,signal:t?.signal?aP(t.signal,r.signal):r.signal};return new aJ(await super.stream(e,i),r)}streamEvents(e,t,r){let i=new AbortController,a={recursionLimit:this.config?.recursionLimit,...t,callbacks:aE(this.config?.callbacks,t?.callbacks),signal:t?.signal?aP(t.signal,i.signal):i.signal};return new aJ(super.streamEvents(e,a,r),i)}async prepareSpecs(e,t){let r={...e,store:this.store},i={},a={};for(let[e,r]of Object.entries(this.channels))id(r)?i[e]=r:t?.skipManaged?a[e]={cls:i4,params:{config:{}}}:a[e]=r;return{channelSpecs:i,managed:new i5(await Object.entries(a).reduce(async(e,[t,i])=>{let a,s=await e;return i3(i)?("key"in i.params&&"__channel_key_placeholder__"===i.params.key&&(i.params.key=t),a=await i.cls.initialize(r,i.params)):a=await i.initialize(r),void 0!==a&&s.push([t,a]),s},Promise.resolve([])))}}async _validateInput(e){return e}async _validateConfigurable(e){return e}async *_streamIterator(e,t){let r,i,a=t?.subgraphs,s=ai(this.config,t);if(void 0===s.recursionLimit||s.recursionLimit<1)throw Error('Passed "recursionLimit" must be at least 1.');if(void 0!==this.checkpointer&&!1!==this.checkpointer&&void 0===s.configurable)throw Error('Checkpointer requires one or more of the following "configurable" keys: "thread_id", "checkpoint_ns", "checkpoint_id"');let n=await this._validateInput(e),{runId:o,...l}=s,[c,u,,h,d,p,m,f,g,b,y]=this._defaults(l);d.configurable=await this._validateConfigurable(d.configurable);let _=new aG({modes:new Set(u)});if(u.includes("messages")){let e=new aQ(e=>_.push(e)),{callbacks:t}=d;if(void 0===t)d.callbacks=[e];else if(Array.isArray(t))d.callbacks=t.concat(e);else{let r=t.copy();r.addHandler(e,!0),d.callbacks=r}}u.includes("custom")&&(d.writer=e=>_.push([[],"custom",e]));let w=await (0,to.kJ)(d),v=await w?.handleChainStart(this.toJSON(),!e||Array.isArray(e)||e instanceof Date||"object"!=typeof e?{input:e}:e,o,void 0,void 0,void 0,d?.runName??this.getName()),{channelSpecs:k,managed:O}=await this.prepareSpecs(d),x=(async()=>{try{r=await aZ.initialize({input:n,config:d,checkpointer:f,nodes:this.nodes,channelSpecs:k,managed:O,outputKeys:h,streamKeys:this.streamChannelsAsIs,store:g,cache:y,stream:_,interruptAfter:m,interruptBefore:p,manager:v,debug:this.debug,triggerToNodes:this.triggerToNodes});let e=new a3({loop:r,nodeFinished:d.configurable?.__pregel_node_finished});t?.subgraphs&&(r.config.configurable={...r.config.configurable,[ij]:r.stream}),await this._runLoop({loop:r,runner:e,debug:c,config:d})}catch(e){i=e}finally{try{r&&(await r.store?.stop(),await r.cache?.stop()),await Promise.all([...r?.checkpointerPromises??[],...Array.from(O.values()).map(e=>e.promises())])}catch(e){i=i??e}i?_.error(i):_.close()}})();try{for await(let e of _){if(void 0===e)throw Error("Data structure error.");let[t,r,i]=e;u.includes(r)&&(a&&!b?yield[t,r,i]:b?a?yield[t,i]:yield i:yield[r,i])}}catch(e){throw await v?.handleChainError(i),e}finally{await x}await v?.handleChainEnd(r?.output??{},o,void 0,void 0,void 0)}async invoke(e,t){let r,i=t?.streamMode??"values",a={...t,outputKeys:t?.outputKeys??this.outputChannels,streamMode:i},s=[],n=await this.stream(e,a),o=[];for await(let e of n)if("values"===i)e&&"object"==typeof e&&i$ in e&&Array.isArray(e[i$])?o.push(e[i$]):r=e;else s.push(e);if("values"===i){if(o.length>0){let e=o.flat(1);if(null==r)return{[i$]:e};if("object"==typeof r)return{...r,[i$]:e}}return r}return s}async _runLoop(e){let t,{loop:r,runner:i,debug:a,config:s}=e;try{for(;await r.tick({inputKeys:this.inputChannels});){var n,o,l;for(let{task:e}of(await r._matchCachedWrites()))r._outputWrites(e.id,e.writes,!0);a&&(n=r.checkpointMetadata.step,o=r.channels,l=this.streamChannelsList,console.log([`${aV(az.blue,`[${n}:checkpoint]`)}`,`\x1b[1m State at the end of step ${n}:\x1b[0m
`,JSON.stringify(aw(o,l),null,2)].join(""))),a&&aW(r.step,Object.values(r.tasks)),await i.tick({timeout:this.stepTimeout,retryPolicy:this.retryPolicy,onStepWrite:(e,t)=>{a&&function(e,t,r){let i={};for(let[e,a]of t)r.includes(e)&&(i[e]||(i[e]=[]),i[e].push(a));console.log([`${aV(az.blue,`[${e}:writes]`)}`,`\x1b[1m Finished step ${e} with writes to ${Object.keys(i).length} channel${1!==Object.keys(i).length?"s":""}:\x1b[0m
`,Object.entries(i).map(([e,t])=>`- ${aV(az.yellow,e)} -> ${t.map(e=>JSON.stringify(e)).join(", ")}`).join("\n")].join(""))}(e,t,this.streamChannelsList)},maxConcurrency:s.maxConcurrency,signal:s.signal})}if("out_of_steps"===r.status)throw new J(`Recursion limit of ${s.recursionLimit} reached without hitting a stop condition. You can increase the limit by setting the "recursionLimit" config key.`,{lc_error_code:"GRAPH_RECURSION_LIMIT"})}catch(e){if(t=e,!await r.finishAndHandleError(t))throw e}finally{void 0===t&&await r.finishAndHandleError()}}async clearCache(){await this.cache?.clear([])}}class st extends ip{constructor(e=!0){super(),Object.defineProperty(this,"lc_graph_name",{enumerable:!0,configurable:!0,writable:!0,value:"EphemeralValue"}),Object.defineProperty(this,"guard",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"value",{enumerable:!0,configurable:!0,writable:!0,value:[]}),this.guard=e}fromCheckpoint(e){let t=new st(this.guard);return void 0!==e&&(t.value=[e]),t}update(e){if(0===e.length){let e=this.value.length>0;return this.value=[],e}if(1!==e.length&&this.guard)throw new er("EphemeralValue can only receive one value per step.");return this.value=[e[e.length-1]],!0}get(){if(0===this.value.length)throw new et;return this.value[0]}checkpoint(){if(0===this.value.length)throw new et;return this.value[0]}isAvailable(){return 0!==this.value.length}}class sr{constructor(e){Object.defineProperty(this,"path",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"ends",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),e8.YN.isRunnable(e.path)?this.path=e.path:this.path=(0,e8.Bp)(e.path).withConfig({runName:"Branch"}),this.ends=Array.isArray(e.pathMap)?e.pathMap.reduce((e,t)=>(e[t]=t,e),{}):e.pathMap}run(e,t){return ap.registerWriter(new as({name:"<branch_run>",trace:!1,func:async(r,i)=>{try{return await this._route(r,i,e,t)}catch(e){throw e.name===H.unminifiable_name&&console.warn("[WARN]: 'NodeInterrupt' thrown in conditional edge. This is likely a bug in your graph implementation.\nNodeInterrupt should only be thrown inside a node, not in edge conditions."),e}}}))}async _route(e,t,r,i){let a,s=await this.path.invoke(i?i(t):e,t);if(Array.isArray(s)||(s=[s]),(a=this.ends?s.map(e=>iZ(e)?e:this.ends[e]):s).some(e=>!e))throw Error("Branch condition returned unknown or null destination");if(a.filter(iZ).some(e=>e.node===iv))throw new er("Cannot send a packet to the END node");return await r(a,t)??e}}class si{constructor(){Object.defineProperty(this,"nodes",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"edges",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"branches",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"entryPoint",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"compiled",{enumerable:!0,configurable:!0,writable:!0,value:!1}),this.nodes={},this.edges=new Set,this.branches={}}warnIfCompiled(e){this.compiled&&console.warn(e)}get allEdges(){return this.edges}addNode(...e){let t=e.length>=1&&"string"!=typeof e[0]?Array.isArray(e[0])?e[0]:Object.entries(e[0]):[[e[0],e[1],e[2]]];if(0===t.length)throw Error("No nodes provided in `addNode`");for(let[e,r,i]of t){for(let t of["|",":"])if(e.includes(t))throw Error(`"${t}" is a reserved character and is not allowed in node names.`);if(this.warnIfCompiled("Adding a node to a graph that has already been compiled. This will not be reflected in the compiled graph."),e in this.nodes)throw Error(`Node \`${e}\` already present.`);if(e===iv)throw Error(`Node \`${e}\` is reserved.`);let t=(0,e8.Bp)(r);this.nodes[e]={runnable:t,metadata:i?.metadata,subgraphs:aF(t)?[t]:i?.subgraphs,ends:i?.ends}}return this}addEdge(e,t){if(this.warnIfCompiled("Adding an edge to a graph that has already been compiled. This will not be reflected in the compiled graph."),e===iv)throw Error("END cannot be a start node");if(t===iw)throw Error("START cannot be an end node");if(Array.from(this.edges).some(([t])=>t===e)&&!("channels"in this))throw Error(`Already found path for ${e}. For multiple edges, use StateGraph.`);return this.edges.add([e,t]),this}addConditionalEdges(e,t,r){let i="object"==typeof e?e:{source:e,path:t,pathMap:r};if(this.warnIfCompiled("Adding an edge to a graph that has already been compiled. This will not be reflected in the compiled graph."),!e8.YN.isRunnable(i.path)){let e=Array.isArray(i.pathMap)?i.pathMap.join(","):Object.keys(i.pathMap??{}).join(",");i.path=(0,e8.Bp)(i.path).withConfig({runName:`Branch<${i.source}${""!==e?`,${e}`:""}>`.slice(0,63)})}let a="RunnableLambda"===i.path.getName()?"condition":i.path.getName();if(this.branches[i.source]&&this.branches[i.source][a])throw Error(`Condition \`${a}\` already present for node \`${e}\``);return this.branches[i.source]??={},this.branches[i.source][a]=new sr(i),this}setEntryPoint(e){return this.warnIfCompiled("Setting the entry point of a graph that has already been compiled. This will not be reflected in the compiled graph."),this.addEdge(iw,e)}setFinishPoint(e){return this.warnIfCompiled("Setting a finish point of a graph that has already been compiled. This will not be reflected in the compiled graph."),this.addEdge(e,iv)}compile({checkpointer:e,interruptBefore:t,interruptAfter:r,name:i}={}){this.validate([...Array.isArray(t)?t:[],...Array.isArray(r)?r:[]]);let a=new sa({builder:this,checkpointer:e,interruptAfter:r,interruptBefore:t,autoValidate:!1,nodes:{},channels:{[iw]:new st,[iv]:new st},inputChannels:iw,outputChannels:iv,streamChannels:[],streamMode:"values",name:i});for(let[e,t]of Object.entries(this.nodes))a.attachNode(e,t);for(let[e,t]of this.edges)a.attachEdge(e,t);for(let[e,t]of Object.entries(this.branches))for(let[r,i]of Object.entries(t))a.attachBranch(e,r,i);return a.validate()}validate(e){let t=new Set([...this.allEdges].map(([e,t])=>e));for(let[e]of Object.entries(this.branches))t.add(e);for(let e of t)if(e!==iw&&!(e in this.nodes))throw Error(`Found edge starting at unknown node \`${e}\``);let r=new Set([...this.allEdges].map(([e,t])=>t));for(let[e,t]of Object.entries(this.branches))for(let i of Object.values(t))if(null!=i.ends)for(let e of Object.values(i.ends))r.add(e);else for(let t of(r.add(iv),Object.keys(this.nodes)))t!==e&&r.add(t);for(let e of Object.values(this.nodes))for(let t of e.ends??[])r.add(t);for(let e of Object.keys(this.nodes))if(!r.has(e))throw new ei(`Node \`${e}\` is not reachable.

If you are returning Command objects from your node,
make sure you are passing names of potential destination nodes as an "ends" array
into ".addNode(..., { ends: ["node1", "node2"] })".`,{lc_error_code:"UNREACHABLE_NODE"});for(let e of r)if(e!==iv&&!(e in this.nodes))throw Error(`Found edge ending at unknown node \`${e}\``);if(e){for(let t of e)if(!(t in this.nodes))throw Error(`Interrupt node \`${t}\` is not present`)}this.compiled=!0}}class sa extends se{constructor({builder:e,...t}){super(t),Object.defineProperty(this,"builder",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.builder=e}attachNode(e,t){this.channels[e]=new st,this.nodes[e]=new ay({channels:[],triggers:[],metadata:t.metadata,subgraphs:t.subgraphs,ends:t.ends}).pipe(t.runnable).pipe(new ap([{channel:e,value:au}],[iV])),this.streamChannels.push(e)}attachEdge(e,t){if(t===iv){if(e===iw)throw Error("Cannot have an edge from START to END");this.nodes[e].writers.push(new ap([{channel:iv,value:au}],[iV]))}else this.nodes[t].triggers.push(e),this.nodes[t].channels.push(e)}attachBranch(e,t,r){for(let i of(e!==iw||this.nodes[iw]||(this.nodes[iw]=a9.subscribeTo(iw,{tags:[iV]})),this.nodes[e].pipe(r.run(r=>new ap(r.map(r=>iZ(r)?r:{channel:r===iv?iv:`branch:${e}:${t}:${r}`,value:au}),[iV]))),r.ends?Object.values(r.ends):Object.keys(this.nodes)))if(i!==iv){let r=`branch:${e}:${t}:${i}`;this.channels[r]=new st,this.nodes[i].triggers.push(r),this.nodes[i].channels.push(r)}}async getGraphAsync(e){let t=e?.xray,r=new i7.T,i={[iw]:r.addNode({schema:q.z.any()},iw)},a={},s={};function n(e,t,s,o=!1){if(t===iv&&void 0===a[iv]&&(a[iv]=r.addNode({schema:q.z.any()},iv)),void 0!==i[e]){if(void 0===a[t])throw Error(`End node ${t} not found!`);return r.addEdge(i[e],a[t],s!==t?s:void 0,o)}}for(let[n,l]of(t&&(s=Object.fromEntries((await ao(this.getSubgraphsAsync())).filter(e=>ss(e[1])))),Object.entries(this.builder.nodes))){let c=sn(n),u=l.runnable,h=l.metadata??{};if(this.interruptBefore?.includes(n)&&this.interruptAfter?.includes(n)?h.__interrupt="before,after":this.interruptBefore?.includes(n)?h.__interrupt="before":this.interruptAfter?.includes(n)&&(h.__interrupt="after"),t){let l="number"==typeof t?t-1:t,d=void 0!==s[n]?await s[n].getGraphAsync({...e,xray:l}):u.getGraph(e);if(d.trimFirstNode(),d.trimLastNode(),Object.keys(d.nodes).length>1){let[e,t]=r.extend(d,c);if(void 0===e)throw Error(`Could not extend subgraph "${n}" due to missing entrypoint.`);function o(e,t){if(void 0!==e&&!(0,ae.A)(e))return e;if(!t||!t.lc_runnable)return t.name??"UnknownSchema";try{let e=t.getName();return e=e.startsWith("Runnable")?e.slice(8):e}catch(e){return t.getName()}}void 0!==t&&(i[c]={name:o(t.id,t.data),...t}),a[c]={name:o(e.id,e.data),...e}}else{let e=r.addNode(u,c,h);i[c]=e,a[c]=e}}else{let e=r.addNode(u,c,h);i[c]=e,a[c]=e}}for(let[e,t]of[...this.builder.allEdges].sort(([e],[t])=>e<t?-1:+(t>e)))n(sn(e),sn(t));for(let[e,t]of Object.entries(this.builder.branches)){let r={...Object.fromEntries(Object.keys(this.builder.nodes).filter(t=>t!==e).map(e=>[sn(e),sn(e)])),[iv]:iv};for(let i of Object.values(t)){let t;for(let[t,a]of Object.entries(void 0!==i.ends?i.ends:r))n(sn(e),sn(a),t,!0)}}for(let[e,t]of Object.entries(this.builder.nodes))if(void 0!==t.ends)for(let r of t.ends)n(sn(e),sn(r),void 0,!0);return r}getGraph(e){let t=e?.xray,r=new i7.T,i={[iw]:r.addNode({schema:q.z.any()},iw)},a={},s={};function n(e,t,s,o=!1){return t===iv&&void 0===a[iv]&&(a[iv]=r.addNode({schema:q.z.any()},iv)),r.addEdge(i[e],a[t],s!==t?s:void 0,o)}for(let[n,l]of(t&&(s=Object.fromEntries(al(this.getSubgraphs()).filter(e=>ss(e[1])))),Object.entries(this.builder.nodes))){let c=sn(n),u=l.runnable,h=l.metadata??{};if(this.interruptBefore?.includes(n)&&this.interruptAfter?.includes(n)?h.__interrupt="before,after":this.interruptBefore?.includes(n)?h.__interrupt="before":this.interruptAfter?.includes(n)&&(h.__interrupt="after"),t){let l="number"==typeof t?t-1:t,d=void 0!==s[n]?s[n].getGraph({...e,xray:l}):u.getGraph(e);if(d.trimFirstNode(),d.trimLastNode(),Object.keys(d.nodes).length>1){let[e,t]=r.extend(d,c);if(void 0===e)throw Error(`Could not extend subgraph "${n}" due to missing entrypoint.`);function o(e,t){if(void 0!==e&&!(0,ae.A)(e))return e;if(!t||!t.lc_runnable)return t.name??"UnknownSchema";try{let e=t.getName();return e=e.startsWith("Runnable")?e.slice(8):e}catch(e){return t.getName()}}void 0!==t&&(i[c]={name:o(t.id,t.data),...t}),a[c]={name:o(e.id,e.data),...e}}else{let e=r.addNode(u,c,h);i[c]=e,a[c]=e}}else{let e=r.addNode(u,c,h);i[c]=e,a[c]=e}}for(let[e,t]of[...this.builder.allEdges].sort(([e],[t])=>e<t?-1:+(t>e)))n(sn(e),sn(t));for(let[e,t]of Object.entries(this.builder.branches)){let r={...Object.fromEntries(Object.keys(this.builder.nodes).filter(t=>t!==e).map(e=>[sn(e),sn(e)])),[iv]:iv};for(let i of Object.values(t)){let t;for(let[t,a]of Object.entries(void 0!==i.ends?i.ends:r))n(sn(e),sn(a),t,!0)}}return r}}function ss(e){return"function"==typeof e.attachNode&&"function"==typeof e.attachEdge}function sn(e){return"subgraph"===e?`"${e}"`:e}let so=(e,t)=>e.size===t.size&&[...e].every(e=>t.has(e));class sl extends ip{constructor(e){super(),Object.defineProperty(this,"lc_graph_name",{enumerable:!0,configurable:!0,writable:!0,value:"NamedBarrierValue"}),Object.defineProperty(this,"names",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"seen",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.names=e,this.seen=new Set}fromCheckpoint(e){let t=new sl(this.names);return void 0!==e&&(t.seen=new Set(e)),t}update(e){let t=!1;for(let r of e)if(this.names.has(r))this.seen.has(r)||(this.seen.add(r),t=!0);else throw new er(`Value ${JSON.stringify(r)} not in names ${JSON.stringify(this.names)}`);return t}get(){if(!so(this.names,this.seen))throw new et}checkpoint(){return[...this.seen]}consume(){return!!(this.seen&&this.names&&so(this.seen,this.names))&&(this.seen=new Set,!0)}isAvailable(){return!!this.names&&so(this.names,this.seen)}}class sc extends ip{constructor(e){super(),Object.defineProperty(this,"lc_graph_name",{enumerable:!0,configurable:!0,writable:!0,value:"NamedBarrierValueAfterFinish"}),Object.defineProperty(this,"names",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"seen",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"finished",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),this.names=e,this.seen=new Set,this.finished=!1}fromCheckpoint(e){let t=new sc(this.names);if(void 0!==e){let[r,i]=e;t.seen=new Set(r),t.finished=i}return t}update(e){let t=!1;for(let r of e)if(this.names.has(r)&&!this.seen.has(r))this.seen.add(r),t=!0;else if(!this.names.has(r))throw new er(`Value ${JSON.stringify(r)} not in names ${JSON.stringify(this.names)}`);return t}get(){if(!this.finished||!so(this.names,this.seen))throw new et}checkpoint(){return[[...this.seen],this.finished]}consume(){return!!(this.finished&&this.seen&&this.names&&so(this.seen,this.names))&&(this.seen=new Set,this.finished=!1,!0)}finish(){return!!(!this.finished&&this.names&&so(this.names,this.seen))&&(this.finished=!0,!0)}isAvailable(){return this.finished&&!!this.names&&so(this.names,this.seen)}}class su{constructor(){Object.defineProperty(this,"_map",{enumerable:!0,configurable:!0,writable:!0,value:new WeakMap}),Object.defineProperty(this,"_extensionCache",{enumerable:!0,configurable:!0,writable:!0,value:new Map})}get(e){return this._map.get(e)}extend(e,t){let r=this.get(e);this._map.set(e,t(r))}remove(e){return this._map.delete(e),this}has(e){return this._map.has(e)}getChannelsForSchema(e){let t={};for(let[r,i]of Object.entries((0,tc.PA)(e))){let e=this.get(i);e?.reducer?t[r]=new ib(e.reducer.fn,e.default):t[r]=new iy}return t}getExtendedChannelSchemas(e,t){if(0===Object.keys(t).length)return e;let r=Object.entries(t).filter(([,e])=>!0===e).sort(([e],[t])=>e.localeCompare(t)).map(([e,t])=>`${e}:${t}`).join("|"),i=this._extensionCache.get(r)??new WeakMap;if(i.has(e))return i.get(e);let a=e;if(t.withReducerSchema||t.withJsonSchemaExtrasAsDescription){let r=Object.entries((0,tc.PA)(e)).map(([e,r])=>{let i=this.get(r),a=t.withReducerSchema?i?.reducer?.schema??r:r;if(t.withJsonSchemaExtrasAsDescription&&i?.jsonSchemaExtra){let e=(0,tc.cg)(a)??(0,tc.cg)(r),t=JSON.stringify({...i.jsonSchemaExtra,description:e});a=a.describe(`lg:${t}`)}return[e,a]});a=(0,tc.W0)(e,Object.fromEntries(r)),(0,tc.IU)(a)&&(a._def.unknownKeys="strip")}return t.asPartial&&(a=(0,tc.BA)(a)),i.set(e,a),this._extensionCache.set(r,i),a}}let sh=new su,sd="__root__",sp=Symbol.for("langgraph.state.partial");class sm extends si{constructor(e,t){if(super(),Object.defineProperty(this,"channels",{enumerable:!0,configurable:!0,writable:!0,value:{}}),Object.defineProperty(this,"waitingEdges",{enumerable:!0,configurable:!0,writable:!0,value:new Set}),Object.defineProperty(this,"_schemaDefinition",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"_schemaRuntimeDefinition",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"_inputDefinition",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"_inputRuntimeDefinition",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"_outputDefinition",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"_outputRuntimeDefinition",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"_schemaDefinitions",{enumerable:!0,configurable:!0,writable:!0,value:new Map}),Object.defineProperty(this,"_metaRegistry",{enumerable:!0,configurable:!0,writable:!0,value:sh}),Object.defineProperty(this,"_configSchema",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"_configRuntimeSchema",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),function(e){return"object"==typeof e&&null!=e&&"state"in e&&!!(0,tc.QV)(e.state)&&(!("input"in e)||!!(0,tc.QV)(e.input))&&(!("output"in e)||!!(0,tc.QV)(e.output))}(e)){let t=this._metaRegistry.getChannelsForSchema(e.state),r=null!=e.input?this._metaRegistry.getChannelsForSchema(e.input):t,i=null!=e.output?this._metaRegistry.getChannelsForSchema(e.output):t;this._schemaDefinition=t,this._schemaRuntimeDefinition=e.state,this._inputDefinition=r,this._inputRuntimeDefinition=e.input??sp,this._outputDefinition=i,this._outputRuntimeDefinition=e.output??e.state}else if((0,tc.QV)(e)){let t=this._metaRegistry.getChannelsForSchema(e);this._schemaDefinition=t,this._schemaRuntimeDefinition=e,this._inputDefinition=t,this._inputRuntimeDefinition=sp,this._outputDefinition=t,this._outputRuntimeDefinition=e}else if(function(e){return"object"==typeof e&&null!==e&&void 0===e.stateSchema&&void 0!==e.input&&void 0!==e.output}(e))this._schemaDefinition=e.input.spec,this._inputDefinition=e.input.spec,this._outputDefinition=e.output.spec;else if(function(e){return"object"==typeof e&&null!==e&&void 0!==e.stateSchema}(e))this._schemaDefinition=e.stateSchema.spec,this._inputDefinition=e.input?.spec??this._schemaDefinition,this._outputDefinition=e.output?.spec??this._schemaDefinition;else if(function(e){return"object"==typeof e&&null!==e&&!Array.isArray(e)&&Object.keys(e).length>0&&Object.values(e).every(e=>"function"==typeof e||id(e))}(e)||sg(e)){let t=sg(e)?e.spec:e;this._schemaDefinition=t}else if(function(e){return"object"==typeof e&&null!==e&&void 0!==e.channels}(e)){let t=function(e){let t={};for(let[r,i]of Object.entries(e))t[r]=i9(i);return t}(e.channels);this._schemaDefinition=t}else throw Error("Invalid StateGraph input. Make sure to pass a valid Annotation.Root or Zod schema.");this._inputDefinition??=this._schemaDefinition,this._outputDefinition??=this._schemaDefinition,this._addSchema(this._schemaDefinition),this._addSchema(this._inputDefinition),this._addSchema(this._outputDefinition),(0,tc.QV)(t)&&(this._configRuntimeSchema=t)}get allEdges(){return new Set([...this.edges,...Array.from(this.waitingEdges).flatMap(([e,t])=>e.map(e=>[e,t]))])}_addSchema(e){if(!this._schemaDefinitions.has(e))for(let[t,r]of(this._schemaDefinitions.set(e,e),Object.entries(e))){let e;if(e="function"==typeof r?r():r,void 0!==this.channels[t]){if(this.channels[t]!==e&&!i3(e)&&"LastValue"!==e.lc_graph_name)throw Error(`Channel "${t}" already exists with a different type.`)}else this.channels[t]=e}}addNode(...e){let t=e.length>=1&&"string"!=typeof e[0]?Array.isArray(e[0])?e[0]:Object.entries(e[0]).map(([e,t])=>[e,t,t[Symbol.for("langgraph.state.node")]??void 0]):[[e[0],e[1],e[2]]];if(0===t.length)throw Error("No nodes provided in `addNode`");for(let[e,r,i]of t){let t;if(e in this.channels)throw Error(`${e} is already being used as a state attribute (a.k.a. a channel), cannot also be used as a node name.`);for(let t of["|",":"])if(e.includes(t))throw Error(`"${t}" is a reserved character and is not allowed in node names.`);if(this.warnIfCompiled("Adding a node to a graph that has already been compiled. This will not be reflected in the compiled graph."),e in this.nodes)throw Error(`Node \`${e}\` already present.`);if(e===iv||e===iw)throw Error(`Node \`${e}\` is reserved.`);let a=this._schemaDefinition;i?.input!==void 0&&((0,tc.QV)(i.input)?a=this._metaRegistry.getChannelsForSchema(i.input):void 0!==i.input.spec&&(a=i.input.spec)),void 0!==a&&this._addSchema(a),t=e8.YN.isRunnable(r)?r:"function"==typeof r?new as({func:r,name:e,trace:!1}):(0,e8.Bp)(r);let s=i?.cachePolicy;"boolean"==typeof s&&(s=s?{}:void 0);let n={runnable:t,retryPolicy:i?.retryPolicy,cachePolicy:s,metadata:i?.metadata,input:a??this._schemaDefinition,subgraphs:aF(t)?[t]:i?.subgraphs,ends:i?.ends,defer:i?.defer};this.nodes[e]=n}return this}addEdge(e,t){if("string"==typeof e)return super.addEdge(e,t);for(let t of(this.compiled&&console.warn("Adding an edge to a graph that has already been compiled. This will not be reflected in the compiled graph."),e)){if(t===iv)throw Error("END cannot be a start node");if(!Object.keys(this.nodes).some(e=>e===t))throw Error(`Need to add a node named "${t}" first`)}if(t===iv)throw Error("END cannot be an end node");if(!Object.keys(this.nodes).some(e=>e===t))throw Error(`Need to add a node named "${t}" first`);return this.waitingEdges.add([e,t]),this}addSequence(e){let t,r=Array.isArray(e)?e:Object.entries(e).map(([e,t])=>[e,t,t[Symbol.for("langgraph.state.node")]??void 0]);if(0===r.length)throw Error("Sequence requires at least one node.");for(let[e,i,a]of r){if(e in this.nodes)throw Error(`Node names must be unique: node with the name "${e}" already exists.`);this.addNode(e,i,a),null!=t&&this.addEdge(t,e),t=e}return this}compile({checkpointer:e,store:t,cache:r,interruptBefore:i,interruptAfter:a,name:s}={}){this.validate([...Array.isArray(i)?i:[],...Array.isArray(a)?a:[]]);let n=Object.keys(this._schemaDefinitions.get(this._outputDefinition)),o=1===n.length&&n[0]===sd?sd:n,l=Object.keys(this.channels),c=1===l.length&&l[0]===sd?sd:l,u=new sf({builder:this,checkpointer:e,interruptAfter:a,interruptBefore:i,autoValidate:!1,nodes:{},channels:{...this.channels,[iw]:new st},inputChannels:iw,outputChannels:o,streamChannels:c,streamMode:"updates",store:t,cache:r,name:s});for(let[e,t]of(u.attachNode(iw),Object.entries(this.nodes)))u.attachNode(e,t);for(let[e]of(u.attachBranch(iw,iK,sy(),{withReader:!1}),Object.entries(this.nodes)))u.attachBranch(e,iK,sy(),{withReader:!1});for(let[e,t]of this.edges)u.attachEdge(e,t);for(let[e,t]of this.waitingEdges)u.attachEdge(e,t);for(let[e,t]of Object.entries(this.branches))for(let[r,i]of Object.entries(t))u.attachBranch(e,r,i);return u.validate()}}class sf extends sa{constructor(){super(...arguments),Object.defineProperty(this,"_metaRegistry",{enumerable:!0,configurable:!0,writable:!0,value:sh})}attachNode(e,t){let r,i=[{value:au,mapper:new as({func:(r=e===iw?Object.entries(this.builder._schemaDefinitions.get(this.builder._inputDefinition)).filter(([e,t])=>!i3(t)).map(([e])=>e):Object.keys(this.builder.channels)).length&&r[0]===sd?function(e){if(i0(e))return e.graph===iQ.PARENT?null:e._updateAsTuples();if(Array.isArray(e)&&e.length>0&&e.some(e=>i0(e))){let t=[];for(let r of e)if(i0(r)){if(r.graph===iQ.PARENT)continue;t.push(...r._updateAsTuples())}else t.push([sd,r]);return t}return null!=e?[[sd,e]]:null}:function t(i){if(!i)return null;if(i0(i))return i.graph===iQ.PARENT?null:i._updateAsTuples().filter(([e])=>r.includes(e));if(Array.isArray(i)&&i.length>0&&i.some(i0)){let e=[];for(let a of i)if(i0(a)){if(a.graph===iQ.PARENT)continue;e.push(...a._updateAsTuples().filter(([e])=>r.includes(e)))}else{let r=t(a);r&&e.push(...r??[])}return e}{if("object"==typeof i&&!Array.isArray(i))return Object.entries(i).filter(([e])=>r.includes(e));let t=Array.isArray(i)?"array":typeof i;throw new er(`Expected node "${e.toString()}" to return an object or an array containing at least one Command object, received ${t}`,{lc_error_code:"INVALID_GRAPH_NODE_RETURN_VALUE"})}},trace:!1,recurse:!1})}];if(e===iw)this.nodes[e]=new ay({tags:[iV],triggers:[iw],channels:[iw],writers:[new ap(i,[iV])]});else{let r=t?.input??this.builder._schemaDefinition,a=Object.fromEntries(Object.keys(this.builder._schemaDefinitions.get(r)).map(e=>[e,e])),s=1===Object.keys(a).length&&sd in a,n=`branch:to:${e}`;this.channels[n]=t?.defer?new i_:new st(!1),this.nodes[e]=new ay({triggers:[n],channels:s?Object.keys(a):a,writers:[new ap(i,[iV])],mapper:s?void 0:e=>Object.fromEntries(Object.entries(e).filter(([e])=>e in a)),bound:t?.runnable,metadata:t?.metadata,retryPolicy:t?.retryPolicy,cachePolicy:t?.cachePolicy,subgraphs:t?.subgraphs,ends:t?.ends})}}attachEdge(e,t){if(t!==iv){if("string"==typeof e)this.nodes[e].writers.push(new ap([{channel:`branch:to:${t}`,value:null}],[iV]));else if(Array.isArray(e)){let r=`join:${e.join("+")}:${t}`;for(let i of(this.channels[r]=this.builder.nodes[t].defer?new sc(new Set(e)):new sl(new Set(e)),this.nodes[t].triggers.push(r),e))this.nodes[i].writers.push(new ap([{channel:r,value:i}],[iV]))}}}attachBranch(e,t,r,i={withReader:!0}){let a=async(t,r)=>{let i=t.filter(e=>e!==iv);if(!i.length)return;let a=i.map(t=>iZ(t)?t:{channel:t===iv?t:`branch:to:${t}`,value:e});await ap.doWrite({...r,tags:(r.tags??[]).concat([iV])},a)};this.nodes[e].writers.push(r.run(a,i.withReader?e=>ag.doRead(e,this.streamChannels??this.outputChannels,!0):void 0))}async _validateInput(e){if(null==e)return e;let t=(()=>{let e=this.builder._inputRuntimeDefinition,t=this.builder._schemaRuntimeDefinition,r=e=>{if(null!=e)return this._metaRegistry.getExtendedChannelSchemas(e,{withReducerSchema:!0})};return(0,tc.QV)(e)?r(e):e===sp?(0,tc.BA)(r(t)):void 0})();return i0(e)?(e.update&&null!=t&&(e.update=(0,tc.Zu)(t,e.update)),e):null!=t?(0,tc.Zu)(t,e):e}async _validateConfigurable(e){let t=this.builder._configRuntimeSchema;return(0,tc.QV)(t)&&(0,tc.Zu)(t,e),e}}function sg(e){return"object"==typeof e&&null!==e&&"lc_graph_name"in e&&"AnnotationRoot"===e.lc_graph_name}function sb(e){if(iZ(e))return[e];let t=[];i0(e)?t.push(e):Array.isArray(e)&&t.push(...e.filter(i0));let r=[];for(let e of t){if(e.graph===iQ.PARENT)throw new X(e);iZ(e.goto)||"string"==typeof e.goto?r.push(e.goto):Array.isArray(e.goto)&&r.push(...e.goto)}return r}function sy(){return new sr({path:new as({func:sb,tags:[iV],trace:!1,recurse:!1,name:"<control_branch>"})})}var s_=r(82116);function sw(e,t){let r,i=Array.isArray(e)?e:[e],a=Array.isArray(t)?t:[t],s=i.map(eq.coerceMessageLikeToMessage),n=a.map(eq.coerceMessageLikeToMessage);for(let e of s)(null===e.id||void 0===e.id)&&(e.id=(0,s_.A)(),e.lc_kwargs.id=e.id);for(let e=0;e<n.length;e+=1){let t=n[e];(null===t.id||void 0===t.id)&&(t.id=(0,s_.A)(),t.lc_kwargs.id=t.id),"remove"===t.getType()&&"__remove_all__"===t.id&&(r=e)}if(null!=r)return n.slice(r+1);let o=[...s],l=new Map(o.map((e,t)=>[e.id,t])),c=new Set;for(let e of n){let t=l.get(e.id);if(void 0!==t)"remove"===e.getType()?c.add(e.id):(c.delete(e.id),o[t]=e);else{if("remove"===e.getType())throw Error(`Attempting to delete a message with an ID that doesn't exist ('${e.id}')`);l.set(e.id,o.length),o.push(e)}}return o.filter(e=>!c.has(e.id))}i6.Root({messages:i6({reducer:sw,default:()=>[]})}),q.z.object({messages:function(e,t){if(t.reducer&&!t.default){let r=(0,tc.RA)(e);null!=r&&(t.default=r)}if(!t.reducer)return sh.extend(e,()=>t),e;{let r=Object.assign(e,{lg_reducer_schema:t.reducer?.schema??e});return sh.extend(r,()=>t),r}}(q.z.custom(),{reducer:{schema:q.z.custom(),fn:sw},jsonSchemaExtra:{langgraph_type:"messages"},default:()=>[]})}),V.Nx.initializeGlobalInstance(new K.AsyncLocalStorage),e8.fJ;class sv extends as{constructor(e,t){let{name:r,tags:i,handleToolErrors:a}=t??{};super({name:r,tags:i,func:(e,t)=>this.run(e,t)}),Object.defineProperty(this,"tools",{enumerable:!0,configurable:!0,writable:!0,value:void 0}),Object.defineProperty(this,"handleToolErrors",{enumerable:!0,configurable:!0,writable:!0,value:!0}),Object.defineProperty(this,"trace",{enumerable:!0,configurable:!0,writable:!0,value:!1}),this.tools=e,this.handleToolErrors=a??this.handleToolErrors}async run(e,t){let r=Array.isArray(e)?e[e.length-1]:e.messages[e.messages.length-1];if(r?._getType()!=="ai")throw Error("ToolNode only accepts AIMessages as input.");let i=await Promise.all(r.tool_calls?.map(async e=>{let r=this.tools.find(t=>t.name===e.name);try{if(void 0===r)throw Error(`Tool "${e.name}" not found.`);let i=await r.invoke({...e,type:"tool_call"},t);if((0,eq.isBaseMessage)(i)&&"tool"===i._getType()||i0(i))return i;return new eq.ToolMessage({name:r.name,content:"string"==typeof i?i:JSON.stringify(i),tool_call_id:e.id})}catch(t){if(!this.handleToolErrors||Q(t))throw t;return new eq.ToolMessage({content:`Error: ${t.message}
 Please fix your mistakes.`,name:e.name,tool_call_id:e.id??""})}})??[]);if(!i.some(i0))return Array.isArray(e)?i:{messages:i};let a=[],s=null;for(let t of i)i0(t)?t.graph===iQ.PARENT&&Array.isArray(t.goto)&&t.goto.every(e=>iZ(e))?s?s.goto.push(...t.goto):s=new iQ({graph:iQ.PARENT,goto:t.goto}):a.push(t):a.push(Array.isArray(e)?[t]:{messages:[t]});return s&&a.push(s),a}}let sk=/<name>(.*?)<\/name>/s,sO=/<content>(.*?)<\/content>/s;function sx(e){if(!((0,eq.isBaseMessage)(e)&&((0,eq.isAIMessage)(e)||(0,eq.isBaseMessageChunk)(e)&&(0,eq.isAIMessageChunk)(e)))||!e.name)return e;let{name:t}=e;if("string"==typeof e.content)return new eq.AIMessage({...Object.keys(e.lc_kwargs??{}).length>0?e.lc_kwargs:e,content:`<name>${t}</name><content>${e.content}</content>`,name:void 0});let r=[],i=0;for(let a of e.content)"string"==typeof a?(i+=1,r.push(`<name>${t}</name><content>${a}</content>`)):"object"==typeof a&&"type"in a&&"text"===a.type?(i+=1,r.push({...a,text:`<name>${t}</name><content>${a.text}</content>`})):r.push(a);return i||r.unshift({type:"text",text:`<name>${t}</name><content></content>`}),new eq.AIMessage({...e.lc_kwargs,content:r,name:void 0})}function sS(e){let t;if(!(0,eq.isAIMessage)(e)||!e.content)return e;let r=[];if(Array.isArray(e.content))r=e.content.filter(e=>{if("text"===e.type){let r=e.text.match(sk),i=e.text.match(sO);if(r&&(!i||""===i[1]))return t=r[1],!1}return!0}).map(e=>{if("text"===e.type){let r=e.text.match(sk),i=e.text.match(sO);return r&&i?(t=r[1],{...e,text:i[1]}):e}return e});else{let i=e.content,a=i.match(sk),s=i.match(sO);if(!a||!s)return e;t=a[1],r=s[1]}return new eq.AIMessage({...Object.keys(e.lc_kwargs??{}).length>0?e.lc_kwargs:e,content:r,name:t})}let sC="prompt";function sA(e){return e8.YN.isRunnable(e)}function sT(e){return"invoke"in e&&"function"==typeof e.invoke&&"_modelType"in e}function sP(e){return"_queuedMethodOperations"in e&&"_model"in e&&"function"==typeof e._model}async function sE(e,t){let r=e;if(e8.zZ.isRunnableSequence(r)&&(r=r.steps.find(e=>e8.fJ.isRunnableBinding(e)||sT(e)||sP(e))||r),sP(r)&&(r=await r._model()),!e8.fJ.isRunnableBinding(r)||!r.kwargs||"object"!=typeof r.kwargs||!("tools"in r.kwargs))return!0;let i=r.kwargs.tools;if(1===i.length&&"functionDeclarations"in i[0]&&(i=i[0].functionDeclarations),t.length!==i.length)throw Error("Number of tools in the model.bindTools() and tools passed to createReactAgent must match");let a=new Set(t.flatMap(e=>sA(e)?e.name:[])),s=new Set;for(let e of i){let t;if("type"in e&&"function"===e.type)t=e.function.name;else if("name"in e)t=e.name;else{if(!("toolSpec"in e)||!("name"in e.toolSpec))continue;t=e.toolSpec.name}t&&s.add(t)}let n=[...a].filter(e=>!s.has(e));if(n.length>0)throw Error(`Missing tools '${n}' in the model.bindTools().Tools in the model.bindTools() must match the tools passed to createReactAgent.`);return!1}async function sj(e){let t=e;if(e8.zZ.isRunnableSequence(t)&&(t=t.steps.find(e=>e8.fJ.isRunnableBinding(e)||sT(e)||sP(e))||t),sP(t)&&(t=await t._model()),e8.fJ.isRunnableBinding(t)&&(t=t.bound),!sT(t))throw Error(`Expected \`llm\` to be a ChatModel or RunnableBinding (e.g. llm.bind_tools(...)) with invoke() and generate() methods, got ${t.constructor.name}`);return t}let sI=()=>i6.Root({messages:i6({reducer:sw,default:()=>[]}),structuredResponse:i6}),sN=i6.Root({llmInputMessages:i6({reducer:sw,default:()=>[]})});var sR=r(55511),sM=r.n(sR);q.z.object({custom_api_config_id:q.z.string(),role:q.z.string().optional(),messages:q.z.array(q.z.any()),model:q.z.string().optional(),temperature:q.z.number().optional(),max_tokens:q.z.number().optional(),stream:q.z.boolean().optional(),specific_api_key_id:q.z.string().optional()});let sD={LLM_REQUEST:1e4,GOOGLE_CLASSIFICATION:1e4,GOOGLE_ORCHESTRATION:3e4},s$=new Map;function sL(e,t,r){let i=e.map(e=>`${e.role}:${"string"==typeof e.content?e.content:JSON.stringify(e.content)}`).join("|"),a=`${t}|${i}|${r||0}`;return sM().createHash("md5").update(a).digest("hex")}async function sU(e,t,r=3,i,a=!1){let s,n=i;n||(n=e.includes("generativelanguage.googleapis.com")?a?sD.GOOGLE_ORCHESTRATION:sD.GOOGLE_CLASSIFICATION:sD.LLM_REQUEST);for(let i=0;i<=r;i++)try{let r=new AbortController,i=setTimeout(()=>r.abort(),n),a=await fetch(e,{...t,signal:r.signal});return clearTimeout(i),a}catch(e){if(s=e,i<r){let e=Math.min(1e3*Math.pow(2,i),5e3);await new Promise(t=>setTimeout(t,e))}}throw s}async function sF(e,t,r,i){let a,s,n,o,l,c=null,u=new Date;if(!i.stream&&i.messages&&t){let e=sL(i.messages,t,i.temperature),r=s$.get(e);if(r&&Date.now()-r.timestamp<12e4)return{success:!0,response:void 0,responseData:r.response,responseHeaders:new Headers({"x-rokey-cache":"hit"}),status:200,error:null,llmRequestTimestamp:new Date,llmResponseTimestamp:new Date}}try{let h=function(e,t){if(!e)return null;if("openrouter"===t.toLowerCase())return e;let r=e.split("/");return r.length>1?r[r.length-1]:e}(t,e||""),d=e?.toLowerCase()==="openrouter"?t:h;if(!d)throw{message:`Effective model ID is missing for provider ${e} (DB Model: ${t})`,status:500,internal:!0};if(c=new Date,e?.toLowerCase()==="openai"){let{custom_api_config_id:e,role:t,...c}=i,h={...c,model:d,messages:i.messages,stream:i.stream};Object.keys(h).forEach(e=>void 0===h[e]&&delete h[e]);let p={method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${r}`,"User-Agent":"RoKey/1.0",Origin:"https://rokey.app","Cache-Control":"no-cache"},body:JSON.stringify(h)},m=await sU("https://api.openai.com/v1/chat/completions",p);if(u=new Date,a=m.status,l=m.headers,!m.ok){let e=await m.json().catch(()=>({error:{message:m.statusText}}));throw s={message:`OpenAI Error: ${e?.error?.message||m.statusText}`,status:m.status,provider_error:e}}if(i.stream){if(!m.body)throw s={message:"OpenAI stream body null",status:500};n=m,o={note:"streamed"}}else o=await m.json()}else if(e?.toLowerCase()==="openrouter"){let{custom_api_config_id:e,role:t,...c}=i,h={...c,model:d,messages:i.messages,stream:i.stream,usage:{include:!0}};Object.keys(h).forEach(e=>void 0===h[e]&&delete h[e]);let p={method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${r}`,"HTTP-Referer":"https://rokey.app","X-Title":"RoKey","User-Agent":"RoKey/1.0",Origin:"https://rokey.app","Cache-Control":"no-cache"},body:JSON.stringify(h)},m=await sU("https://openrouter.ai/api/v1/chat/completions",p);if(u=new Date,a=m.status,l=m.headers,!m.ok){let e=await m.json().catch(()=>({error:{message:m.statusText}}));throw s={message:`OpenRouter Error: ${e?.error?.message||m.statusText}`,status:m.status,provider_error:e}}if(i.stream){if(!m.body)throw s={message:"OpenRouter stream body null",status:500};n=m,o={note:"streamed"}}else o=await m.json()}else if(e?.toLowerCase()==="google"){let e=d?.replace(/^models\//,"")||d,{custom_api_config_id:t,role:c,...h}=i,p={model:e,messages:i.messages,stream:i.stream||!1};void 0!==i.temperature&&(p.temperature=i.temperature),void 0!==i.max_tokens&&(p.max_tokens=i.max_tokens);let m={method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${r}`,"User-Agent":"RoKey/1.0",Origin:"https://rokey.app","Cache-Control":"no-cache"},body:JSON.stringify(p)},f=void 0!==i.role,g=await sU("https://generativelanguage.googleapis.com/v1beta/openai/chat/completions",m,3,void 0,f);if(u=new Date,a=g.status,l=g.headers,!g.ok){let e=await g.json().catch(()=>({error:{message:g.statusText}})),t=e?.error?.message||g.statusText;throw Array.isArray(e)&&e[0]?.error?.message&&(t=e[0].error.message),s={message:`Google Error: ${t}`,status:g.status,provider_error:e}}if(i.stream){if(!g.body)throw s={message:"Google stream body null",status:500};n=g,o={note:"streamed"}}else o=await g.json()}else if(e?.toLowerCase()==="anthropic"){let{custom_api_config_id:e,role:t,...c}=i,h={...c,model:d,messages:i.messages,stream:i.stream};Object.keys(h).forEach(e=>void 0===h[e]&&delete h[e]);let p={method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${r}`,"User-Agent":"RoKey/1.0",Origin:"https://rokey.app","Cache-Control":"no-cache"},body:JSON.stringify(h)},m=await sU("https://api.anthropic.com/v1/chat/completions",p);if(u=new Date,a=m.status,l=m.headers,!m.ok){let e=await m.json().catch(()=>({error:{message:m.statusText}}));throw s={message:`Anthropic Error: ${e?.error?.message||m.statusText}`,status:m.status,provider_error:e}}if(i.stream){if(!m.body)throw s={message:"Anthropic stream body null",status:500};n=m,o={note:"streamed"}}else o=await m.json()}else if(e?.toLowerCase()==="deepseek"){let{custom_api_config_id:e,role:t,...c}=i,h={...c,model:d,messages:i.messages,stream:i.stream};Object.keys(h).forEach(e=>void 0===h[e]&&delete h[e]);let p={method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${r}`,"User-Agent":"RoKey/1.0",Origin:"https://rokey.app","Cache-Control":"no-cache"},body:JSON.stringify(h)},m=await sU("https://api.deepseek.com/v1/chat/completions",p);if(u=new Date,a=m.status,l=m.headers,!m.ok){let e=await m.json().catch(()=>({error:{message:m.statusText}}));throw s={message:`DeepSeek Error: ${e?.error?.message||m.statusText}`,status:m.status,provider_error:e}}if(i.stream){if(!m.body)throw s={message:"DeepSeek stream body null",status:500};n=m,o={note:"streamed"}}else o=await m.json()}else if(e?.toLowerCase()==="xai"){let{custom_api_config_id:e,role:t,...c}=i,h={...c,model:d,messages:i.messages,stream:i.stream||!1};Object.keys(h).forEach(e=>void 0===h[e]&&delete h[e]);let p={method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${r}`,"User-Agent":"RoKey/1.0",Origin:"https://rokey.app","Cache-Control":"no-cache"},body:JSON.stringify(h)},m=await sU("https://api.x.ai/v1/chat/completions",p);if(u=new Date,a=m.status,l=m.headers,!m.ok){let e=await m.json().catch(()=>({error:{message:m.statusText}}));throw s={message:`XAI/Grok Error: ${e?.error?.message||m.statusText} (Type: ${e?.error?.type})`,status:m.status,provider_error:e}}if(i.stream){if(!m.body)throw s={message:"XAI stream body null",status:500};n=m,o={note:"streamed"}}else o=await m.json()}else throw s={message:`Provider '${e}' is configured but not supported by RoKey proxy (executeProviderRequest).`,status:501,internal:!0};if(!i.stream&&o&&i.messages&&t){let r=sL(i.messages,t,i.temperature);if(s$.set(r,{response:o,timestamp:Date.now(),provider:e||"unknown",model:t}),s$.size>1e3){let e=Array.from(s$.entries());e.sort((e,t)=>e[1].timestamp-t[1].timestamp);let t=Math.floor(.2*e.length);for(let r=0;r<t;r++)s$.delete(e[r][0])}}return{success:!0,response:n,responseData:o,responseHeaders:l,status:a,error:null,llmRequestTimestamp:c,llmResponseTimestamp:u}}catch(i){let e=s||i,t="ProviderCommsError",r="";return"AbortError"===i.name?(t="TimeoutError",r="Request timed out (timeout varies by provider and context)"):i.message?.includes("fetch failed")?(t="NetworkError",r="Network connection failed - check internet connectivity"):"ENOTFOUND"===i.code?(t="DNSError",r="DNS resolution failed - check network settings"):"ECONNREFUSED"===i.code&&(t="ConnectionRefused",r="Connection refused by server"),{success:!1,status:e.status||500,error:e.provider_error||{message:`${e.message}${r?` (${r})`:""}`,type:e.internal?"RoKeyInternal":t,diagnostic:r},llmRequestTimestamp:c||new Date,llmResponseTimestamp:u||new Date,response:void 0,responseData:void 0,responseHeaders:l}}}class sq extends tp{constructor(e){super({}),this.apiKey=e.apiKey,this.temperature=e.temperature??.7,this.maxTokens=e.maxTokens??2e3,this.streaming=e.streaming??!1,this.customApiConfigId=e.customApiConfigId}_llmType(){return"rokey-llm"}convertMessagesToRouKeyFormat(e){return e.map(e=>e instanceof eq.HumanMessage?{role:"user",content:e.content}:e instanceof eq.AIMessage?{role:"assistant",content:e.content}:e instanceof eq.SystemMessage?{role:"system",content:e.content}:{role:"user",content:e.content})}async _generate(e,t,r){try{let r=this.convertMessagesToRouKeyFormat(e),i={custom_api_config_id:this.customApiConfigId||"unknown",messages:r,temperature:this.temperature,max_tokens:this.maxTokens,stream:this.streaming,role:"orchestration",...t};this.customApiConfigId;let a=await (0,U.Y)(this.apiKey.encrypted_api_key),s=await sF(this.apiKey.provider,this.apiKey.predefined_model_id,a,i);if(!s.success||!s.responseData){let e=s.error?.message||s.error||"Unknown error",t=s.error?.type||"Unknown";throw Error(`RouKey LLM call failed: ${e} (${t})`)}let n=s.responseData.choices?.[0]?.message?.content||"",o=s.responseData.choices?.[0]?.finish_reason||"stop";if(!n||0===n.length){let e=s.responseData.content||s.responseData.text||s.responseData.response||s.responseData.choices?.[0]?.text||s.responseData.choices?.[0]?.content||"";if(e)return{generations:[{message:new eq.AIMessage(e),text:e,generationInfo:{finishReason:o,usage:s.responseData.usage,provider:this.apiKey.provider,model:this.apiKey.predefined_model_id,extractionMethod:"alternative"}}],llmOutput:{tokenUsage:s.responseData.usage,provider:this.apiKey.provider,model:this.apiKey.predefined_model_id}};throw Error(`No content found in response from ${this.apiKey.provider}. Response structure: ${JSON.stringify(s.responseData)}`)}return{generations:[{message:new eq.AIMessage(n),text:n,generationInfo:{finishReason:o,usage:s.responseData.usage,provider:this.apiKey.provider,model:this.apiKey.predefined_model_id}}],llmOutput:{tokenUsage:s.responseData.usage,provider:this.apiKey.provider,model:this.apiKey.predefined_model_id}}}catch(e){throw Error(`RouKey LLM failed: ${e instanceof Error?e.message:"Unknown error"}`)}}async *_streamResponseChunks(e,t,r){let i=await this._generate(e,t,r);i.generations.length>0&&(yield{chunk:i.generations[0].message,generationInfo:i.generations[0].generationInfo})}get identifyingParams(){return{provider:this.apiKey.provider,model:this.apiKey.predefined_model_id,temperature:this.temperature,maxTokens:this.maxTokens,apiKeyId:this.apiKey.id}}withParameters(e){return new sq({apiKey:e.apiKey??this.apiKey,temperature:e.temperature??this.temperature,maxTokens:e.maxTokens??this.maxTokens,streaming:e.streaming??this.streaming,customApiConfigId:e.customApiConfigId??this.customApiConfigId})}getModelInfo(){return`${this.apiKey.provider}/${this.apiKey.predefined_model_id} (${this.apiKey.label})`}bindTools(e){return this}async invoke(e,t){let r;if(Array.isArray(e))r=e;else if(e.messages)r=e.messages;else if("string"==typeof e)r=[new eq.HumanMessage(e)];else throw Error("Invalid input format for RouKeyLLM.invoke()");let i=await this._generate(r,t),a=i.generations[0]?.message;if(!a||!a.content||0===a.content.length)throw Error(`RouKey LLM invoke() failed: No content generated by ${this.apiKey.provider}`);return a}}class sz{constructor(e,t){this.agents={},this.workflow=null,this.config=e,this.progressCallback=t}async initializeAgents(){for(let e of this.config.roles){let t=this.config.userApiKeys[e];if(!t)continue;let r=new sq({apiKey:t,temperature:this.getTemperatureForRole(e),maxTokens:this.getMaxTokensForRole(e),customApiConfigId:this.config.customApiConfigId});this.config.customApiConfigId;let i=this.getSystemPromptForRole(e),a=this.getToolsForRole(e),s=function(e){let t,r,i,{llm:a,tools:s,messageModifier:n,stateModifier:o,prompt:l,stateSchema:c,checkpointSaver:u,checkpointer:h,interruptBefore:d,interruptAfter:p,store:m,responseFormat:f,preModelHook:g,postModelHook:b,name:y,includeAgentName:_}=e;Array.isArray(s)?r=new sv((t=s).filter(sA)):(t=s.tools,r=s);let w=null,v=async e=>{let r;if(w)return w;if(await sE(e,t)){if(!("bindTools"in e)||"function"!=typeof e.bindTools)throw Error(`llm ${e} must define bindTools method.`);r=e.bindTools(t)}else r=e;let i=function(e,t,r){let i;if([e,t,r].filter(e=>null!=e).length>1)throw Error("Expected only one of prompt, stateModifier, or messageModifier, got multiple values");let a=e;null!=t?a=t:null!=r&&(a=function(e){if("string"==typeof e||(0,eq.isBaseMessage)(e)&&"system"===e._getType())return e;if("function"==typeof e)return async t=>e(t.messages);if(e8.YN.isRunnable(e))return e8.jY.from(e=>e.messages).pipe(e);throw Error(`Unexpected type for messageModifier: ${typeof e}`)}(r));var s=a;if(null==s)i=e8.jY.from(e=>e.messages).withConfig({runName:sC});else if("string"==typeof s){let e=new eq.SystemMessage(s);i=e8.jY.from(t=>[e,...t.messages??[]]).withConfig({runName:sC})}else if((0,eq.isBaseMessage)(s)&&"system"===s._getType())i=e8.jY.from(e=>[s,...e.messages]).withConfig({runName:sC});else if("function"==typeof s)i=e8.jY.from(s).withConfig({runName:sC});else if(e8.YN.isRunnable(s))i=s;else throw Error(`Got unexpected type for 'prompt': ${typeof s}`);return i}(l,o,n),a="inline"===_?i.pipe(function(e,t){let r,i;if("inline"===t)r=sx,i=sS;else throw Error(`Invalid agent name mode: ${t}. Needs to be one of: "inline"`);return e8.zZ.from([e8.jY.from(function(e){return e.map(r)}),e,e8.jY.from(i)])}(r,_)):i.pipe(r);return w=a,a},k=new Set(t.filter(sA).filter(e=>"returnDirect"in e&&e.returnDirect).map(e=>e.name)),O=async(e,t)=>{let r;if(null==f)throw Error("Attempted to generate structured output with no passed response schema. Please contact us for help.");let i=[...e.messages];if("object"==typeof f&&"prompt"in f&&"schema"in f){let{prompt:e,schema:t}=f;r=(await sj(a)).withStructuredOutput(t),i.unshift(new eq.SystemMessage({content:e}))}else r=(await sj(a)).withStructuredOutput(f);return{structuredResponse:await r.invoke(i,t)}},x=async(e,t)=>{let r=await v(a),i=await r.invoke(function(e){let{messages:t,llmInputMessages:r,...i}=e;return null!=r&&r.length>0?{messages:r,...i}:{messages:t,...i}}(e),t);return i.name=y,i.lc_kwargs.name=y,{messages:[i]}},S=c??sI(),C=new sm(S).addNode("tools",r),A=e=>Object.fromEntries(Object.entries(e).filter(([e,t])=>null!=t)),T="agent";return null!=g?(C.addNode("pre_model_hook",g).addEdge("pre_model_hook","agent"),T="pre_model_hook",i=i6.Root({...S.spec,...sN.spec})):T="agent",C.addNode("agent",x,{input:i}).addEdge(iw,T),null!=b&&C.addNode("post_model_hook",b).addEdge("agent","post_model_hook").addConditionalEdges("post_model_hook",e=>{let{messages:t}=e,r=t[t.length-1];return(0,eq.isAIMessage)(r)&&r.tool_calls?.length?"tools":(0,eq.isToolMessage)(r)?T:null!=f?"generate_structured_response":iv},A({tools:"tools",[T]:T,generate_structured_response:null!=f?"generate_structured_response":null,[iv]:null!=f?null:iv})),void 0!==f&&C.addNode("generate_structured_response",O).addEdge("generate_structured_response",iv),null==b&&C.addConditionalEdges("agent",e=>{let{messages:t}=e,r=t[t.length-1];return(0,eq.isAIMessage)(r)&&r.tool_calls?.length?"tools":null!=f?"generate_structured_response":iv},A({tools:"tools",generate_structured_response:null!=f?"generate_structured_response":null,[iv]:null!=f?null:iv})),k.size>0?C.addConditionalEdges("tools",e=>{for(let t=e.messages.length-1;t>=0;t-=1){let r=e.messages[t];if(!(0,eq.isToolMessage)(r))break;if(void 0!==r.name&&k.has(r.name))return iv}return T},A({[T]:T,[iv]:iv})):C.addEdge("tools",T),C.compile({checkpointer:h??u,interruptBefore:d,interruptAfter:p,store:m,name:y})}({llm:r,tools:a,messageModifier:new eq.SystemMessage(i)});this.agents[e]={agent:s,llm:r,role:e,systemPrompt:i,tools:a.length}}}getSystemPromptForRole(e){let t=F.p2.find(t=>t.id===e),r=t?.description||`You are an expert in ${e}.`;return({brainstorming_ideation:`${r}

You are a creative brainstorming expert. Your role in this multi-agent collaboration is to:
- Generate innovative and diverse ideas
- Think outside the box and explore unconventional approaches
- Build upon ideas from other agents
- Ask thought-provoking questions to stimulate creativity
- Provide multiple perspectives and alternatives

Always be enthusiastic, creative, and collaborative. When other agents share their work, build upon it creatively.`,writing:`${r}

You are a professional writing expert. Your role in this multi-agent collaboration is to:
- Transform ideas into well-structured, engaging content
- Adapt writing style to the target audience and purpose
- Ensure clarity, coherence, and compelling narrative flow
- Collaborate with other agents to refine and improve content
- Provide constructive feedback on written materials

Focus on creating high-quality, polished content that achieves the intended goals.`,coding_backend:`${r}

You are a backend development expert. Your role in this multi-agent collaboration is to:
- Design robust, scalable backend architectures
- Write clean, efficient, and maintainable code
- Consider security, performance, and best practices
- Collaborate with frontend developers and other team members
- Provide technical guidance and code reviews

Always prioritize code quality, security, and scalability in your solutions.`,coding_frontend:`${r}

You are a frontend development expert. Your role in this multi-agent collaboration is to:
- Create intuitive, responsive user interfaces
- Implement modern frontend technologies and best practices
- Ensure excellent user experience and accessibility
- Collaborate with backend developers and designers
- Optimize for performance and cross-browser compatibility

Focus on creating beautiful, functional, and user-friendly interfaces.`,general_chat:`You are a helpful AI assistant participating in a multi-agent collaboration. Your role is to:
- Provide general assistance and coordination
- Help bridge communication between specialized agents
- Offer balanced perspectives and common-sense insights
- Facilitate smooth collaboration between team members
- Ensure all participants stay focused on the main objectives

Be supportive, clear, and help maintain productive collaboration.`})[e]||`${r}

You are participating in a multi-agent collaboration. Work together with other agents to achieve the best possible outcome. Be collaborative, constructive, and focused on the shared goals.`}getToolsForRole(e){return[]}getTemperatureForRole(e){return({brainstorming_ideation:.9,writing:.7,coding_backend:.3,coding_frontend:.3,translation_localization:.3,summarization_briefing:.5,general_chat:.7})[e]||.7}getMaxTokensForRole(e){return({brainstorming_ideation:3e3,writing:4e3,coding_backend:3e3,coding_frontend:3e3,translation_localization:2e3,summarization_briefing:2e3,general_chat:2e3})[e]||2e3}determineWorkflowType(){let e=this.config.roles.length;return this.config.workflowType?this.config.workflowType:2===e?"sequential":e<=3?"supervisor":"hierarchical"}async buildWorkflow(e){switch(e){case"sequential":this.workflow=this.buildSequentialWorkflow();break;case"parallel":this.workflow=this.buildParallelWorkflow();break;case"supervisor":this.workflow=this.buildSupervisorWorkflow();break;case"hierarchical":this.workflow=this.buildHierarchicalWorkflow();break;default:throw Error(`Unknown workflow type: ${e}`)}}buildSequentialWorkflow(){let e=new sm({channels:{messages:{value:(e,t)=>e.concat(t),default:()=>[]},next:{value:(e,t)=>t??e,default:()=>void 0},currentRole:{value:(e,t)=>t??e,default:()=>void 0},taskProgress:{value:(e,t)=>({...e||{},...t||{}}),default:()=>({})},results:{value:(e,t)=>({...e||{},...t||{}}),default:()=>({})},metadata:{value:(e,t)=>({...e||{},...t||{}}),default:()=>({})},conversationId:{value:(e,t)=>t??e,default:()=>this.config.conversationId},userId:{value:(e,t)=>t??e,default:()=>this.config.userId}}});this.config.roles.forEach((t,r)=>{e.addNode(t,this.createAgentNode(t))}),e.addEdge(iw,this.config.roles[0]);for(let t=0;t<this.config.roles.length-1;t++)e.addEdge(this.config.roles[t],this.config.roles[t+1]);return e.addEdge(this.config.roles[this.config.roles.length-1],iv),e.compile()}buildParallelWorkflow(){let e=new sm({channels:{messages:{value:(e,t)=>e.concat(t),default:()=>[]},next:{value:(e,t)=>t??e,default:()=>void 0},currentRole:{value:(e,t)=>t??e,default:()=>void 0},taskProgress:{value:(e,t)=>({...e||{},...t||{}}),default:()=>({})},results:{value:(e,t)=>({...e||{},...t||{}}),default:()=>({})},metadata:{value:(e,t)=>({...e||{},...t||{}}),default:()=>({})},conversationId:{value:(e,t)=>t??e,default:()=>this.config.conversationId},userId:{value:(e,t)=>t??e,default:()=>this.config.userId}}});return this.config.roles.forEach(t=>{e.addNode(t,this.createAgentNode(t))}),e.addNode("merger",this.createMergerNode()),this.config.roles.forEach(t=>{e.addEdge(iw,t),e.addEdge(t,"merger")}),e.addEdge("merger",iv),e.compile()}buildSupervisorWorkflow(){let e=new sm({channels:{messages:{value:(e,t)=>e.concat(t),default:()=>[]},next:{value:(e,t)=>t??e,default:()=>void 0},currentRole:{value:(e,t)=>t??e,default:()=>void 0},taskProgress:{value:(e,t)=>({...e||{},...t||{}}),default:()=>({})},results:{value:(e,t)=>({...e||{},...t||{}}),default:()=>({})},metadata:{value:(e,t)=>({...e||{},...t||{}}),default:()=>({})},conversationId:{value:(e,t)=>t??e,default:()=>this.config.conversationId},userId:{value:(e,t)=>t??e,default:()=>this.config.userId}}}),t=this.determineSupervisorRole();e.addNode("supervisor",this.createSupervisorNode(t));let r="supervisor"===t?this.config.roles:this.config.roles.filter(e=>e!==t);return r.forEach(t=>{e.addNode(t,this.createAgentNode(t)),e.addEdge(t,"supervisor")}),e.addConditionalEdges("supervisor",this.createSupervisorRouter(r)),e.addEdge(iw,"supervisor"),e.compile()}buildHierarchicalWorkflow(){return this.buildSupervisorWorkflow()}async execute(){let e=Date.now();try{await this.initializeAgents();let t=this.determineWorkflowType();await this.buildWorkflow(t);let r=await this.executeWorkflow(),i=Date.now()-e;return{success:!0,finalResponse:r.finalResponse,roleContributions:r.roleContributions,metadata:{totalTokens:r.totalTokens,executionTime:i,workflowType:t,rolesUsed:this.config.roles},conversationHistory:r.conversationHistory}}catch(t){return{success:!1,finalResponse:`Orchestration failed: ${t instanceof Error?t.message:"Unknown error"}`,roleContributions:{},metadata:{totalTokens:0,executionTime:Date.now()-e,workflowType:"failed",rolesUsed:this.config.roles},conversationHistory:[]}}}createAgentNode(e){return async(t,r)=>{let i=this.getTaskDescriptionForRole(e,t);this.progressCallback?.onAgentWorkStart?.(e,i);let a=this.agents[e];if(!a)throw Error(`Agent not found for role: ${e}`);try{let i=[...t.messages];if(i.length>0){let r=i[i.length-1];if(r instanceof eq.HumanMessage){let a=Object.entries(t.results||{}).filter(([t])=>t!==e&&"supervisor"!==t&&"final"!==t).map(([e,t])=>{let r="string"==typeof t&&t.length>500?t.substring(0,500)+"...[truncated]":t;return`${e}: ${r}`}),s=`${r.content}

[Multi-Agent Context]
You are working as part of a team with the following roles: ${this.config.roles.join(", ")}
Your specific role is: ${e}
Previous work from other agents: ${a.length>0?a.join("\n\n"):"None yet"}

Please contribute your expertise while building upon any previous work.`;i[i.length-1]=new eq.HumanMessage(s)}}let s=await a.agent.invoke({messages:i},r),n=s.messages[s.messages.length-1].content;if(!n||0===n.length)throw Error(`Agent ${e} returned empty response. This indicates a critical issue with the LLM provider response.`);return n.length,this.progressCallback?.onAgentWorkComplete?.(e,n),{messages:[new eq.AIMessage({content:n,name:e})],currentRole:e,results:{...t.results,[e]:n},taskProgress:{...t.taskProgress,[e]:"completed"}}}catch(i){try{let i=[new eq.HumanMessage(this.config.originalPrompt)],s=await a.agent.invoke({messages:i},r),n=s.messages[s.messages.length-1].content;if(n&&n.length>0)return{messages:[new eq.AIMessage({content:n,name:e})],currentRole:e,results:{...t.results,[e]:n},taskProgress:{...t.taskProgress,[e]:"completed_fallback"}}}catch(e){}try{let i=this.agents[this.config.roles[0]],a=this.createRoleSpecificFallbackPrompt(e,this.config.originalPrompt),s=await i.agent.invoke({messages:[new eq.HumanMessage(a)]},r),n=s.messages[s.messages.length-1].content;if(n&&n.length>0)return{messages:[new eq.AIMessage({content:n,name:e})],currentRole:e,results:{...t.results,[e]:n},taskProgress:{...t.taskProgress,[e]:"completed_supervisor_fallback"}}}catch(e){}return{messages:[new eq.AIMessage({content:`Error in ${e}: ${i instanceof Error?i.message:"Unknown error"}`,name:e})],currentRole:e,results:{...t.results,[e]:`Error: ${i instanceof Error?i.message:"Unknown error"}`},taskProgress:{...t.taskProgress,[e]:"failed"}}}}}getTaskDescriptionForRole(e,t){let r=Object.keys(t.results||{}).length,i={brainstorming_ideation:"Generating creative ideas and innovative approaches",writing:"Crafting well-structured and engaging content",coding_backend:"Developing robust backend architecture and code",coding_frontend:"Creating intuitive user interfaces and experiences",translation_localization:"Translating and localizing content",summarization_briefing:"Summarizing and briefing key information",general_chat:"Providing general assistance and coordination"}[e]||`Working on ${e} tasks`;return r>0?`${i} (building on ${r} previous contributions)`:i}createRoleSpecificFallbackPrompt(e,t){return({brainstorming_ideation:`As a creative brainstorming expert, generate innovative ideas for this request: "${t}"

Focus on:
- Creative concepts and possibilities
- Innovative approaches and solutions
- Multiple perspectives and angles
- Detailed idea development

Provide comprehensive brainstorming output.`,coding_backend:`As a backend development expert, provide code and technical solutions for this request: "${t}"

Focus on:
- Clean, functional code
- Best practices and architecture
- Security and performance considerations
- Complete implementation details

Provide working code with explanations.`,coding_frontend:`As a frontend development expert, create user interface solutions for this request: "${t}"

Focus on:
- User-friendly interfaces
- Modern web technologies
- Responsive design principles
- Complete implementation

Provide working frontend code with explanations.`,writing:`As a professional writer, create well-structured content for this request: "${t}"

Focus on:
- Clear, engaging writing
- Proper structure and flow
- Target audience considerations
- Polished, professional output

Provide comprehensive written content.`,translation_localization:`As a translation expert, handle the language aspects of this request: "${t}"

Focus on:
- Accurate translations
- Cultural considerations
- Localization best practices
- Clear communication

Provide complete translation/localization output.`,summarization_briefing:`As a summarization expert, create concise summaries for this request: "${t}"

Focus on:
- Key points and highlights
- Clear, structured summaries
- Essential information extraction
- Professional briefing format

Provide comprehensive summaries.`,general_chat:`As a helpful assistant, provide comprehensive assistance for this request: "${t}"

Focus on:
- Clear, helpful responses
- Comprehensive coverage
- Professional communication
- Practical solutions

Provide detailed assistance.`})[e]||`As an expert in ${e}, provide comprehensive assistance for this request: "${t}"

Focus on your specialized expertise and provide detailed, helpful output.`}createMergerNode(){return async(e,t)=>{let r=e.results||{},i=Object.entries(r).map(([e,t])=>`**${e.toUpperCase()}:**
${t}`).join("\n\n---\n\n"),a=`# Multi-Agent Collaboration Results

${i}

---

**Summary:** This response was collaboratively created by ${this.config.roles.length} specialized agents, each contributing their expertise to provide you with a comprehensive and well-rounded answer.`;return{messages:[new eq.AIMessage({content:a,name:"merger"})],currentRole:"merger",results:{...r,final:a}}}}determineSupervisorRole(){return this.config.roles.includes("general_chat")?"general_chat":"supervisor"}createSupervisorNode(e){return async(t,r)=>{let i,a;if("supervisor"===e){let e=this.config.roles[0];if(!(i=this.agents[e]))throw Error(`No agent found for supervisor coordination (tried role: ${e})`)}else if(!(i=this.agents[e]))throw Error(`Supervisor agent not found for role: ${e}`);let s="supervisor"===e?this.config.roles:this.config.roles.filter(t=>t!==e),n=Object.keys(t.results||{}).filter(t=>t!==e&&"final"!==t),o=s.filter(e=>!n.includes(e));0===o.length?(this.progressCallback?.onSupervisorSynthesisStart?.(),a=`All team members have completed their work. Please create a comprehensive final response that synthesizes all contributions:

${Object.entries(t.results||{}).filter(([t])=>t!==e).map(([e,t])=>`**${e}:** ${t}`).join("\n\n")}

Create a polished, cohesive final response that incorporates the best insights from each team member.`):a=`You are coordinating a multi-agent team. Here's the current status:

Original request: ${this.config.originalPrompt}

Available team members: ${s.join(", ")}
Completed work: ${n.join(", ")||"None yet"}
Remaining work: ${o.join(", ")}

${n.length>0?`Previous contributions:
${Object.entries(t.results||{}).filter(([t])=>t!==e).map(([e,t])=>`**${e}:** ${t}`).join("\n\n")}`:""}

Decide which team member should work next. Respond with just the role name: ${o.join(" or ")}.`;try{let s=await i.agent.invoke({messages:[...t.messages,new eq.HumanMessage(a)]},r),n=s.messages[s.messages.length-1].content;return 0===o.length&&this.progressCallback?.onSupervisorSynthesisComplete?.(n),{messages:[new eq.AIMessage({content:n,name:e})],currentRole:e,next:0===o.length?iv:this.parseNextRole(n,o),results:{...t.results,[e]:n,...0===o.length?{final:n}:{}}}}catch(t){return{messages:[new eq.AIMessage({content:`Supervisor error: ${t instanceof Error?t.message:"Unknown error"}`,name:e})],currentRole:e,next:iv}}}}createSupervisorRouter(e){return t=>{let r=t.next;return r!==iv&&r&&e.includes(r)?r:iv}}parseNextRole(e,t){let r=e.toLowerCase();for(let e of t)if(r.includes(e.toLowerCase()))return e;return t[0]||iv}async executeWorkflow(){if(!this.workflow)throw Error("Workflow not built");let e={messages:[new eq.HumanMessage(this.config.originalPrompt)],conversationId:this.config.conversationId,userId:this.config.userId,results:{},taskProgress:{},metadata:{}},t=[];try{let r,i=await this.workflow.stream(e,{recursionLimit:this.config.maxIterations||10}),a=e;for await(let e of i)if(e&&"object"==typeof e){let r=Object.keys(e)[0],i=e[r];i&&"object"==typeof i&&(a={...a,...i,results:{...a.results,...i.results},taskProgress:{...a.taskProgress,...i.taskProgress}},i.messages&&t.push(...i.messages))}let s=a.results||{};if(s.final)r=s.final;else if(s.merger)r=s.merger;else{let e=Object.entries(s).map(([e,t])=>`**${e.toUpperCase()}:**
${t}`).join("\n\n---\n\n");r=`# Multi-Agent Collaboration Results

${e}`}return{finalResponse:r,roleContributions:s,totalTokens:0,conversationHistory:t}}catch(e){throw e}}}class sV{constructor(e,t,r){this.config=e,this.streamingCallback=t,this.progressCallback=r}async execute(){try{this.progressCallback?.onClassificationStart?.(),await this.simulateDelay(200);let e=this.config.roles;this.progressCallback?.onClassificationComplete?.(e,.14),await this.simulateDelay(150),this.progressCallback?.onRoleSelectionComplete?.(e,[]),this.validateConfiguration();let t=this.determineWorkflowType(),r=this.getWorkflowRecommendations().reasoning;this.progressCallback?.onWorkflowSelectionComplete?.(t,r),await this.simulateDelay(100),this.progressCallback?.onAgentCreationStart?.(),await this.simulateDelay(300);let i=this.config.roles.map(e=>({role:e,apiKey:`${this.config.userApiKeys[e]?.id?.substring(0,8)}...`||"unknown"}));this.progressCallback?.onAgentCreationComplete?.(i),this.config.roles.length>1&&(this.progressCallback?.onSupervisorInitStart?.(),await this.simulateDelay(200),this.progressCallback?.onSupervisorInitComplete?.("supervisor"),this.progressCallback?.onTaskPlanningStart?.(),await this.simulateDelay(250),this.progressCallback?.onTaskPlanningComplete?.("Task distribution planned based on role dependencies"));let a={roles:this.config.roles,userApiKeys:this.config.userApiKeys,originalPrompt:this.config.originalPrompt,conversationId:this.config.conversationId,userId:this.config.userId,customApiConfigId:this.config.customApiConfigId,workflowType:t,maxIterations:this.config.preferences?.maxIterations||10,enableMemory:this.config.preferences?.enableMemory||!1},s=new sz(a,this.progressCallback);if(this.config.preferences?.enableStreaming&&this.streamingCallback)return await this.executeWithStreaming(s);return await s.execute()}catch(e){return this.progressCallback?.onError?.("orchestration",e instanceof Error?e.message:"Unknown error"),{success:!1,finalResponse:`Multi-role orchestration failed: ${e instanceof Error?e.message:"Unknown error"}`,roleContributions:{},metadata:{totalTokens:0,executionTime:0,workflowType:"failed",rolesUsed:this.config.roles},conversationHistory:[]}}}async simulateDelay(e){return new Promise(t=>setTimeout(t,e))}validateConfiguration(){if(!this.config.roles||0===this.config.roles.length)throw Error("No roles specified for orchestration");if(!this.config.userApiKeys||0===Object.keys(this.config.userApiKeys).length)throw Error("No API keys provided for orchestration");if(!this.config.originalPrompt||0===this.config.originalPrompt.trim().length)throw Error("No prompt provided for orchestration");if(this.config.roles.filter(e=>!this.config.userApiKeys[e]).length>0&&(this.config.roles=this.config.roles.filter(e=>this.config.userApiKeys[e]),0===this.config.roles.length))throw Error("No valid API keys found for any of the specified roles")}determineWorkflowType(){if(this.config.preferences?.workflowType&&"auto"!==this.config.preferences.workflowType)return this.config.preferences.workflowType;let e=this.config.roles.length;return 1===e?"sequential":e<=4?"supervisor":"hierarchical"}async executeWithStreaming(e){this.streamingCallback?.onWorkflowProgress&&this.streamingCallback.onWorkflowProgress({completed:[],remaining:this.config.roles});let t=await e.execute();return this.streamingCallback?.onFinalResult&&this.streamingCallback.onFinalResult(t),t}getWorkflowRecommendations(){let e,t,r=this.config.roles.length,i=[];return 1===r?(e="sequential",i=[],t="Single role uses sequential execution for consistency."):r<=4?(e="supervisor",i=["hierarchical"],t=`With ${r} roles, supervisor pattern ensures proper coordination where one agent manages the workflow and others build on each other's work.`):(e="hierarchical",i=["supervisor"],t=`For ${r} roles, hierarchical coordination provides multi-level management to prevent chaos and ensure structured collaboration.`),{recommended:e,alternatives:i,reasoning:t}}static async executeMultiRole(e,t,r,i){let a=new sV({roles:e,userApiKeys:t,originalPrompt:r,conversationId:i?.conversationId,userId:i?.userId,customApiConfigId:i?.customApiConfigId,preferences:{workflowType:i?.workflowType||"auto",enableStreaming:i?.enableStreaming||!1}},void 0,i?.progressCallback);return await a.execute()}}var sK=r(87846),sB=r(62706),sW=r(2842),sJ=r(55591),sG=r.n(sJ),sY=r(81630),sH=r.n(sY),sX=r(79551);let sZ=new Map,sQ=new Map,s0=new Map;function s1(e,t){sQ.has(e)||sQ.set(e,{});let r=sQ.get(e);r[t]||(r[t]={count:0,lastUsed:0}),r[t].count++,r[t].lastUsed=Date.now()}let s2=new Map;async function s5(e,t,r,i,a=[]){let s=nu(e);if(s2.has(s))try{return await s2.get(s)}catch(e){s2.delete(s)}let n=s3(e,t,r,i,a);s2.set(s,n);try{return await n}catch(e){throw e}finally{s2.delete(s)}}async function s3(e,t,r,i,a=[]){let s=a?a.slice(-3).map(e=>`${e.role}: ${"string"==typeof e.content?e.content.substring(0,200):Array.isArray(e.content)?e.content.find(e=>"text"===e.type)?.text?.substring(0,200)||"[non-text]":"[unknown]"}`).join("\n"):"",n=await s4(e,t,r,s);if(n.isMultiRole)return n;let o=t.map(e=>`- Role ID: "${e.id}", Name: "${e.name}", Description: "${(e.description||"N/A").substring(0,150)}"`).join("\n"),l=`Available Roles:
${o}

Recent Conversation:
${s}

Current Request: "${e.substring(0,2e3)}"

Most Appropriate Role ID:`,c=await n$("https://generativelanguage.googleapis.com/v1beta/openai/chat/completions",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${r}`,Connection:"keep-alive","User-Agent":"RoKey/1.0 (Classification-Optimized)",Origin:"https://rokey.app"},body:JSON.stringify({model:"gemini-2.0-flash-lite",messages:[{role:"system",content:"You are an expert task classification system. Analyze the user's request considering both the current message AND recent conversation context. Key rules: 1) If user is responding to options/choices in an ongoing task (like '1, 2, 3' after coding options), continue with same role. 2) Only switch roles for clear new tasks ('write story', 'solve math', etc.). 3) Examples: 'write code'=coding roles, 'write story'=writing roles, 'solve math'=logic_reasoning. Respond with ONLY the Role ID string."},{role:"user",content:l}],temperature:.1,max_tokens:50})},2,nR.CLASSIFICATION);if(!c.ok)throw Error(`Gemini API error: ${c.status}`);let u=await c.json(),h=u.choices?.[0]?.message?.content?.trim().replace(/["'`]/g,"")||null;if(h){h=h.replace(/^(Role ID:\s*|Role:\s*|Classification:\s*)/i,"").trim();let e=t.find(e=>e.id===h);if(e||(e=t.find(e=>e.id.toLowerCase()===h.toLowerCase())),e||(e=t.find(e=>e.name&&e.name.toLowerCase()===h.toLowerCase())),!e)return{roleId:h,confidence:.5};h=e.id}else throw Error("Empty classification result");return{roleId:h,confidence:.95}}async function s4(e,t,r,i=""){let a=t.map(e=>`- Role ID: "${e.id}", Name: "${e.name}", Description: "${(e.description||"N/A").substring(0,150)}"`).join("\n"),s=`You are an expert task analyzer that determines whether a user request requires multiple specialized roles or can be handled by a single role.

IMPORTANT PRINCIPLES:
1. Analyze requests carefully to identify distinct tasks that require different specialized skills
2. Simple requests like "continue", "more", "help me", or single actions should be single-role
3. Multi-role is for requests that contain multiple distinct tasks with different skill requirements
4. Use ONLY role IDs from the available roles list - never make up role names

AVAILABLE ROLE IDS (use these exact IDs):
- general_chat: General conversation and Q&A
- logic_reasoning: Mathematical reasoning and problem-solving
- writing: Articles, blog posts, marketing copy, creative content
- coding_frontend: HTML, CSS, JavaScript, React, Vue, Angular, UI/UX
- coding_backend: Server-side logic, APIs, databases, Python, Node.js, Java
- research_synthesis: Information retrieval, data analysis, research reports
- summarization_briefing: Condensing texts into summaries
- translation_localization: Language translation and cultural adaptation
- data_extraction_structuring: Extracting and organizing information from text
- brainstorming_ideation: Creative idea generation and concept development
- education_tutoring: Explaining concepts and educational assistance
- image_generation: Creating images from descriptions
- audio_transcription: Converting speech to text


Examples of TRUE multi-role requests (multiple distinct tasks):
- "Brainstorm a game idea and then write the backend code for it" (brainstorming_ideation → coding_backend)
- "Brainstorm an idea for a simple snake game and give me the full script" (brainstorming_ideation → coding_backend)
- "Research the history of AI and write a blog post about it" (research_synthesis → writing)
- "Create a React component and write documentation for it" (coding_frontend → writing)
- "Build a REST API and create a frontend interface for it" (coding_backend → coding_frontend)


Examples of single-role requests (even if complex):
- "Write a Python API for user authentication" (just coding_backend)
- "Create a responsive navbar in React" (just coding_frontend)
- "Build a full-stack web app" (just coding_backend - this is one cohesive task)
- "Create a blog post about climate change" (just writing)

- "continue" (just general_chat)
- "more" (just general_chat)
- "explain this code" (just general_chat)
- "help me debug this JavaScript error" (just coding_frontend)

CODING ROLE GUIDELINES:
- coding_frontend: HTML, CSS, JavaScript, React, Vue, Angular, UI components, styling, client-side logic
- coding_backend: Python, Node.js, Java, APIs, databases, server logic, data processing, algorithms

Analyze the request thoughtfully - choose multi-role when the request contains multiple distinct tasks that require different specialized skills.

Respond in JSON format ONLY with no additional text:
{
  "isMultiRole": true/false,
  "roles": [
    {"roleId": "role_id1", "confidence": 0.9, "executionOrder": 1},
    {"roleId": "role_id2", "confidence": 0.8, "executionOrder": 2}
  ],
  "reasoning": "Detailed explanation of your analysis"
}`,n=`Available Roles:
${a}

Recent Conversation:
${i}

User Request: "${e.substring(0,2e3)}"

Analyze this request: Does it require multiple distinct specialized roles working together, or can it be handled by a single role?`;try{let e,i=await n$("https://generativelanguage.googleapis.com/v1beta/openai/chat/completions",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${r}`,Connection:"keep-alive","User-Agent":"RoKey/1.0 (Multi-Role-Detection)",Origin:"https://rokey.app"},body:JSON.stringify({model:"gemini-2.0-flash-lite",messages:[{role:"system",content:s},{role:"user",content:n}],temperature:.3,max_tokens:800,response_format:{type:"json_object"}})},2,nR.CLASSIFICATION);if(!i.ok)return{isMultiRole:!1,roles:[],reasoning:"API error during multi-role detection"};let a=await i.json();try{let r=a.choices?.[0]?.message?.content;if(!r)throw Error("Empty response from multi-role detection");if(e=JSON.parse(r),"boolean"!=typeof e.isMultiRole||!Array.isArray(e.roles))throw Error("Invalid response structure from multi-role detection");return e.isMultiRole&&e.roles.length>0&&(e.roles=e.roles.map(e=>{let r=t.find(t=>t.id===e.roleId||t.id.toLowerCase()===e.roleId.toLowerCase()||t.name&&t.name.toLowerCase()===e.roleId.toLowerCase());return r?{...e,roleId:r.id,confidence:"number"==typeof e.confidence?e.confidence:.8}:null}).filter(Boolean),0===e.roles.length&&(e.isMultiRole=!1,e.reasoning="No valid roles matched after filtering")),e}catch(e){return{isMultiRole:!1,roles:[],reasoning:"Error parsing multi-role detection result"}}}catch(e){return{isMultiRole:!1,roles:[],reasoning:"Error during multi-role detection"}}}let s8=new Map;async function s6(e,t,r,i){let a=await (0,$.createSupabaseServerClientOnRequest)(),{error:s}=await a.from("synthesis_storage").upsert({synthesis_id:e,conversation_id:t,complete_synthesis:r,chunks:i,total_chunks:i.length,created_at:new Date().toISOString(),last_access_time:new Date().toISOString()});if(s)throw Error(`Failed to store synthesis: ${s.message}`)}async function s9(e){let t=await (0,$.createSupabaseServerClientOnRequest)(),{data:r,error:i}=await t.from("synthesis_storage").select("*").eq("synthesis_id",e).single();return i||!r?null:(await t.from("synthesis_storage").update({last_access_time:new Date().toISOString()}).eq("synthesis_id",e),{conversationId:r.conversation_id,completeSynthesis:r.complete_synthesis,chunks:r.chunks,totalChunks:r.total_chunks,createdAt:new Date(r.created_at).getTime(),lastAccessTime:new Date(r.last_access_time).getTime()})}async function s7(e){let t=await (0,$.createSupabaseServerClientOnRequest)(),{data:r,error:i}=await t.from("synthesis_storage").select("synthesis_id").eq("conversation_id",e).order("created_at",{ascending:!1}).limit(1).single();return i||!r?null:r.synthesis_id}let ne=0,nt={MAX_CHARS:7e4,MIN_CHUNK_SIZE:1e3,CODE_BLOCK_MAX_CHARS:7e4};function nr(e){let t=function(e){let t=e.find(e=>"user"===e.role);if(t&&"string"==typeof t.content){let e=sM().createHash("md5").update(t.content.substring(0,200)).digest("hex").substring(0,12);return`conv_${e}`}return`conv_${Date.now()}`}(e);for(let[e,r]of s8.entries())if(e.startsWith(t.substring(0,15))&&Date.now()-r.lastActivity<18e5)return e;return t}async function ni(){let e=await (0,$.createSupabaseServerClientOnRequest)(),t=new Date(Date.now()-18e5).toISOString(),{data:r,error:i}=await e.from("synthesis_storage").select("synthesis_id").lt("last_access_time",t);if(i||!r||0===r.length)return;let{error:a}=await e.from("synthesis_storage").delete().lt("last_access_time",t);a||ne++}async function na(e,t){let r=function(e){let t=[],r="",i=0,a=!1,s=e.split("\n\n");for(let e=0;e<s.length;e++){let n=s[e],o=n.length,l=n.match(/^```(\w+)?/),c=!!n.match(/```\s*$/);l&&!a?(a=!0,l[1]):c&&a&&(a=!1);let u=i+o+2>nt.MAX_CHARS,h=i+o+2>nt.CODE_BLOCK_MAX_CHARS;if(a&&u&&!h&&r.length>nt.MIN_CHUNK_SIZE)r+=(r?"\n\n":"")+n,i+=o+2*!!r;else if(a&&h)t.push(r.trim()),r=n,i=o;else if(u&&r.length>nt.MIN_CHUNK_SIZE&&!a)t.push(r.trim()),r=n,i=o;else if(u&&r.length<=nt.MIN_CHUNK_SIZE&&!a)for(let e of n.split(/(?<=[.!?])\s+/))i+e.length+1>nt.MAX_CHARS&&r.length>nt.MIN_CHUNK_SIZE?(t.push(r.trim()),r=e,i=e.length):(r+=(r?" ":"")+e,i+=e.length+ +!!r);else r+=(r?"\n\n":"")+n,i+=o+2*!!r}r.trim()&&t.push(r.trim());let n=t.filter(e=>e.trim().length>0),o=n.map((e,t)=>{let r=e,i=r.trim().startsWith("```"),a=r.includes("```");if(!i&&a&&t>0){let e=n[t-1];if((e.match(/```/g)||[]).length%2==1){let t=e.match(/```(\w+)?/);r="```"+(t&&t[1]||"")+"\n"+r}}return r});return o.forEach((e,t)=>{(e.match(/```/g)||[]).length}),o}(t),i=`synthesis_${e}_${Date.now()}`;try{return await s6(i,e,t,r),ne++,await s9(i),i}catch(e){throw e}}async function ns(e,t){let r=await s9(e);if(!r)return{chunk:null,isComplete:!0,progress:"not_found",totalChunks:0,synthesisId:e};if(t>=r.totalChunks)return{chunk:null,isComplete:!0,progress:"complete",totalChunks:r.totalChunks,synthesisId:e};let i=r.chunks[t],a=t+1>=r.totalChunks,s=`${t+1}/${r.totalChunks}`;return a&&setTimeout(async()=>{let t=await (0,$.createSupabaseServerClientOnRequest)();await t.from("synthesis_storage").delete().eq("synthesis_id",e),ne++},3e4),{chunk:i,isComplete:a,progress:s,totalChunks:r.totalChunks,synthesisId:e}}async function nn(e){try{let t=await s7(e);if(t)return t;return null}catch(e){return null}}async function no(e){try{let t=await s7(e);if(t)return{synthesisId:t,isComplete:!1};return null}catch(e){return null}}let nl=new Map,nc=new Map;function nu(e){return sM().createHash("md5").update(e.toLowerCase().trim()).digest("hex")}function nh(e,t,r){let i=e.map(e=>`${e.role}:${"string"==typeof e.content?e.content:JSON.stringify(e.content)}`).join("|"),a=`${t}|${i}|${r||0}`;return sM().createHash("md5").update(a).digest("hex")}function nd(e,t,r,i,a,s){if(r&&i&&a&&t.custom_api_config_id){let e=`${t.custom_api_config_id}:${t.messages?.[t.messages.length-1]?.content?.substring(0,100)||"default"}`;setImmediate(()=>{nc.set(e,{provider:r,model:i,apiKey:a,timestamp:Date.now()})})}if(r&&i&&t.custom_api_config_id&&e.responseData&&!t.stream){let a=t.messages?.[t.messages.length-1];a?.role==="user"&&"string"==typeof a.content&&setImmediate(async()=>{try{let n=await (0,$.createSupabaseServerClientOnRequest)(),{data:o}=await n.from("custom_api_configs").select("user_id").eq("id",t.custom_api_config_id).single();if(o?.user_id){let n=await (0,sB.cr)(o.user_id),l=e.responseData?.usage?.prompt_tokens||e.responseData?.usage?.input_tokens||0,c=e.responseData?.usage?.completion_tokens||e.responseData?.usage?.output_tokens||0;await sK.R.storeCache({promptText:a.content,modelUsed:i,providerUsed:r,temperature:t.temperature,maxTokens:t.max_tokens,metadata:{stream:t.stream,roleUsed:s?.roleUsed,routingStrategy:s?.routingStrategy}},{responseData:e.responseData,tokensPrompt:l,tokensCompletion:c,cost:1e-6*l+2e-6*c},o.user_id,t.custom_api_config_id,n)}}catch(e){}})}else t.stream&&r&&i&&t.custom_api_config_id;let n={};if(s?.roleUsed&&(n["X-RoKey-Role-Used"]=s.roleUsed),s?.routingStrategy&&(n["X-RoKey-Routing-Strategy"]=s.routingStrategy),s?.complexityLevel&&(n["X-RoKey-Complexity-Level"]=s.complexityLevel.toString()),r&&(n["X-RoKey-API-Key-Provider"]=r),s?.processingTime&&(n["X-RoKey-Processing-Time"]=`${s.processingTime}ms`),t.stream&&e.response){let t={...Object.fromEntries(e.response.headers.entries()),...n};return new Response(e.response.body,{status:e.response.status,headers:t})}if(t.stream||void 0===e.responseData)throw Error("Invalid provider result: no response data available");{let t={...e.responseHeaders||{},...n},r=s?.roleUsed?np(s.roleUsed):[],i={...e.responseData,rokey_metadata:{roles_used:r,routing_strategy:s?.routingStrategy||"unknown",...e.responseData.rokey_metadata||{}}};return D.NextResponse.json(i,{status:e.status||200,headers:t})}}function np(e){return e?e.includes("RouKey_Multi Roles_")?e.replace("RouKey_Multi Roles_","").replace("_routing","").split("_").filter(e=>e&&"routing"!==e):e.startsWith("intelligent_role_")?[e.replace("intelligent_role_","")]:!e||e.includes("_")||e.includes("default")||e.includes("failed")?[]:[e]:[]}async function nm(e,t,r){let i=`${e}_${t}`,a=s0.get(i);if(a&&Date.now()-a.timestamp<9e5)return{customRoles:a.customRoles,roleAssignments:a.roleAssignments,apiKeys:a.apiKeys};try{let[a,s,n]=await Promise.allSettled([r.from("user_custom_roles").select("role_id, name, description").eq("user_id",e),r.from("api_key_role_assignments").select("role_name, api_key_id").eq("custom_api_config_id",t),r.from("api_keys").select("*").eq("custom_api_config_id",t).eq("status","active")]),o="fulfilled"===a.status&&a.value.data||[],l="fulfilled"===s.status&&s.value.data||[],c="fulfilled"===n.status&&n.value.data||[];return s0.set(i,{customRoles:o,roleAssignments:l,apiKeys:c,timestamp:Date.now()}),{customRoles:o,roleAssignments:l,apiKeys:c}}catch(e){return null}}async function nf(e){try{let t=sW.trainingDataCache.get(e);if(t)return{trainingData:t.data,trainingJobId:t.jobId};let r=await (0,$.createSupabaseServerClientOnRequest)(),{data:i,error:a}=await r.from("training_jobs").select("id, training_data, created_at").eq("custom_api_config_id",e).eq("status","completed").order("created_at",{ascending:!1}).limit(1).single();if(a&&"PGRST116"!==a.code||!i?.training_data)return null;return sW.trainingDataCache.set(e,i.training_data,i.id),{trainingData:i.training_data,trainingJobId:i.id}}catch(e){return null}}async function ng(e){try{if(e.trim().length<8)return!1;let t=process.env.ROKEY_CLASSIFICATION_GEMINI_API_KEY;if(!t)return e.length>20;let r=`Analyze this user message and determine if it would benefit from searching a knowledge base/documentation.

User message: "${e}"

Consider these factors:
- Is this a greeting, simple response, or conversational filler? (NO search needed)
- Is this asking for specific information, explanations, or help? (YES search needed)
- Is this a technical question about APIs, code, or documentation? (YES search needed)
- Is this asking about a person, company, or specific topic? (YES search needed)
- Is this just continuing a conversation with "continue", "ok", "thanks"? (NO search needed)

Respond with exactly one word: "YES" if knowledge base search would be helpful, "NO" if not.`,i=await n$("https://generativelanguage.googleapis.com/v1beta/openai/chat/completions",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${t}`},body:JSON.stringify({model:"gemini-2.0-flash-001",messages:[{role:"user",content:r}],temperature:.1,max_tokens:10})},1,3e3);if(!i.ok)return e.length>20;let a=await i.json(),s=a.choices?.[0]?.message?.content?.trim().toUpperCase();return"YES"===s}catch(t){return e.length>20}}async function nb(e,t,i,a){try{let{jinaEmbeddings:s}=await Promise.resolve().then(r.bind(r,88108)),{jinaReranker:n}=await r.e(9618).then(r.bind(r,29618)),o=await s.embedQuery(e),{data:l,error:c}=await a.rpc("search_document_chunks",{query_embedding:o,config_id:t,user_id_param:i,match_threshold:.5,match_count:15});if(c)return{context:"",sources:[]};if(!l||0===l.length){let{data:e,error:r}=await a.rpc("search_document_chunks",{query_embedding:o,config_id:t,user_id_param:i,match_threshold:.3,match_count:10});if(r||!e||0===e.length)return{context:"",sources:[]};l=e}let u=l;if(l.length>1)try{let t=l.map(e=>({content:e.content,document_id:e.document_id,similarity:e.similarity,metadata:e.metadata}));u=(await n.rerankDocuments(e,t,5)).map(e=>({document_id:e.document_id,content:e.content,similarity:e.original_similarity,rerank_score:e.rerank_score,final_score:e.final_score,metadata:e.metadata}))}catch(e){u=l.slice(0,5)}else u=l.slice(0,5);u.forEach((e,t)=>{e.rerank_score&&(e.rerank_score.toFixed(3),e.final_score.toFixed(3))});let h=[...new Set(u.map(e=>e.document_id))],{data:d}=await a.from("documents").select("id, filename").in("id",h),p=u.map((e,t)=>{let r=e.rerank_score?`Similarity: ${e.similarity.toFixed(3)}, Rerank: ${e.rerank_score.toFixed(3)}, Final: ${e.final_score.toFixed(3)}`:`Similarity: ${e.similarity.toFixed(3)}`;return`[Document ${t+1} - ${r}]
${e.content.trim()}`}),m=u.map(e=>{let t=d?.find(t=>t.id===e.document_id);return{filename:t?.filename||"Unknown Document",document_id:e.document_id,similarity:Math.round(100*e.similarity)/100,rerank_score:e.rerank_score?Math.round(100*e.rerank_score)/100:void 0,final_score:e.final_score?Math.round(100*e.final_score)/100:void 0}});return{context:p.join("\n\n"),sources:m}}catch(e){return{context:"",sources:[]}}}async function ny(e,t){if(!t||!t.processed_prompts)return e;let{processed_prompts:r}=t,i="";if(r.system_instructions?.trim()&&(i+=`${r.system_instructions.trim()}

`),r.general_instructions?.trim()&&(i+=`${r.general_instructions.trim()}

`),r.behavior_guidelines?.trim()&&(i+=`## Behavior Guidelines:
${r.behavior_guidelines.trim()}

`),r.examples&&r.examples.length>0&&(i+=`## Training Examples:
`,r.examples.forEach((e,t)=>{i+=`Example ${t+1}:
User: ${e.input}
Assistant: ${e.output}

`})),i.trim()){i+=`---
IMPORTANT INSTRUCTIONS:
1. Follow the training examples and behavior guidelines above
2. Maintain the personality and behavior patterns shown in the examples
3. Apply the system instructions and general instructions consistently

Now respond to the user following these patterns and guidelines.`;let t=[...e],r=t.findIndex(e=>"system"===e.role);if(r>=0){let e=t[r].content,a="string"==typeof e?e:Array.isArray(e)&&e.find(e=>"text"===e.type)?.text||"";t[r].content=i+"\n\n"+a}else t.unshift({role:"system",content:i});return t}return e}setInterval(function(){let e=Date.now();for(let[t,r]of(sW.trainingDataCache.cleanup(),sZ.entries()))e-r.timestamp>36e5&&sZ.delete(t)},6e5);let n_=q.z.object({custom_api_config_id:q.z.string().uuid({message:"custom_api_config_id must be a valid UUID."}),role:q.z.string().optional(),messages:q.z.array(q.z.object({role:q.z.enum(["user","assistant","system"]),content:q.z.any()})).min(1,{message:"Messages array cannot be empty and must contain at least one message."}),model:q.z.string().optional(),stream:q.z.boolean().optional().default(!1),temperature:q.z.number().optional(),max_tokens:q.z.number().int().positive().optional(),specific_api_key_id:q.z.string().uuid().optional()}).catchall(q.z.any());async function nw(e,t,i,a,s){let n=process.env.ROKEY_CLASSIFICATION_GEMINI_API_KEY;if(!n)return{targetApiKeyData:null,roleUsedState:"missing_classification_api_key"};if(!e.user_id)return{targetApiKeyData:null,roleUsedState:"missing_user_id"};let o="";if(i.messages&&i.messages.length>0){let e=i.messages[i.messages.length-1];if("user"===e.role&&"string"==typeof e.content)o=e.content;else if("user"===e.role&&Array.isArray(e.content)){let t=e.content.find(e=>"text"===e.type);t&&"string"==typeof t.text&&(o=t.text)}}if(!o&&i.prompt&&(o=i.prompt),!o)return{targetApiKeyData:null,roleUsedState:"no_prompt_for_classification"};let l=nr(i.messages),c=s8.get(l);if(c){let r=Date.now()-c.lastActivity,s=function(e,t){let r=e.toLowerCase().trim();for(let[e,i]of Object.entries({coding_frontend:["write code","write python","write javascript","write java","write c++","code this","program this","create a script","build an app","make a website","write html","write css","write react","frontend code","client code","create component","build interface","ui code","web development"],coding_backend:["write api","create server","database code","backend code","server code","write sql","create endpoint","api development","microservice","write node","express code","django code","flask code"],data_analysis:["analyze this data","create a chart","make a graph","data analysis","statistical analysis","create visualization","data science","machine learning","pandas code","numpy analysis","plot this","visualize data"],writing:["write an article","write a blog","create content","write an essay","write documentation","create copy","marketing content","blog post","article about","essay on","content for"],translation:["translate this","translate to","convert to language","in spanish","in french","in german","in chinese","translate into"],summarization:["summarize this","create summary","tldr","brief overview","key points","main ideas","executive summary"]}))if(e!==t){for(let t of i)if(r.includes(t))return{isTransition:!0,newTaskRole:e,reasoning:`explicit_task_transition: "${t}" -> ${e}`}}let i=["now","instead","switch to","change to","help me","can you","please","i want you to","i need you to"].some(e=>r.includes(e)),a=["create","build","make","develop","design","implement","generate","produce","construct","craft","compose"].some(e=>r.includes(e));if(i&&a)return{isTransition:!0,newTaskRole:null,reasoning:"transition_keyword_with_action_verb"};for(let e of["now write","now create","now build","now make","now help","instead write","instead create","can you write","can you create","help me write","help me create","help me build"])if(r.includes(e))return{isTransition:!0,newTaskRole:null,reasoning:`strong_transition_phrase: "${e}"`};return{isTransition:!1,newTaskRole:null,reasoning:"no_transition_detected"}}(o,c.lastClassifiedRole);if(s.isTransition){if(s.newTaskRole){s1(e.user_id,s.newTaskRole),s8.set(l,{lastClassifiedRole:s.newTaskRole,messageCount:i.messages.length,lastActivity:Date.now(),confidence:.9,conversationId:l});let r=await nm(e.user_id,t,a);if(r){let e=r.roleAssignments.find(e=>e.role_name===s.newTaskRole);if(e&&e.api_key_id){let t=r.apiKeys.find(t=>t.id===e.api_key_id);if(t)return{targetApiKeyData:t,roleUsedState:`context_transition_${c.lastClassifiedRole}_to_${s.newTaskRole}`}}}}}else{let s=function(e,t,r,i){let a=e.toLowerCase().trim(),s=["continue","continue please","keep going","go on","more","more please","finish","complete","what next","then what","and then"],n=r.slice().reverse().find(e=>"assistant"===e.role),o=n&&"string"==typeof n.content&&(n.content.includes("[SYNTHESIS CONTINUES")||n.content.includes("**[SYNTHESIS CONTINUES")||n.content.includes("The response will continue")||n.content.length>1500);if(s.includes(a)&&i<6e5&&o)return{isContinuation:!0,confidence:.98,reasoning:"universal_synthesis_continuation"};if(s.includes(a)&&i<12e4)return{isContinuation:!0,confidence:.85,reasoning:"universal_short_continuation"};let l={StoryTeller:{strong:["continue","what happens next","keep going","more story","then what"],medium:["be creative","more","and then","what about","tell me more"],weak:["go on","next","more please","continue please"]},coding_frontend:{strong:["fix this","debug this","improve this code","add feature"],medium:["make it better","optimize","refactor","enhance"],weak:["change","update","modify"]},coding_backend:{strong:["fix this","debug this","improve this code","add feature"],medium:["make it better","optimize","refactor","enhance"],weak:["change","update","modify"]},writing:{strong:["revise this","edit this","improve this","rewrite this"],medium:["make it better","enhance","polish"],weak:["change","update","fix"]},data_analysis:{strong:["analyze more","deeper analysis","what else","more insights"],medium:["explain further","elaborate","more details"],weak:["continue","more"]}}[t];if(!l)return{isContinuation:!1,confidence:0,reasoning:"no_patterns_for_role"};for(let e of l.strong)if(a.includes(e))return{isContinuation:!0,confidence:.95,reasoning:`strong_pattern_match: "${e}"`};for(let e of l.medium)if(a.includes(e))return{isContinuation:!0,confidence:.8,reasoning:`medium_pattern_match: "${e}"`};if(i<12e4){for(let e of l.weak)if(a.includes(e))return{isContinuation:!0,confidence:.6,reasoning:`weak_pattern_match: "${e}" (recent)`}}return a.length<20&&i<3e5&&["yes","no","ok","sure","please","thanks","more","again"].some(e=>a.includes(e))?{isContinuation:!0,confidence:.7,reasoning:"short_continuation_prompt"}:i<6e4?{isContinuation:!0,confidence:.65,reasoning:"very_recent_message"}:i<3e5?{isContinuation:!0,confidence:.4,reasoning:"recent_message"}:{isContinuation:!1,confidence:0,reasoning:"no_continuation_detected"}}(o,c.lastClassifiedRole,i.messages,r);if(s.isContinuation&&s.confidence>.6){c.lastActivity=Date.now(),c.messageCount++,c.confidence=s.confidence;let r=c.lastClassifiedRole;s1(e.user_id,r);let i=await nm(e.user_id,t,a);if(i){let e=i.roleAssignments.find(e=>e.role_name===r);if(e&&e.api_key_id){let t=i.apiKeys.find(t=>t.id===e.api_key_id);if(t)return{targetApiKeyData:t,roleUsedState:`contextual_continuation_${r}_confidence_${Math.round(100*s.confidence)}`}}}}}}let u=nu(o),h=`${t}_${u}`;e.user_id;let d=sZ.get(h);if(d&&Date.now()-d.timestamp<36e5){let r=await nm(e.user_id,t,a);if(r)if("general_chat"===d.roleId){let e=r.apiKeys.find(e=>!0===e.is_default_general_chat_model);if(e)return{targetApiKeyData:e,roleUsedState:z.intelligentRoleRouting(d.roleId)}}else{let e=r.roleAssignments.find(e=>e.role_name===d.roleId);if(e&&e.api_key_id){let t=r.apiKeys.find(t=>t.id===e.api_key_id);if(t)return{targetApiKeyData:t,roleUsedState:z.intelligentRoleRouting(d.roleId||"general_chat")}}}}let[p,m]=await Promise.all([nm(e.user_id,t,a),Promise.resolve().then(()=>F.p2.map(e=>({id:e.id,name:e.name,description:e.description||""})))]);if(!p)return{targetApiKeyData:null,roleUsedState:"ecosystem_load_failed"};let f=[...m,...p.customRoles.map(e=>({id:e.role_id,name:e.name,description:e.description||""}))],g=function(e,t){let r=function(e,t=5){let r=sQ.get(e);return r?Object.entries(r).filter(([e,t])=>Date.now()-t.lastUsed<6048e5).sort(([e,t],[r,i])=>i.count-t.count).slice(0,t).map(([e,t])=>e):[]}(t,8);return 0===r.length?e:[...e.filter(e=>r.includes(e.id)),...e.filter(e=>!r.includes(e.id))]}(f,e.user_id),b=p.roleAssignments;if(0===f.length)return{targetApiKeyData:null,roleUsedState:"no_roles_available"};i.messages&&i.messages.slice(-3).map(e=>`${e.role}: ${"string"==typeof e.content?e.content.substring(0,200):Array.isArray(e.content)?e.content.find(e=>"text"===e.type)?.text?.substring(0,200)||"[non-text]":"[unknown]"}`).join("\n"),g.map(e=>`- ${e.id}: ${e.name}`).join("\n"),o.substring(0,1e3);let y=null;try{let a=await s5(o,g,n,t,i.messages||[]);if(a){if("isMultiRole"in a&&a.isMultiRole){try{let s=a.roles.map(e=>e.roleId),n={};for(let e of s){let t=null,r=p.roleAssignments.find(t=>t.role_name===e);r&&r.api_key_id&&(t=p.apiKeys.find(e=>e.id===r.api_key_id)),t&&(n[e]=t)}let l=Object.keys(n);if(l.length>=2){let a=(await r.e(1542).then(r.bind(r,71542))).default.getInstance();a.resetColorIndex();let s={onClassificationStart:()=>{a.classificationStart()},onClassificationComplete:(e,t)=>{a.classificationComplete(e,t)},onRoleSelectionComplete:(e,t)=>{a.roleSelectionComplete(e,t)},onWorkflowSelectionComplete:(e,t)=>{a.workflowSelectionComplete(e,t)},onAgentCreationStart:()=>{a.agentCreationStart()},onAgentCreationComplete:e=>{a.agentCreationComplete(e)},onSupervisorInitStart:()=>{a.supervisorInitStart()},onSupervisorInitComplete:e=>{a.supervisorInitComplete(e)},onTaskPlanningStart:()=>{a.taskPlanningStart()},onTaskPlanningComplete:e=>{a.taskPlanningComplete(e)},onAgentWorkStart:(e,t)=>{a.agentWorkStart(e,t)},onAgentWorkComplete:(e,t)=>{a.agentWorkComplete(e,t)},onSupervisorSynthesisStart:()=>{a.supervisorSynthesisStart()},onSupervisorSynthesisComplete:e=>{a.supervisorSynthesisComplete(e)},onOrchestrationComplete:e=>{a.orchestrationComplete(e)},onError:(e,t)=>{a.error(e,t)}},c=new ReadableStream({async start(r){let c=new TextEncoder,u=e=>{let t={id:`progress-${sM().randomUUID()}`,object:"orchestration.progress",created:Math.floor(Date.now()/1e3),type:"progress",data:{message:e.message,type:e.type,colorIndex:e.colorIndex,timestamp:e.timestamp}};r.enqueue(c.encode(`data: ${JSON.stringify(t)}

`))};a.on("progress",u);try{let a=await sV.executeMultiRole(l,n,o,{conversationId:i.conversation_id,userId:e.user_id,customApiConfigId:t,workflowType:"auto",enableStreaming:!1,progressCallback:s});if(a.success){let e=a.finalResponse;for(let t=0;t<e.length;t+=3){let i=e.substring(t,t+3),a={id:`chatcmpl-${sM().randomUUID()}`,object:"chat.completion.chunk",created:Math.floor(Date.now()/1e3),model:"rokey-agents-orchestration",choices:[{index:0,delta:{content:i},finish_reason:null}]};r.enqueue(c.encode(`data: ${JSON.stringify(a)}

`)),await new Promise(e=>setTimeout(e,50))}let t={id:`chatcmpl-${sM().randomUUID()}`,object:"chat.completion.chunk",created:Math.floor(Date.now()/1e3),model:"rokey-agents-orchestration",choices:[{index:0,delta:{},finish_reason:"stop"}],usage:{prompt_tokens:0,completion_tokens:a.metadata.totalTokens,total_tokens:a.metadata.totalTokens}};r.enqueue(c.encode(`data: ${JSON.stringify(t)}

`))}else{let e={id:`chatcmpl-${sM().randomUUID()}`,object:"chat.completion.chunk",created:Math.floor(Date.now()/1e3),model:"rokey-agents-orchestration",choices:[{index:0,delta:{content:`❌ Orchestration failed: ${a.finalResponse}`},finish_reason:"stop"}]};r.enqueue(c.encode(`data: ${JSON.stringify(e)}

`))}}catch(t){let e={id:`chatcmpl-${sM().randomUUID()}`,object:"chat.completion.chunk",created:Math.floor(Date.now()/1e3),model:"rokey-agents-orchestration",choices:[{index:0,delta:{content:`❌ Orchestration error: ${t instanceof Error?t.message:"Unknown error"}`},finish_reason:"stop"}]};r.enqueue(c.encode(`data: ${JSON.stringify(e)}

`))}r.enqueue(c.encode("data: [DONE]\n\n")),a.removeListener("progress",u),r.close()}});return{targetApiKeyData:null,roleUsedState:`RouKey_Multi Roles_${l.join("_")}_routing`,hybridResponse:new Response(c,{headers:{"Content-Type":"text/event-stream","Cache-Control":"no-cache",Connection:"keep-alive"}})}}}catch(e){}a.roles.length>0&&(y=a.roles[0].roleId)}else"roleId"in a&&(y=a.roleId);y&&sZ.set(h,{roleId:y,timestamp:Date.now()});let s=`user_${e.user_id}_general_pattern`;if("general_chat"===y){let e=sZ.get(s)||{consecutiveGeneralChat:0,timestamp:Date.now()};sZ.set(s,{consecutiveGeneralChat:(e.consecutiveGeneralChat||0)+1,timestamp:Date.now()})}else sZ.set(s,{consecutiveGeneralChat:0,timestamp:Date.now()})}else y="general_chat"}catch(e){y="general_chat"}if(y){if(s1(e.user_id,y),"general_chat"===y){let e=p.apiKeys.find(e=>!0===e.is_default_general_chat_model);if(e)return{targetApiKeyData:e,roleUsedState:z.intelligentRoleRouting(y)}}let t=b.find(e=>e.role_name===y);if(t&&t.api_key_id){let e=p.apiKeys.find(e=>e.id===t.api_key_id);if(e)return{targetApiKeyData:e,roleUsedState:z.intelligentRoleRouting(y)}}}return{targetApiKeyData:null,roleUsedState:"classification_no_key_found"}}async function nv(e,t,r){let i=e?.ordered_api_key_ids;if(!Array.isArray(i)||0===i.length)return{targetApiKeyData:null,roleUsedState:"strict_fallback_no_keys_defined"};let a=i.map(async(e,i)=>{try{let{data:a,error:s}=await r.from("api_keys").select("*").eq("id",e).eq("custom_api_config_id",t).single();return{index:i,keyId:e,apiKey:a,error:s,success:!s&&a&&"active"===a.status}}catch(t){return{index:i,keyId:e,apiKey:null,error:t,success:!1}}}),s=await Promise.allSettled(a);for(let e=0;e<s.length;e++){let t=s[e];if("fulfilled"===t.status&&t.value.success){let{apiKey:r,keyId:i}=t.value;return{targetApiKeyData:r,roleUsedState:z.fallbackRouting(e)}}if("fulfilled"===t.status){let{keyId:e,apiKey:r,error:i}=t.value;i&&i.code}}return{targetApiKeyData:null,roleUsedState:"strict_fallback_no_active_key_in_list"}}async function nk(e,t,r,i){try{let a,s="";if(r.messages&&r.messages.length>0){let e=r.messages[r.messages.length-1];if("user"===e.role&&"string"==typeof e.content)s=e.content;else if("user"===e.role&&Array.isArray(e.content)){let t=e.content.find(e=>"text"===e.type);t&&"string"==typeof t.text&&(s=t.text)}}if(!s)return await nO(e,t,i);let{data:n}=await i.from("cost_optimization_profiles").select("*").eq("user_id",e.user_id).eq("custom_api_config_id",t).single();if(!n||!n.learning_phase_completed)return await nx(e,t,s,i);let o=process.env.ROKEY_CLASSIFICATION_GEMINI_API_KEY;if(!o)return await nO(e,t,i);let l=await nj(s,o);if(!l||l<1||l>5)return await nS("moderate",e,t,i);a=l<=2?"cheap":l<=3?"moderate":"premium";let c=await nS(a,e,t,i);if(c.targetApiKeyData)return{...c,qualityMetrics:{complexityLevel:l,targetTier:a,routingReason:`complexity_${l}_tier_${a}`}};for(let r of"cheap"===a?["moderate","premium"]:"moderate"===a?["cheap","premium"]:["moderate","cheap"]){let a=await nS(r,e,t,i);if(a.targetApiKeyData)return{...a,qualityMetrics:{complexityLevel:l,targetTier:r,routingReason:`complexity_${l}_fallback_${r}`}}}return{targetApiKeyData:null,roleUsedState:"smart_cost_optimized_no_models"}}catch(e){return{targetApiKeyData:null,roleUsedState:"smart_cost_optimized_error"}}}async function nO(e,t,r){try{let{data:e,error:i}=await r.from("api_keys").select("*").eq("custom_api_config_id",t).eq("is_active",!0);if(i||!e||0===e.length)return{targetApiKeyData:null,roleUsedState:"basic_cost_optimized_no_keys"};let a=e.map(e=>e.predefined_model_id).filter(Boolean),{data:s,error:n}=await r.from("models").select("id, input_token_price, output_token_price, name, display_name").in("id",a);if(n||!s)return{targetApiKeyData:null,roleUsedState:"basic_cost_optimized_pricing_error"};let o=e.map(e=>{let t=s.find(t=>t.id===e.predefined_model_id);if(!t||!t.input_token_price||!t.output_token_price)return{...e,totalCostPer1000Tokens:1/0,pricing:null};let r=500*t.input_token_price+500*t.output_token_price;return{...e,totalCostPer1000Tokens:r,pricing:t}}).sort((e,t)=>e.totalCostPer1000Tokens-t.totalCostPer1000Tokens)[0];if(!o||o.totalCostPer1000Tokens===1/0)return{targetApiKeyData:null,roleUsedState:"basic_cost_optimized_no_pricing"};return{targetApiKeyData:o,roleUsedState:"basic_cost_optimized_success"}}catch(e){return{targetApiKeyData:null,roleUsedState:"basic_cost_optimized_error"}}}async function nx(e,t,r,i){try{let a=await nS("cheap",e,t,i);return await nC({user_id:e.user_id,custom_api_config_id:t,api_key_id:a.targetApiKeyData?.id,prompt_text:r.substring(0,1e3),routing_strategy:"cost_optimized_learning",was_ab_test:!1},i),{...a,qualityMetrics:{learningPhase:!0,routingReason:"learning_phase_cheapest_first"}}}catch(e){return{targetApiKeyData:null,roleUsedState:"conservative_learning_error"}}}async function nS(e,t,r,i){try{let{data:a,error:s}=await i.from("api_keys").select("*").eq("custom_api_config_id",r).eq("is_active",!0);if(s||!a||0===a.length)return{targetApiKeyData:null,roleUsedState:`tier_${e}_no_keys`};let n=a.map(e=>e.predefined_model_id).filter(Boolean),{data:o,error:l}=await i.from("model_cost_tiers").select("*").in("model_id",n).eq("cost_tier",e);if(l)return await nO(t,r,i);let c=o?.map(e=>e.model_id)||[],u=a.filter(e=>c.includes(e.predefined_model_id));if(0===u.length)return{targetApiKeyData:null,roleUsedState:`tier_${e}_no_models`};return{targetApiKeyData:u.sort((e,t)=>{let r=o.find(t=>t.model_id===e.predefined_model_id),i=o.find(e=>e.model_id===t.predefined_model_id);return(i?.cost_efficiency_score||0)-(r?.cost_efficiency_score||0)})[0],roleUsedState:`tier_${e}_success`}}catch(t){return{targetApiKeyData:null,roleUsedState:`tier_${e}_error`}}}async function nC(e,t){try{let{error:r}=await t.from("routing_quality_metrics").insert([{...e,created_at:new Date().toISOString()}])}catch(e){}}async function nA(e,t,r,i){try{let{data:a,error:s}=await i.from("ab_test_assignments").select("*").eq("user_id",e.user_id).eq("custom_api_config_id",t).single();if(s&&"PGRST116"!==s.code)return{targetApiKeyData:null,roleUsedState:"ab_routing_assignment_error"};if(!a){let{data:r,error:s}=await i.from("ab_test_assignments").insert([{user_id:e.user_id,custom_api_config_id:t,test_percentage:15,test_active:!0}]).select().single();if(s)return{targetApiKeyData:null,roleUsedState:"ab_routing_create_error"};a=r}let n=Math.random()<a.test_percentage/100,o=null,l="";if(n?(o=await nT(e,t,i),l="ab_test_group"):(o=await nP(e,t,i),l="ab_control_group"),!o)return{...await nO(e,t,i),abTestMetrics:{isTestRequest:n,routingReason:"ab_fallback_basic_cost"}};let c=n?{test_requests:(a.test_requests||0)+1,total_requests:(a.total_requests||0)+1}:{control_requests:(a.control_requests||0)+1,total_requests:(a.total_requests||0)+1};await i.from("ab_test_assignments").update(c).eq("id",a.id);let u=function(e){if(e.messages&&e.messages.length>0){let t=e.messages[e.messages.length-1];if("user"===t.role&&"string"==typeof t.content)return t.content;if("user"===t.role&&Array.isArray(t.content)){let e=t.content.find(e=>"text"===e.type);if(e&&"string"==typeof e.text)return e.text}}return null}(r);return await nC({user_id:e.user_id,custom_api_config_id:t,api_key_id:o.id,model_used:o.predefined_model_id,provider:o.provider,prompt_text:u?.substring(0,1e3)||"",routing_strategy:"ab_routing",was_ab_test:!0,ab_test_group:n?"test":"control"},i),{targetApiKeyData:o,roleUsedState:"ab_routing_success",abTestMetrics:{isTestRequest:n,routingReason:l,abTestId:a.id}}}catch(e){return{targetApiKeyData:null,roleUsedState:"ab_routing_error"}}}async function nT(e,t,r){try{let{data:e,error:i}=await r.from("api_keys").select("*").eq("custom_api_config_id",t).eq("is_active",!0);if(i||!e||0===e.length)return null;let a=Math.floor(Math.random()*e.length);return e[a]}catch(e){return null}}async function nP(e,t,r){try{let i=new Date;i.setDate(i.getDate()-30);let{data:a,error:s}=await r.from("routing_quality_metrics").select("api_key_id, quality_score, cost_per_quality_point").eq("user_id",e.user_id).eq("custom_api_config_id",t).gte("created_at",i.toISOString()).not("quality_score","is",null);if(s||!a||0===a.length)return await nE(e,t,r);let n=new Map;a.forEach(e=>{n.has(e.api_key_id)||n.set(e.api_key_id,{totalQuality:0,totalCostEfficiency:0,count:0});let t=n.get(e.api_key_id);t.totalQuality+=e.quality_score||0,t.totalCostEfficiency+=1/(e.cost_per_quality_point||1),t.count+=1});let o=null,l=0;if(n.forEach((e,t)=>{let r=e.totalQuality/e.count,i=e.totalCostEfficiency/e.count,a=r*i;a>l&&(l=a,o=t)}),o){let{data:e,error:t}=await r.from("api_keys").select("*").eq("id",o).eq("is_active",!0).single();if(!t&&e)return e}return await nE(e,t,r)}catch(i){return await nE(e,t,r)}}async function nE(e,t,r){return(await nO(e,t,r)).targetApiKeyData}async function nj(e,t){try{let r=`Analyze the following user prompt and classify its complexity level on a scale of 1-5:

1 = Very Simple (basic questions, greetings, simple requests)
2 = Simple (straightforward questions, basic conversations)
3 = Moderate (explanations, analysis, moderate complexity tasks)
4 = Complex (detailed analysis, coding, research, multi-step reasoning)
5 = Very Complex (advanced reasoning, complex coding, comprehensive research)

User Prompt: "${e}"

Respond with ONLY the number (1-5) representing the complexity level.`,i=await fetch("https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-lite-001:generateContent?key="+t,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({contents:[{parts:[{text:r}]}],generationConfig:{temperature:.1,maxOutputTokens:10}})});if(!i.ok)return null;let a=await i.json(),s=a.candidates?.[0]?.content?.parts?.[0]?.text;if(!s)return null;let n=parseInt(s.trim());if(isNaN(n)||n<1||n>5)return null;return n}catch(e){return null}}async function nI(e,t,r){let i=process.env.ROKEY_CLASSIFICATION_GEMINI_API_KEY;if(!i)return{targetApiKeyData:null,roleUsedState:"complexity_rr_missing_classifier_key"};let a="";if(t.messages&&t.messages.length>0){let e=t.messages[t.messages.length-1];if("user"===e.role&&"string"==typeof e.content)a=e.content;else if("user"===e.role&&Array.isArray(e.content)){let t=e.content.find(e=>"text"===e.type);t&&"string"==typeof t.text&&(a=t.text)}}if(!a&&t.prompt&&(a=t.prompt),!a)return{targetApiKeyData:null,roleUsedState:"complexity_rr_no_prompt"};let s=await nj(a,i);if(null===s||s<1||s>5)return{targetApiKeyData:null,roleUsedState:"complexity_rr_invalid_classification"};let{data:n,error:o}=await r.from("config_api_key_complexity_assignments").select(`
      api_key_id,
      complexity_level,
      api_keys!inner (
        id,
        status,
        provider,
        predefined_model_id,
        is_default_general_chat_model,
        encrypted_api_key,
        label
      )
    `).eq("custom_api_config_id",e.id).eq("complexity_level",s);if(o)return{targetApiKeyData:null,roleUsedState:"complexity_rr_db_error",classifiedComplexityLevel:s,classifiedComplexityLLM:"gemini-2.0-flash-lite"};let l=n?.filter(e=>"active"===e.api_keys.status)?.map(e=>e.api_keys)||[];if(l.length>0){let t=e.routing_strategy_params||{},i=`_complexity_${s}_rr_idx`,a="number"==typeof t[i]?t[i]:0,n=[...l].sort((e,t)=>e.id.localeCompare(t.id)),o=n[a%n.length],c=(a+1)%n.length;return t[i]=c,setImmediate(async()=>{let{error:i}=await r.from("custom_api_configs").update({routing_strategy_params:t}).eq("id",e.id)}),{targetApiKeyData:o,roleUsedState:`complexity_rr_level_${s}_key_found`,classifiedComplexityLevel:s,classifiedComplexityLLM:"gemini-2.0-flash-lite"}}let c=[];for(let e=1;e<=4;e++)s-e>=1&&c.push(s-e),s+e<=5&&c.push(s+e);for(let t of c){let{data:i,error:a}=await r.from("config_api_key_complexity_assignments").select(`
        api_key_id,
        complexity_level,
        api_keys!inner (
          id,
          status,
          provider,
          predefined_model_id,
          is_default_general_chat_model,
          encrypted_api_key,
          label
        )
      `).eq("custom_api_config_id",e.id).eq("complexity_level",t);if(a)continue;let n=i?.filter(e=>"active"===e.api_keys.status)?.map(e=>e.api_keys)||[];if(n.length>0){let i=e.routing_strategy_params||{},a=`_complexity_${t}_rr_idx`,o="number"==typeof i[a]?i[a]:0,l=[...n].sort((e,t)=>e.id.localeCompare(t.id)),c=l[o%l.length],u=(o+1)%l.length;return i[a]=u,setImmediate(async()=>{let{error:t}=await r.from("custom_api_configs").update({routing_strategy_params:i}).eq("id",e.id)}),{targetApiKeyData:c,roleUsedState:`complexity_rr_level_${s}_proximal_${t}_key_found`,classifiedComplexityLevel:s,classifiedComplexityLLM:"gemini-2.0-flash-lite"}}}return{targetApiKeyData:null,roleUsedState:"complexity_rr_no_keys_found",classifiedComplexityLevel:s,classifiedComplexityLLM:"gemini-2.0-flash-lite"}}async function nN(e,t,r){let[i,a]=await Promise.allSettled([r?e.from("api_key_role_assignments").select("api_key_id").eq("custom_api_config_id",t).eq("role_name",r).single():Promise.resolve({data:null,error:null}),e.from("api_keys").select("*").eq("custom_api_config_id",t).eq("is_default_general_chat_model",!0).single()]);if(r&&"fulfilled"===i.status&&i.value.data){let t=i.value.data,{data:r,error:a}=await e.from("api_keys").select("*").eq("id",t.api_key_id).single();if(a);else if(r&&"active"===r.status)return r}else r&&i.status;if("fulfilled"===a.status&&a.value.data){let e=a.value.data;if("active"===e.status)return e}else a.status;return null}let nR={CLASSIFICATION:5e3,LLM_REQUEST:8e3,SOCKET:1500,GOOGLE_CLASSIFICATION:7e3},nM=new(sG()).Agent({keepAlive:!0,keepAliveMsecs:12e4,maxSockets:200,maxFreeSockets:50,timeout:nR.SOCKET,scheduling:"lifo",maxTotalSockets:500}),nD=new(sH()).Agent({keepAlive:!0,keepAliveMsecs:12e4,maxSockets:200,maxFreeSockets:50,timeout:nR.SOCKET,scheduling:"lifo",maxTotalSockets:500});async function n$(e,t,i=3,a){let s,n=a||(e.includes("generativelanguage.googleapis.com")?nR.GOOGLE_CLASSIFICATION:nR.LLM_REQUEST);for(let a=1;a<=i;a++)try{return await function(e,t,i=nR.LLM_REQUEST){return new Promise((a,s)=>{let n=new sX.URL(e),o="https:"===n.protocol,l=o?sG():sH(),c={hostname:n.hostname,port:n.port||(o?443:80),path:n.pathname+n.search,method:t.method||"GET",headers:{...t.headers,Connection:"keep-alive","Keep-Alive":`timeout=${Math.floor(i/1e3)}, max=100`},timeout:i,agent:o?nM:nD},u=l.request(c,e=>{let t=e,i=e.headers["content-encoding"];if("gzip"===i){let i=r(74075);t=e.pipe(i.createGunzip())}else if("deflate"===i){let i=r(74075);t=e.pipe(i.createInflate())}else if("br"===i){let i=r(74075);t=e.pipe(i.createBrotliDecompress())}if(e.headers["content-type"]?.includes("text/event-stream")||e.headers["content-type"]?.includes("text/plain")||e.headers["content-type"]?.includes("application/x-ndjson"))a(new Response(new ReadableStream({start(e){t.on("data",t=>{let r=t.toString("utf8");e.enqueue(new TextEncoder().encode(r))}),t.on("end",()=>{e.close()}),t.on("error",t=>{e.error(t)})}}),{status:e.statusCode,statusText:e.statusMessage||"",headers:new Headers(e.headers)}));else{let r=[];t.on("data",e=>{r.push(Buffer.isBuffer(e)?e:Buffer.from(e))}),t.on("end",()=>{a(new Response(Buffer.concat(r).toString("utf8"),{status:e.statusCode,statusText:e.statusMessage||"",headers:new Headers(e.headers)}))})}});u.on("error",e=>{s(Error(`Network request failed: ${e.message}`))}),u.on("timeout",()=>{u.destroy(),s(Error(`Request timeout after ${i}ms`))}),t.body&&u.write(t.body),u.end()})}(e,t,n)}catch(t){if(s=t,a===i)throw t;let e=t.message.includes("timeout")?50:100*a;await new Promise(t=>setTimeout(t,e))}throw s}async function nL(e,t,i,a){let s,n,o,l,c,u=null,h=new Date;if(!a.stream&&a.messages&&t){let e=nh(a.messages,t,a.temperature),r=nl.get(e);if(r&&Date.now()-r.timestamp<12e4)return{success:!0,response:void 0,responseData:r.response,responseHeaders:new Headers({"x-rokey-cache":"hit"}),status:200,error:null,llmRequestTimestamp:new Date,llmResponseTimestamp:new Date}}let d={method:"POST",headers:{"Content-Type":"application/json",Connection:"keep-alive","Keep-Alive":"timeout=30, max=100","User-Agent":"RoKey/1.0 (Performance-Optimized)",Accept:"application/json","Cache-Control":"no-cache"}};try{let p=function(e,t){if(!e)return"";let r=t.toLowerCase(),i=`${r}/`;return e.toLowerCase().startsWith(i)?e.substring(i.length):e}(t,e||""),m=e?.toLowerCase()==="openrouter"?t:p;if(!m)throw{message:`Effective model ID is missing for provider ${e} (DB Model: ${t})`,status:500,internal:!0};if(u=new Date,e?.toLowerCase()==="openai"){let{custom_api_config_id:e,role:t,...r}=a,u={...r,model:m,messages:a.messages,stream:a.stream};Object.keys(u).forEach(e=>void 0===u[e]&&delete u[e]);let p={...d};p.headers={...d.headers,Authorization:`Bearer ${i}`,Origin:"https://rokey.app",Connection:"keep-alive","Keep-Alive":"timeout=30, max=100","Cache-Control":"no-cache",Priority:"u=1, i"},p.body=JSON.stringify(u);let f=await n$("https://api.openai.com/v1/chat/completions",p);if(h=new Date,s=f.status,c=f.headers,!f.ok){let e=await f.json().catch(()=>({error:{message:f.statusText}}));throw n={message:`OpenAI Error: ${e?.error?.message||f.statusText}`,status:f.status,provider_error:e}}if(a.stream){if(!f.body)throw n={message:"OpenAI stream body null",status:500};o=f,l={note:"streamed"}}else l=await f.json()}else if(e?.toLowerCase()==="openrouter"){let{custom_api_config_id:e,role:t,...u}=a,p={...u,model:m,messages:a.messages,stream:a.stream,usage:{include:!0}};Object.keys(p).forEach(e=>void 0===p[e]&&delete p[e]);let f={...d};f.headers={...d.headers,Authorization:`Bearer ${i}`,"HTTP-Referer":"https://rokey.app","X-Title":"RoKey",Origin:"https://rokey.app",Connection:"keep-alive","Keep-Alive":"timeout=30, max=100","Cache-Control":"no-cache",Priority:"u=1, i"},f.body=JSON.stringify(p);let g=await n$("https://openrouter.ai/api/v1/chat/completions",f);if(h=new Date,s=g.status,c=g.headers,!g.ok){let e=await g.json().catch(()=>({error:{message:g.statusText}}));throw n={message:`OpenRouter Error: ${e?.error?.message||g.statusText}`,status:g.status,provider_error:e}}if(a.stream){if(!g.body)throw n={message:"OpenRouter stream body null",status:500};let{createFirstTokenTrackingStream:e}=await r.e(9704).then(r.bind(r,99704)),t=e(g.body,"OpenRouter",m);o=new Response(t,{status:g.status,statusText:g.statusText,headers:g.headers}),l={note:"streamed"}}else l=await g.json()}else if(e?.toLowerCase()==="google"){let e=m.replace(/^models\//,""),t=a.messages.map(e=>{if("string"==typeof e.content)return{role:e.role,content:e.content};if(Array.isArray(e.content)){let t=e.content.map(e=>"text"===e.type&&"string"==typeof e.text?{type:"text",text:e.text}:"image_url"===e.type&&e.image_url?.url?{type:"image_url",image_url:{url:e.image_url.url}}:null).filter(Boolean);return{role:e.role,content:t}}return{role:e.role,content:"[RoKey: Invalid content structure for Google]"}});if(0===t.length)throw n={message:"No processable message content found for Google provider after filtering.",status:400};let u={model:e,messages:t,stream:a.stream};void 0!==a.temperature&&(u.temperature=a.temperature),void 0!==a.max_tokens&&(u.max_tokens=a.max_tokens),void 0!==a.top_p&&(u.top_p=a.top_p);let p={...d};p.headers={...d.headers,Authorization:`Bearer ${i}`,Connection:"keep-alive","Keep-Alive":"timeout=30, max=100","User-Agent":"RoKey/1.0 (Performance-Optimized)",Origin:"https://rokey.app","Cache-Control":"no-cache",Priority:"u=1, i"},p.body=JSON.stringify(u);let f=await n$("https://generativelanguage.googleapis.com/v1beta/openai/chat/completions",p);if(h=new Date,s=f.status,c=f.headers,!f.ok){let e=await f.json().catch(()=>({error:{message:f.statusText}})),t=e?.error?.message||f.statusText;throw Array.isArray(e)&&e[0]?.error?.message&&(t=e[0].error.message),n={message:`Google Error: ${t}`,status:f.status,provider_error:e}}if(a.stream){if(!f.body)throw n={message:"Google stream body null",status:500};l={note:"streamed"};let{createFirstTokenTrackingStream:t}=await r.e(9704).then(r.bind(r,99704)),i=t(f.body,"Google",e);o=new Response(i,{status:f.status,statusText:f.statusText,headers:f.headers})}else l=await f.json()}else if(e?.toLowerCase()==="anthropic"){let e,t=a.max_tokens||2048,u=a.messages.filter(t=>"system"!==t.role||("string"==typeof t.content&&(e=t.content),!1)).map(e=>({role:e.role,content:e.content}));if(0===u.length||"user"!==u[0].role)throw n={message:"Invalid messages format for Anthropic: Must contain at least one user message and start with user after system filter.",status:400};let p={model:m,messages:u,max_tokens:t,stream:a.stream};e&&(p.system=e),void 0!==a.temperature&&(p.temperature=a.temperature);let f={...d};f.headers={...d.headers,"x-api-key":i,"anthropic-version":"2023-06-01",Origin:"https://rokey.app"},f.body=JSON.stringify(p);let g=await n$("https://api.anthropic.com/v1/messages",f);if(h=new Date,s=g.status,c=g.headers,!g.ok){let e=await g.json().catch(()=>({error:{message:g.statusText}}));throw n={message:`Anthropic Error: ${e?.error?.message||g.statusText} (Type: ${e?.error?.type})`,status:g.status,provider_error:e}}if(a.stream){if(!g.body)throw n={message:"Anthropic stream body null",status:500};let{createFirstTokenTrackingStream:e}=await r.e(9704).then(r.bind(r,99704)),t=e(g.body,"Anthropic",m);o=new Response(t,{status:g.status,headers:{"Content-Type":"text/event-stream","Cache-Control":"no-cache",Connection:"keep-alive"}}),l={note:"streamed"}}else{let e=await g.json(),t=e.content?.find(e=>"text"===e.type)?.text||"";l={id:e.id||`anthropic-exPR-${Date.now()}`,object:"chat.completion",created:Math.floor(Date.now()/1e3),model:e.model||m,choices:[{index:0,message:{role:"assistant",content:t},finish_reason:e.stop_reason?.toLowerCase()||"stop"}],usage:{prompt_tokens:e.usage?.input_tokens,completion_tokens:e.usage?.output_tokens,total_tokens:(e.usage?.input_tokens||0)+(e.usage?.output_tokens||0)}}}}else if(e?.toLowerCase()==="deepseek"){let{custom_api_config_id:e,role:t,...u}=a,p={...u,model:m,messages:a.messages,stream:a.stream};Object.keys(p).forEach(e=>void 0===p[e]&&delete p[e]);let f={...d};f.headers={...d.headers,Authorization:`Bearer ${i}`,Origin:"https://rokey.app"},f.body=JSON.stringify(p);let g=await n$("https://api.deepseek.com/chat/completions",f);if(h=new Date,s=g.status,c=g.headers,!g.ok){let e=await g.json().catch(()=>({error:{message:g.statusText}}));throw n={message:`DeepSeek Error: ${e?.error?.message||g.statusText} (Type: ${e?.error?.type})`,status:g.status,provider_error:e}}if(a.stream){if(!g.body)throw n={message:"DeepSeek stream body null",status:500};let{createFirstTokenTrackingStream:e}=await r.e(9704).then(r.bind(r,99704)),t=e(g.body,"DeepSeek",m);o=new Response(t,{status:g.status,statusText:g.statusText,headers:g.headers}),l={note:"streamed"}}else l=await g.json()}else if(e?.toLowerCase()==="xai"){let{custom_api_config_id:e,role:t,...u}=a,p={...u,model:m,messages:a.messages,stream:a.stream||!1};"number"==typeof a.temperature&&(p.temperature=a.temperature),"number"==typeof a.max_tokens&&(p.max_tokens=a.max_tokens),"number"==typeof a.top_p&&(p.top_p=a.top_p),"number"==typeof a.frequency_penalty&&(p.frequency_penalty=a.frequency_penalty),"number"==typeof a.presence_penalty&&(p.presence_penalty=a.presence_penalty);let f={...d};f.headers={...d.headers,Authorization:`Bearer ${i}`,Origin:"https://rokey.app"},f.body=JSON.stringify(p);let g=await n$("https://api.x.ai/v1/chat/completions",f);if(h=new Date,s=g.status,c=g.headers,!g.ok){let e=await g.json().catch(()=>({error:{message:g.statusText}}));throw n={message:`XAI/Grok Error: ${e?.error?.message||g.statusText} (Type: ${e?.error?.type})`,status:g.status,provider_error:e}}if(a.stream){if(!g.body)throw n={message:"XAI/Grok stream body null",status:500};let{createFirstTokenTrackingStream:e}=await r.e(9704).then(r.bind(r,99704)),t=e(g.body,"XAI",m);o=new Response(t,{status:g.status,headers:{...Object.fromEntries(g.headers),"Content-Type":"text/event-stream"}}),l={note:"streamed"}}else l=await g.json()}else throw n={message:`Provider '${e}' is configured but not supported by RoKey proxy (executeProviderRequest).`,status:501,internal:!0};if(!a.stream&&l&&a.messages&&t){let r=nh(a.messages,t,a.temperature);if(nl.set(r,{response:l,timestamp:Date.now(),provider:e||"unknown",model:t}),nl.size>1e3){let e=Array.from(nl.entries());e.sort((e,t)=>e[1].timestamp-t[1].timestamp);let t=Math.floor(.2*e.length);for(let r=0;r<t;r++)nl.delete(e[r][0])}}return{success:!0,response:o,responseData:l,responseHeaders:c,status:s,error:null,llmRequestTimestamp:u,llmResponseTimestamp:h}}catch(i){let e=n||i,t="ProviderCommsError",r="";return"AbortError"===i.name?(t="TimeoutError",r="Request timed out after 30 seconds"):i.message?.includes("fetch failed")?(t="NetworkError",r="Network connection failed - check internet connectivity"):"ENOTFOUND"===i.code?(t="DNSError",r="DNS resolution failed - check network settings"):"ECONNREFUSED"===i.code&&(t="ConnectionRefused",r="Connection refused by server"),{success:!1,status:e.status||500,error:e.provider_error||{message:`${e.message}${r?` (${r})`:""}`,type:e.internal?"RoKeyInternal":t,diagnostic:r},llmRequestTimestamp:u||new Date,llmResponseTimestamp:h||new Date,response:void 0,responseData:void 0,responseHeaders:c}}}async function nU(e,t){try{let e=await (0,$.createSupabaseServerClientOnRequest)(),{data:r,error:i}=await e.from("orchestration_executions").select("*").eq("id",t).single();if(i||!r)return D.NextResponse.json({error:"Execution not found"},{status:404});let{data:a,error:s}=await e.from("orchestration_steps").select("*").eq("execution_id",t).eq("status","completed").order("step_number");if(s||!a||0===a.length)return D.NextResponse.json({error:"No completed steps found"},{status:404});let n=`As an AI orchestration moderator, synthesize multiple specialist outputs into one cohesive response:

Original Request: "${r.original_prompt}"

Specialist Outputs:
${a.map(e=>`${e.step_number}. ${e.role_id}: "${e.output}"`).join("\n\n")}

Please synthesize the above analyses into a well-structured response that addresses the original request. Be sure to maintain all key insights and technical details while presenting them in a clear and organized manner.`,o=process.env.ROKEY_CLASSIFICATION_GEMINI_API_KEY;if(!o)return D.NextResponse.json({error:"Server configuration error"},{status:500});let l=await n$("https://generativelanguage.googleapis.com/v1beta/openai/chat/completions",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${o}`,"User-Agent":"RoKey/1.0 (Synthesis)",Origin:"https://rokey.app","Cache-Control":"no-cache"},body:JSON.stringify({model:"gemini-2.0-flash-001",messages:[{role:"user",content:n}],stream:!0,temperature:.3,max_tokens:8e3})});if(!l.ok)return D.NextResponse.json({error:"Synthesis API error"},{status:l.status});let c=l.body.getReader(),u=new TextDecoder,h="";try{for(;;){let{done:e,value:t}=await c.read();if(e)break;for(let e of u.decode(t,{stream:!0}).split("\n"))if(e.startsWith("data: ")&&!e.includes("[DONE]"))try{let t=e.substring(6),r=JSON.parse(t);r.choices?.[0]?.delta?.content&&(h+=r.choices[0].delta.content)}catch(e){}}}finally{c.releaseLock()}if(!h)return D.NextResponse.json({error:"Empty synthesis response"},{status:500});let d=`synthesis_${t}`,p=await na(d,h),{chunk:m,isComplete:f,progress:g}=await ns(p,0);if(!m)return D.NextResponse.json({error:"Failed to chunk synthesis"},{status:500});await e.from("orchestration_executions").update({status:"completed",completed_at:new Date().toISOString()}).eq("id",t);let b=new ReadableStream({async start(e){let t=new TextEncoder,r=m.split(/(\s+)/);for(let i=0;i<r.length;i++){let a=r[i],s={id:`synthesis-${Date.now()}-${i}`,object:"chat.completion.chunk",created:Math.floor(Date.now()/1e3),model:"gemini-2.0-flash-001",choices:[{index:0,delta:{content:a},finish_reason:null}]};e.enqueue(t.encode(`data: ${JSON.stringify(s)}

`)),i<r.length-1&&await new Promise(e=>setTimeout(e,10))}let i={id:`synthesis-${Date.now()}-final`,object:"chat.completion.chunk",created:Math.floor(Date.now()/1e3),model:"gemini-2.0-flash-001",choices:[{index:0,delta:{},finish_reason:f?"stop":"length"}]};e.enqueue(t.encode(`data: ${JSON.stringify(i)}

`)),e.enqueue(t.encode("data: [DONE]\n\n")),e.close()}});return new Response(b,{status:200,headers:{"Content-Type":"text/event-stream","Cache-Control":"no-cache",Connection:"keep-alive","Access-Control-Allow-Origin":"*","X-Accel-Buffering":"no","X-Synthesis-Id":p,"X-Synthesis-Progress":g,"X-Synthesis-Complete":f.toString()}})}catch(e){return D.NextResponse.json({error:"Internal synthesis error",details:e.message},{status:500})}}async function nF(e,t,r){let i=e.headers.get("X-Synthesis-Chunk-Index"),a=i?parseInt(i,10):1,{chunk:s,isComplete:n,progress:o,totalChunks:l,synthesisId:c}=await ns(r,a);if(!s)if(n||a>=l)return new Response(JSON.stringify({error:"synthesis_complete",message:"The synthesis is already complete. Your message will be processed as a new conversation."}),{status:200,headers:{"Content-Type":"application/json"}});else return new Response(JSON.stringify({error:"synthesis_not_found",message:"Synthesis data not found. Please start a new conversation."}),{status:404,headers:{"Content-Type":"application/json"}});return new Response(new ReadableStream({async start(e){let t=new TextEncoder,r=s.split(/(\s+)/);for(let i=0;i<r.length;i++){let s=r[i],o={id:sM().randomUUID(),object:"chat.completion.chunk",created:Math.floor(Date.now()/1e3),model:"synthesis-continuation",choices:[{index:0,delta:{content:s},finish_reason:null}],synthesis_metadata:{synthesis_id:c,current_chunk:a,total_chunks:l,has_more:!n}};e.enqueue(t.encode(`data: ${JSON.stringify(o)}

`)),i<r.length-1&&await new Promise(e=>setTimeout(e,10))}if(!n){let r={id:sM().randomUUID(),object:"chat.completion.chunk",created:Math.floor(Date.now()/1e3),model:"synthesis-continuation",choices:[{index:0,delta:{content:`

**[SYNTHESIS CONTINUES - Please type 'continue' to see the rest (${o})]**`},finish_reason:null}],synthesis_metadata:{synthesis_id:c,current_chunk:a,total_chunks:l,has_more:!0}};e.enqueue(t.encode(`data: ${JSON.stringify(r)}

`))}let i={id:sM().randomUUID(),object:"chat.completion.chunk",created:Math.floor(Date.now()/1e3),model:"synthesis-continuation",choices:[{index:0,delta:{},finish_reason:n?"stop":"length"}],synthesis_metadata:{synthesis_id:c,current_chunk:a,total_chunks:l,has_more:!n}};e.enqueue(t.encode(`data: ${JSON.stringify(i)}

`)),e.enqueue(t.encode("data: [DONE]\n\n")),e.close()}}),{headers:{"Content-Type":"text/event-stream","Cache-Control":"no-cache",Connection:"keep-alive","X-Synthesis-Progress":o,"X-Synthesis-Complete":n.toString(),"X-Synthesis-ID":c,"X-Synthesis-Chunk-Index":a.toString(),"X-Synthesis-Total-Chunks":l.toString()}})}async function nq(e,t,r){let i=new TextEncoder;return new Response(new ReadableStream({async start(e){try{let a={id:sM().randomUUID(),object:"chat.completion.chunk",created:Math.floor(Date.now()/1e3),model:"rokey-orchestration-enhanced",choices:[{index:0,delta:{content:"\uD83C\uDFAC **Multi-Role AI Orchestration Started!**\n\nYour request is being processed by multiple specialized AI models working together.\n\n"},finish_reason:null}]};e.enqueue(i.encode(`data: ${JSON.stringify(a)}

`)),await nz(t,e,i,r)}catch(r){let t={id:sM().randomUUID(),object:"chat.completion.chunk",created:Math.floor(Date.now()/1e3),model:"rokey-orchestration-enhanced",choices:[{index:0,delta:{content:`

❌ **Error:** ${r instanceof Error?r.message:"Unknown error occurred"}

`},finish_reason:"stop"}]};e.enqueue(i.encode(`data: ${JSON.stringify(t)}

`)),e.enqueue(i.encode(`data: [DONE]

`)),e.close()}},cancel(){}}),{headers:{"Content-Type":"text/event-stream","Cache-Control":"no-cache",Connection:"keep-alive","Access-Control-Allow-Origin":"*","X-Accel-Buffering":"no","X-RoKey-Orchestration-ID":t,"X-RoKey-Orchestration-Active":"true"}})}async function nz(e,t,r,i){let a=await (0,$.createSupabaseServerClientOnRequest)(),s=Date.now(),n=0,o=!1,l={};for(;Date.now()-s<3e5;)try{let{data:c,error:u}=await a.from("orchestration_executions").select("*").eq("id",e).single();if(u)throw Error(`Failed to fetch execution: ${u.message}`);if(!c)throw Error(`Orchestration execution not found: ${e}`);let{data:h}=await a.from("orchestration_steps").select("*").eq("execution_id",e).order("step_number");if(h&&h.length>0){let a=h.filter(e=>"completed"===e.status),s=h.length;for(let e of(h.find(e=>"in_progress"===e.status),h.find(e=>"pending"===e.status),!o&&s>0&&(o=!0,await nV(h,t,r)),h)){let i=e.step_number,a=e.status,s=l[i];a!==s&&(l[i]=a,await nK(e,a,s,t,r))}if(a.length>n){n=a.length;let e=a[a.length-1];e&&await nB(e,a.length,s,t,r)}if(a.length===s&&s>0){await nW(a,t,r),await nJ(e,a,t,r,i);return}}else if(Date.now()-s>3e4)throw Error("No orchestration steps found after 30 seconds");await new Promise(e=>setTimeout(e,2e3))}catch(e){throw e}throw Error("Orchestration timed out after 5 minutes")}async function nV(e,t,r){let i={brainstorming_ideation:"\uD83E\uDDE0",coding_backend:"\uD83D\uDCBB",coding_frontend:"\uD83C\uDFA8",education_tutoring:"\uD83C\uDF93",research_analysis:"\uD83D\uDD2C",creative_writing:"✍️",business_strategy:"\uD83D\uDCCA",technical_documentation:"\uD83D\uDCDA"},a={brainstorming_ideation:"Generate creative concepts and innovative ideas",coding_backend:"Implement technical solutions and backend logic",coding_frontend:"Create user interfaces and frontend experiences",education_tutoring:"Add educational elements and learning components",research_analysis:"Conduct research and analyze information",creative_writing:"Craft engaging content and narratives",business_strategy:"Develop strategic approaches and solutions",technical_documentation:"Create comprehensive technical documentation"},s="\uD83D\uDCCB **Orchestration Plan:**\n\n";e.forEach((e,t)=>{let r=i[e.role_id]||"\uD83E\uDD16",n=a[e.role_id]||"Specialized AI processing";s+=`**Step ${e.step_number}:** ${r} ${e.role_id.replace("_"," ").toUpperCase()} Specialist
└─ ${n}

`}),s+=`🤖 **Moderator:** "I've analyzed your request and assembled a team of ${e.length} specialists. Each will contribute their expertise in sequence, and I'll coordinate their work to deliver a comprehensive response."

---

`;let n={id:sM().randomUUID(),object:"chat.completion.chunk",created:Math.floor(Date.now()/1e3),model:"rokey-orchestration-enhanced",choices:[{index:0,delta:{content:s},finish_reason:null}]};t.enqueue(r.encode(`data: ${JSON.stringify(n)}

`))}async function nK(e,t,r,i,a){let s={brainstorming_ideation:"\uD83E\uDDE0",coding_backend:"\uD83D\uDCBB",coding_frontend:"\uD83C\uDFA8",education_tutoring:"\uD83C\uDF93",research_analysis:"\uD83D\uDD2C",creative_writing:"✍️",business_strategy:"\uD83D\uDCCA",technical_documentation:"\uD83D\uDCDA"}[e.role_id]||"\uD83E\uDD16",n="";if("pending"===t&&"pending"!==r?n=`🤖 **Moderator** → ${s} **${e.role_id.replace("_"," ").toUpperCase()} Specialist:**
"You're up next! Please analyze the request and provide your specialized expertise..."

`:"in_progress"===t&&"in_progress"!==r&&(n=`${s} **${e.role_id.replace("_"," ").toUpperCase()} Specialist:** Working... ⏳
🔄 *Analyzing and processing your request with specialized expertise*

`),n){let e={id:sM().randomUUID(),object:"chat.completion.chunk",created:Math.floor(Date.now()/1e3),model:"rokey-orchestration-enhanced",choices:[{index:0,delta:{content:n},finish_reason:null}]};i.enqueue(a.encode(`data: ${JSON.stringify(e)}

`))}}async function nB(e,t,r,i,a){let s={brainstorming_ideation:"\uD83E\uDDE0",coding_backend:"\uD83D\uDCBB",coding_frontend:"\uD83C\uDFA8",education_tutoring:"\uD83C\uDF93",research_analysis:"\uD83D\uDD2C",creative_writing:"✍️",business_strategy:"\uD83D\uDCCA",technical_documentation:"\uD83D\uDCDA"}[e.role_id]||"\uD83E\uDD16",n=`${s} **${e.role_id.replace("_"," ").toUpperCase()} Specialist:** ✅ **Complete!**

`,o=Math.floor(85+10*Math.random());n+=`🤖 **Moderator Review:** "Excellent work! Quality: ${o}%. `,t<r?n+=`Passing results to Step ${t+1} specialist..."

📤 *Preparing handoff with context and requirements*

`:n+=`All specialists have completed their work. Ready for synthesis!"

`;let l={id:sM().randomUUID(),object:"chat.completion.chunk",created:Math.floor(Date.now()/1e3),model:"rokey-orchestration-enhanced",choices:[{index:0,delta:{content:n},finish_reason:null}]};i.enqueue(a.encode(`data: ${JSON.stringify(l)}

`))}async function nW(e,t,r){let i="\uD83C\uDFAD **SYNTHESIS PHASE INITIATED**\n\n";i+=`🤖 **Moderator:** "All ${e.length} specialists have completed their work! Now I'll weave their expertise together into a comprehensive response."

---

🧩 **Synthesis Process:**

`;let a={id:sM().randomUUID(),object:"chat.completion.chunk",created:Math.floor(Date.now()/1e3),model:"rokey-orchestration-enhanced",choices:[{index:0,delta:{content:i},finish_reason:null}]};t.enqueue(r.encode(`data: ${JSON.stringify(a)}

`));let s=["\uD83D\uDD0D Analyzing all specialist contributions...","\uD83E\uDDE0 Identifying key insights and patterns...","\uD83D\uDD17 Finding connections between different perspectives...","\uD83D\uDCDD Structuring the comprehensive response...","✨ Adding final polish and coherence..."];for(let e=0;e<s.length;e++){await new Promise(e=>setTimeout(e,800));let i=`${s[e]} ✅

`,a={id:sM().randomUUID(),object:"chat.completion.chunk",created:Math.floor(Date.now()/1e3),model:"rokey-orchestration-enhanced",choices:[{index:0,delta:{content:i},finish_reason:null}]};t.enqueue(r.encode(`data: ${JSON.stringify(a)}

`))}await new Promise(e=>setTimeout(e,500));let n={id:sM().randomUUID(),object:"chat.completion.chunk",created:Math.floor(Date.now()/1e3),model:"rokey-orchestration-enhanced",choices:[{index:0,delta:{content:"\uD83C\uDF89 **Synthesis Complete!** Here's your comprehensive response:\n\n---\n\n"},finish_reason:null}]};t.enqueue(r.encode(`data: ${JSON.stringify(n)}

`))}async function nJ(e,t,r,i,a){try{let e=t.filter(e=>e.response&&e.response.trim().length>0);if(0===e.length)throw Error("No valid specialist responses found for synthesis");let a=`As an AI orchestration moderator, synthesize multiple specialist outputs into one cohesive response.

CRITICAL INSTRUCTIONS:
- PRESERVE ALL CODE BLOCKS, TECHNICAL DETAILS, AND IMPLEMENTATION SPECIFICS
- DO NOT SUMMARIZE OR TRUNCATE CODE - INCLUDE EVERY LINE OF CODE PROVIDED
- MAINTAIN ALL TECHNICAL SPECIFICATIONS, FILE STRUCTURES, AND DETAILED EXPLANATIONS
- COMBINE THE OUTPUTS SEAMLESSLY BUT KEEP ALL TECHNICAL CONTENT INTACT
- If specialists provided complete code implementations, include them in full

Original Request: Please provide a comprehensive response based on the following specialist analyses.

Specialist Outputs:
${e.map(e=>`${e.step_number}. ${e.role_id} Specialist Analysis:
${e.response}`).join("\n\n---\n\n")}

SYNTHESIS INSTRUCTIONS:
1. PRESERVE ALL CODE BLOCKS IN THEIR ENTIRETY - Do not truncate, summarize, or abbreviate any code
2. INCLUDE ALL TECHNICAL SPECIFICATIONS, FILE STRUCTURES, AND IMPLEMENTATION DETAILS
3. MAINTAIN ALL FUNCTION DEFINITIONS, CLASS STRUCTURES, AND VARIABLE DECLARATIONS
4. KEEP ALL COMMENTS, DOCUMENTATION, AND EXPLANATORY TEXT FROM THE SPECIALISTS
5. If multiple specialists provided code, include ALL code from ALL specialists
6. Combine the outputs seamlessly while preserving every technical detail
7. Focus on practical implementation with complete, runnable code examples
8. Provide a complete, comprehensive response that fully addresses the original request

Please synthesize the above specialist analyses into a well-structured, comprehensive response that addresses the original request. Include all technical details and provide complete, runnable code examples.`,s=process.env.ROKEY_CLASSIFICATION_GEMINI_API_KEY;if(!s)throw Error("Classification API key not found");let n=await n$("https://generativelanguage.googleapis.com/v1beta/openai/chat/completions",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${s}`,"User-Agent":"RoKey/1.0 (Synthesis)",Origin:"https://rokey.app","Cache-Control":"no-cache"},body:JSON.stringify({model:"gemini-2.0-flash-001",messages:[{role:"user",content:a}],stream:!0,temperature:.3,max_tokens:8e3})});if(!n.ok)throw Error(`Synthesis API error: ${n.status}`);if(!n.body)throw Error("No response body for synthesis stream");let o=n.body.getReader(),l=new TextDecoder;for(;;){let{done:e,value:t}=await o.read();if(e){let e={id:sM().randomUUID(),object:"chat.completion.chunk",created:Math.floor(Date.now()/1e3),model:"rokey-orchestration-enhanced",choices:[{index:0,delta:{},finish_reason:"stop"}]};r.enqueue(i.encode(`data: ${JSON.stringify(e)}

`)),r.enqueue(i.encode(`data: [DONE]

`)),r.close();break}for(let e of l.decode(t,{stream:!0}).split("\n"))if(e.startsWith("data: ")){let t=e.slice(6);if("[DONE]"===t){let e={id:sM().randomUUID(),object:"chat.completion.chunk",created:Math.floor(Date.now()/1e3),model:"rokey-orchestration-enhanced",choices:[{index:0,delta:{},finish_reason:"stop"}]};r.enqueue(i.encode(`data: ${JSON.stringify(e)}

`)),r.enqueue(i.encode(`data: [DONE]

`)),r.close();return}try{let e=JSON.parse(t);if(e.choices?.[0]?.delta?.content){let t=e.choices[0].delta.content;t.length;let a={id:sM().randomUUID(),object:"chat.completion.chunk",created:Math.floor(Date.now()/1e3),model:"rokey-orchestration-enhanced",choices:[{index:0,delta:{content:t},finish_reason:null}]};r.enqueue(i.encode(`data: ${JSON.stringify(a)}

`))}e.choices?.[0]?.finish_reason}catch(e){}}}}catch(a){let e={id:sM().randomUUID(),object:"chat.completion.chunk",created:Math.floor(Date.now()/1e3),model:"rokey-orchestration-enhanced",choices:[{index:0,delta:{content:`

❌ **Synthesis Error:** ${a instanceof Error?a.message:"Unknown error occurred during synthesis"}

`},finish_reason:null}]};r.enqueue(i.encode(`data: ${JSON.stringify(e)}

`));let t={id:sM().randomUUID(),object:"chat.completion.chunk",created:Math.floor(Date.now()/1e3),model:"rokey-orchestration-enhanced",choices:[{index:0,delta:{},finish_reason:"stop"}]};r.enqueue(i.encode(`data: ${JSON.stringify(t)}

`)),r.enqueue(i.encode(`data: [DONE]

`)),r.close()}}async function nG(e){let t,r,i=new Date,a="true"===e.headers.get("X-External-Request")?(0,L.createClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co",process.env.SUPABASE_SERVICE_ROLE_KEY,{auth:{autoRefreshToken:!1,persistSession:!1}}):await (0,$.createSupabaseServerClientOnRequest)();performance.now();let s=e.headers.get("Authorization"),n=process.env.ROKEY_API_ACCESS_TOKEN;if(!n)return D.NextResponse.json({error:"Server configuration error: API access token not configured."},{status:500});if(!s||!s.startsWith("Bearer "))return D.NextResponse.json({error:"Unauthorized: Missing or invalid Authorization header format."},{status:401});if(s.substring(7)!==n)return D.NextResponse.json({error:"Unauthorized: Invalid API token."},{status:401});let o=e.headers.get("X-Synthesis-Execution-Id");if(o)return await nU(e,o);let l=null,c="unknown",u=null,h=null,d=null,p=null,m=null,f=null,g=null,b=null,y=null,_=null,w=null,v=null,k=null,O=null,x=null,S=!1,C=null;try{let s=await e.json();s&&"_internal_user_id"in s&&(v=s._internal_user_id,delete s._internal_user_id,delete s._internal_user_email);let n=n_.safeParse(s);if(!n.success)throw p={message:"Invalid request body",issues:n.error.flatten().fieldErrors,status:400};u=(t=n.data).custom_api_config_id;let o=t.messages?.[t.messages.length-1];if(o?.role==="user"&&"string"==typeof o.content){let r=o.content.toLowerCase().trim();if(["continue","continue please","keep going","go on","more","more please","finish","complete","what next","then what","and then"].includes(r)){let r=nr(t.messages),i=await nn(r);if(i)return await nF(e,t,i);let a=await no(r);if(a&&a.isComplete)return new Response(JSON.stringify({error:"synthesis_complete",message:"The synthesis is already complete. Your message will be processed as a new conversation."}),{status:200,headers:{"Content-Type":"application/json"}})}}let k=Date.now();for(let[e,t]of s8.entries())k-t.lastActivity>18e5&&s8.delete(e);if(await ni(),t.specific_api_key_id)try{let{data:e,error:r}=await a.from("api_keys").select("*").eq("id",t.specific_api_key_id).eq("custom_api_config_id",u).eq("status","active").single();if(r||!e)throw p={message:`Specific API key ${t.specific_api_key_id} not found or not active in this configuration.`,status:404};let i=await (0,U.Y)(e.encrypted_api_key),s=await nL(e.provider,e.predefined_model_id,i,t);if(l=e.id,h=e.predefined_model_id,d=e.provider,g=s.llmRequestTimestamp,b=s.llmResponseTimestamp,m=s.status??null,w=s.responseHeaders??null,c="specific_key_retry",s.success){if(f=s.responseData||{note:"streamed via specific key routing"},t.stream&&s.response)return s.response;if(!t.stream&&void 0!==s.responseData)return D.NextResponse.json(f,{status:m||200,headers:s.responseHeaders})}else throw p={message:`Specific API key ${e.id} failed: ${s.error?.message||"Unknown error"}`,status:s.status||500,provider_error:s.error},f=s.error,p}catch(e){p||(p={message:`Error using specific API key: ${e.message}`,status:500})}let O=`${u}:${t.messages?.[t.messages.length-1]?.content?.substring(0,100)||"default"}`,x=nc.get(O);if(x&&Date.now()-x.timestamp<18e5){let e=await nL(x.provider,x.model,x.apiKey,t);if(e.success)return nd(e,t);nc.delete(O)}let[S,A]=await Promise.allSettled([nf(u),a.from("custom_api_configs").select("id, name, user_id, routing_strategy, routing_strategy_params").eq("id",u).single()]);if("rejected"===A.status||!A.value.data){let e="rejected"===A.status?A.reason:A.value.error;throw p={message:"Custom API Configuration not found or error fetching it.",status:404,provider_error:e}}let T=A.value.data;if(C=T.user_id){let e=await (0,sB.cr)(C),r=t.messages?.[t.messages.length-1],i=null;if(r?.role==="user"&&"string"==typeof r.content){let a=r.content;if(i=await sK.R.searchCache({promptText:a,modelUsed:"unknown",providerUsed:"unknown",temperature:t.temperature,maxTokens:t.max_tokens,metadata:{stream:t.stream}},C,u,e)){if(c="semantic_cache_hit",d=i.providerUsed,h=i.modelUsed,g=new Date,b=new Date,m=200,f=i.responseData,!t.stream)return D.NextResponse.json(i.responseData,{status:200,headers:{"X-Cache-Hit":"true","X-Cache-Similarity":i.similarity.toString()}});{let e=new TextEncoder,t=new ReadableStream({start(t){let r="";r=i.responseData?.choices?.[0]?.message?.content?i.responseData.choices[0].message.content:i.responseData?.content?i.responseData.content:i.responseData?.message?.content?i.responseData.message.content:"string"==typeof i.responseData?i.responseData:"Error: Unable to extract content from cached response";let a=((e,t=150)=>{if(e.length<=t)return[e];let r=[],i=0;for(;i<e.length;){let a=Math.min(i+t,e.length);if(a<e.length){let r=e.lastIndexOf(" ",a),s=e.lastIndexOf("\n",a),n=Math.max(e.lastIndexOf(".",a),e.lastIndexOf("!",a),e.lastIndexOf("?",a),e.lastIndexOf(",",a));n>i+.5*t?a=n+1:s>i+.5*t?a=s+1:r>i+.5*t&&(a=r+1)}r.push(e.slice(i,a)),i=a}return r})(r);a.forEach((r,s)=>{let n={id:`cached-${Date.now()}-${s}`,object:"chat.completion.chunk",created:Math.floor(Date.now()/1e3),model:i.modelUsed,choices:[{index:0,delta:{content:r},finish_reason:s===a.length-1?"stop":null}]};t.enqueue(e.encode(`data: ${JSON.stringify(n)}

`))}),t.enqueue(e.encode("data: [DONE]\n\n")),t.close()}});return new Response(t,{headers:{"Content-Type":"text/plain; charset=utf-8","Cache-Control":"no-cache",Connection:"keep-alive","X-Cache-Hit":"true","X-Cache-Similarity":i.similarity.toString()}})}}}}let P=T.routing_strategy,E=T.routing_strategy_params,j=P&&"none"!==P&&"auto"!==P,I=performance.now(),[N,R]=await Promise.allSettled([(async()=>{let e=t.messages,r=null,i="",s=[];if("fulfilled"===S.status&&S.value&&(r=S.value.trainingData,e=await ny(e,r)),C&&u){let t=e.filter(e=>"user"===e.role);if(t.length>0){let r=t[t.length-1],n="string"==typeof r.content?r.content:r.content?.[0]?.text||"";if(n.trim()&&await ng(n)){let t=await nb(n,u,C,a);if(i=t.context,s=t.sources,i){let t=e.findIndex(e=>"system"===e.role),r=`

=== IMPORTANT KNOWLEDGE BASE CONTEXT ===
${i}
=== END KNOWLEDGE BASE ===

IMPORTANT: The above knowledge base contains specific information that should be prioritized when answering questions. Use this information to provide detailed, accurate responses. If the user asks about topics covered in the knowledge base, draw extensively from this content rather than giving generic responses.`;t>=0?e[t].content+=r:e.unshift({role:"system",content:`You are a helpful AI assistant.${r}`})}}}}return{enhancedMessages:e,trainingData:r,documentContext:i,documentSources:s}})(),(async()=>{if("intelligent_role"===P)return await nw(T,u,t,a,e);if("strict_fallback"===P)return await nv(E,u,a);if("complexity_round_robin"===P)return await nI(T,t,a);if("cost_optimized"===P)return await nk(T,u,t,a);if("ab_routing"===P)return await nA(T,u,t,a);return{targetApiKeyData:null,roleUsedState:"no_strategy"}})()]),M=performance.now(),$=null,L=[];if("fulfilled"===N.status&&(t.messages=N.value.enhancedMessages,L=N.value.documentSources||[],t.messages.find(e=>"system"===e.role),L.length),"fulfilled"===R.status){if("hybridResponse"in R.value&&R.value.hybridResponse){c=R.value.roleUsedState,d="hybrid_orchestration",h=null,d="RouKey",g=new Date,b=new Date,m=200;let e=c.replace("RouKey_Multi Roles_","").replace("_routing","").split("_").map(e=>`RouKey/${e}`);return f={note:"RouKey multi-role orchestration response",orchestration_type:"rokey_multi_role",is_orchestration:!0,models_used:e},R.value.hybridResponse}if($=R.value.targetApiKeyData,(c=R.value.roleUsedState)&&c.startsWith("orchestration_started_")){let r=c.split("_")[2];return nq(e,r,t)}"classifiedComplexityLevel"in R.value&&void 0!==R.value.classifiedComplexityLevel&&(y=R.value.classifiedComplexityLevel),"classifiedComplexityLLM"in R.value&&void 0!==R.value.classifiedComplexityLLM&&(_=R.value.classifiedComplexityLLM)}else c="routing_failed";if(!$)if(j)if($=await nN(a,u,t.role))if(t.role){let{data:e}=await a.from("api_key_role_assignments").select("api_key_id").eq("custom_api_config_id",u).eq("api_key_id",$.id).eq("role_name",t.role).maybeSingle();c=e?z.roleRouting(t.role):z.defaultKeySuccess()}else c=z.defaultKeySuccess();else c=`${c}_then_fb_failed_completely`;else{let{data:e,error:r}=await a.from("api_keys").select("*").eq("custom_api_config_id",u).eq("status","active");if(r)p={message:"Database error fetching keys for default routing.",status:500,provider_error:r},c="default_db_error_fetching_keys";else if(e&&0!==e.length){let r=T.routing_strategy_params||{};"number"==typeof r._default_rr_idx&&r._default_rr_idx;let i=[...e].sort((e,t)=>e.id.localeCompare(t.id)),s=i.map(async(e,r)=>{let i=r+1;try{let r=await (0,U.Y)(e.encrypted_api_key),a=await nL(e.provider,e.predefined_model_id,r,t);if(a.success)return{success:!0,key:e,result:a,attemptNumber:i};return{success:!1,key:e,error:a.error,status:a.status,attemptNumber:i}}catch(t){return{success:!1,key:e,error:t,status:t.status||500,attemptNumber:i}}});try{let e=await Promise.allSettled(s),n=null,o=null,u=e.length;for(let t of e)if("fulfilled"===t.status&&t.value.success){n=t.value;break}else"fulfilled"===t.status&&(o=t.value);if(n){let e=n.key,s=n.result,o=n.attemptNumber;if(r._default_rr_idx=(i.findIndex(t=>t.id===e.id)+1)%i.length,T.routing_strategy_params=r,setImmediate(async()=>{let{error:e}=await a.from("custom_api_configs").update({routing_strategy_params:r}).eq("id",T.id)}),l=e.id,h=e.predefined_model_id,d=e.provider,g=s?.llmRequestTimestamp||null,b=s?.llmResponseTimestamp||null,m=s?.status??null,w=s?.responseHeaders??null,c=z.defaultKeySuccess(o),p=null,t.stream&&s?.response)return f=s.responseData||{note:"streamed via parallel default routing"},s.response;if(t.stream||s?.responseData===void 0)f={error:(p={message:`Internal error: Key ${e.id} success but no response data/stream.`,status:500}).message},m=500;else{f=s.responseData;let e=np(c),t={...f,rokey_metadata:{roles_used:e,routing_strategy:T?.routing_strategy||"unknown",...f.rokey_metadata||{}}};return D.NextResponse.json(t,{status:m||200,headers:s.responseHeaders})}}else o?(f=o.error,m=o.status??null,p={message:`All ${u} key(s) for parallel default routing failed. Last error from key ${o.key.id}: ${o.error?.message||"Unknown"}`,status:o.status||500,provider_error:o.error},c=z.allKeysFailed(u)):(p={message:`All ${u} key(s) for parallel default routing failed with unknown errors.`,status:500},c=`default_all_parallel_attempts_failed_${u}`)}catch(e){p={message:`Parallel default routing failed: ${e.message}`,status:500},c="default_parallel_execution_error"}!p&&!l&&i.length>0&&(p={message:`All ${i.length} key(s) for default routing were attempted but failed. Status: ${m||"N/A"}. An internal error may have occurred.`,status:m||500,provider_error:null},i.length>0&&(c=z.allKeysFailed(i.length))),p&&!l&&(c=`default_all_attempts_failed_final_err_summary_${p?.message?.substring(0,70)}`)}else p={message:`No active keys configured for RoKey Config ID ${u} to use with default routing.`,status:404},c="default_no_active_keys_for_config"}if($&&!p)if(l=$.id,h=$.predefined_model_id,d=$.provider){if(!r)try{r=await (0,U.Y)($.encrypted_api_key)}catch(e){p={message:`API Key decryption failed for selected key ${$.id}.`,status:500}}}else p={message:`Selected API key '${l}' does not have a provider configured. Please check the API key settings.`,status:500};else $||p||(p={message:`RoKey could not resolve an API key for this request. Last routing state: ${c||"unknown"}.`,status:404});if($&&r&&!p&&d&&j){let s=await nL(d,h,r,t);if(g=s.llmRequestTimestamp,b=s.llmResponseTimestamp,m=s.status??null,w=s.responseHeaders??null,s.success)return f=s.responseData||{note:"streamed via explicit strategy"},nd(s,t,d,h||void 0,r,{roleUsed:c,routingStrategy:P,complexityLevel:y||void 0,processingTime:M-I});{let r={keyId:l,provider:d,status:s.status,error:s.error,strategy:P};try{let r={custom_api_config_id:u,api_key_id:l,user_id:C||v,predefined_model_id:h,role_requested:t?.role||null,role_used:`${c}_FAILED`,ip_address:e.headers.get("x-forwarded-for")||e.headers.get("x-real-ip")||null,request_timestamp:i.toISOString(),response_timestamp:new Date().toISOString(),status_code:s.status||500,request_payload_summary:t?{messages_count:t.messages?.length,model_requested_passthrough:t.model,stream:t.stream,temp:t.temperature,max_tok:t.max_tokens}:{note:"Request body was not available."},response_payload_summary:{error_type:s.error?.type||s.error?.error?.type||"provider_error",error_message_summary:s.error?.message||s.error?.error?.message||s.error?.details?.message||"Unknown error",provider_status:s.error?.status||s.status,full_error_details:s.error,fallback_initiated:!0},error_message:`ORIGINAL FAILURE: ${s.error?.message||s.error?.error?.message||s.error?.details?.message||JSON.stringify(s.error)||"Unknown error"}. Fallback will be attempted.`,error_source:d,error_details_zod:null,llm_provider_name:d,llm_model_name:h,llm_provider_status_code:s.status,llm_provider_latency_ms:s.llmResponseTimestamp&&s.llmRequestTimestamp?s.llmResponseTimestamp.getTime()-s.llmRequestTimestamp.getTime():null,processing_duration_ms:new Date().getTime()-i.getTime(),classified_role_llm:null,classified_complexity_level:y,classified_complexity_llm:_,cost:null,input_tokens:null,output_tokens:null,is_multimodal:!!t?.messages&&t.messages.some(e=>Array.isArray(e.content)&&e.content.some(e=>"image_url"===e.type))},{error:n}=await a.from("request_logs").insert(r)}catch(e){}let n=!1,o=await nN(a,u,void 0);if(o&&o.id!==l){let e;try{e=await (0,U.Y)(o.encrypted_api_key)}catch(t){e=""}if(e){let r=await nL(o.provider,o.predefined_model_id,e,t);if(r.success){if(c=`${c}_FALLBACK_SUCCESS_default`,n=!0,g=r.llmRequestTimestamp,b=r.llmResponseTimestamp,m=r.status??null,w=r.responseHeaders??null,l=o.id,h=o.predefined_model_id,d=o.provider,t.stream&&r.response)return f=r.responseData||{note:"streamed via intelligent fallback to default"},r.response;if(!t.stream&&void 0!==r.responseData){f=r.responseData;let e=np(c),t={...f,rokey_metadata:{roles_used:e,routing_strategy:T?.routing_strategy||"unknown",...f.rokey_metadata||{}}};return D.NextResponse.json(t,{status:m||200,headers:r.responseHeaders})}}}}if(!n){let{data:e,error:r}=await a.from("api_keys").select("*").eq("custom_api_config_id",u).eq("status","active");if(r);else if(e&&e.length>0){let r=new Set([l]);o&&r.add(o.id);let i=e.filter(e=>!r.has(e.id));if(i.length>0){let e=i.map(async e=>{try{let r=await (0,U.Y)(e.encrypted_api_key),i=await nL(e.provider,e.predefined_model_id,r,t);if(i.success)return{success:!0,key:e,result:i};return{success:!1,key:e,error:i.error}}catch(t){return{success:!1,key:e,error:t}}});try{for(let r of(await Promise.allSettled(e)))if("fulfilled"===r.status&&r.value.success){let e=r.value,i=e.key,a=e.result;if(c=`${c}_PARALLEL_FALLBACK_SUCCESS_${i.id}`,n=!0,g=a?.llmRequestTimestamp||null,b=a?.llmResponseTimestamp||null,m=a?.status??null,w=a?.responseHeaders??null,l=i.id,h=i.predefined_model_id,d=i.provider,t.stream&&a?.response)return f=a.responseData||{note:`streamed via parallel fallback to ${i.id}`},a.response;if(!t.stream&&a?.responseData!==void 0){f=a.responseData;let e=np(c),t={...f,rokey_metadata:{roles_used:e,routing_strategy:T?.routing_strategy||"unknown",...f.rokey_metadata||{}}};return D.NextResponse.json(t,{status:m||200,headers:a.responseHeaders})}break}}catch(e){}}}}n||(c=`${c}_all_fallbacks_failed`,p={message:`Intelligent routing (${r.strategy}) failed for key ${r.keyId} (${r.provider}). All fallback attempts also failed. Original error: ${r.error?.message||"Unknown"}`,status:r.status||500,provider_error:r.error,fallback_attempted:!0},f=r.error,m=r.status??null)}}else $&&!r&&!p&&d&&j?p||(p={message:`API key ${l} selected but decryption failed (safeguard).`,status:500}):$&&r&&!p&&!d&&j&&!p&&(p={message:`API key ${l} selected but has no provider configured (safeguard).`,status:500});if(p){let e=p.status||500,t=p.message||"An unexpected internal server error occurred.",r=p.issues,i=p.provider_error;return!m&&p.status&&p.provider_error&&(m=p.status),!f&&t&&(f={error:{message:t,...i&&{details:i}}}),D.NextResponse.json({error:t,...r&&{issues:r},...i&&{provider_error_details:i}},{status:e})}if(!p&&!l)return p={message:"Critical internal error: No API key processed and no explicit error state.",status:500},D.NextResponse.json({error:p.message},{status:p.status});if(f&&!t?.stream){let e=np(c),t={...f,rokey_metadata:{roles_used:e,routing_strategy:T?.routing_strategy||"unknown",...f.rokey_metadata||{}}};return D.NextResponse.json(t,{status:m||200})}return D.NextResponse.json({error:"An unexpected critical server error occurred."},{status:500})}finally{let r=new Date,s=r.getTime()-i.getTime(),n=null;g&&b&&(n=b.getTime()-g.getTime());let o=s-(n||0);performance.now(),t?.messages&&(S=t.messages.some(e=>Array.isArray(e.content)&&e.content.some(e=>"image_url"===e.type))),f?.usage?(O=f.usage.prompt_tokens||f.usage.input_tokens||null,x=f.usage.completion_tokens||f.usage.output_tokens||null):d?.toLowerCase()==="google"&&f?.promptFeedback?.tokenCount!==void 0&&(O=f.promptFeedback.tokenCount,x=f.candidates?.[0]?.tokenCount||null),function(e,t){if(!e)return!1;let r=e.toLowerCase();if("deepseek"===r)return!0;if(("google"===r||"gemini"===r)&&t)for(let e of["x-ratelimit-limit","x-ratelimit-requests-limit","x-goog-quota-limit","quota-limit"]){let r=t.get(e);if(r){let e=parseInt(r);if(!isNaN(e))return e<=60}}return!1}(d,w||void 0)?k=0:f?.usage?.cost&&d?.toLowerCase()==="openrouter"?k=1e-6*f.usage.cost:null!==O&&null!==x&&h&&(setImmediate(async()=>{try{let{data:e,error:t}=await a.from("models").select("input_token_price, output_token_price").eq("id",h).single();if(!t&&e?.input_token_price&&e?.output_token_price&&null!==O&&null!==x){let t=O*e.input_token_price,r=x*e.output_token_price;A&&await a.from("request_logs").update({cost:t+r}).eq("custom_api_config_id",A).eq("request_timestamp",i.toISOString())}}catch(e){}}),k=null);let A=u||t?.custom_api_config_id;A?setImmediate(async()=>{try{let a=C||v,s={custom_api_config_id:A,api_key_id:l,user_id:a,predefined_model_id:h,role_requested:t?.role||null,role_used:c,ip_address:e.headers.get("x-forwarded-for")||e.headers.get("x-real-ip")||null,request_timestamp:i.toISOString(),response_timestamp:r.toISOString(),status_code:p?p.status||500:m||200,request_payload_summary:t?{messages_count:t.messages?.length,model_requested_passthrough:t.model,stream:t.stream,temp:t.temperature,max_tok:t.max_tokens}:{note:"Request body was not available or Zod validation failed."},response_payload_summary:{usage:f?.usage,finish_reason:f?.choices?.[0]?.finish_reason,error_type:f?.error?.type,error_message_summary:f?.error?.message,full_error_details:f?.error,is_fallback_success:c?.includes("FALLBACK_SUCCESS")||!1,original_failure_summary:c?.includes("FAILED")?"See previous log entry for original failure details":null},error_message:c?.includes("FALLBACK_SUCCESS")?"FALLBACK SUCCESS: Original model failed, successfully used fallback model. Check previous log entry for failure details.":p?.message?p.message:f?.error?.message?f.error.message:null,error_source:p?p.provider_error&&d?d:"RoKey":f?.error?d:null,error_details_zod:p?.issues?JSON.stringify(p.issues):null,llm_provider_name:d,llm_model_name:h,llm_provider_status_code:m,llm_provider_latency_ms:n,processing_duration_ms:o,classified_role_llm:null,classified_complexity_level:y,classified_complexity_llm:_,cost:k,input_tokens:O,output_tokens:x,is_multimodal:S},u=(0,L.createClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co",process.env.SUPABASE_SERVICE_ROLE_KEY,{auth:{autoRefreshToken:!1,persistSession:!1}}),{error:g}=await u.from("request_logs").insert(s)}catch(e){}}):p&&setImmediate(async()=>{try{let a={custom_api_config_id:null,api_key_id:null,user_id:v,predefined_model_id:null,role_requested:t?.role||null,role_used:null,ip_address:e.headers.get("x-forwarded-for")||e.headers.get("x-real-ip")||null,request_timestamp:i.toISOString(),response_timestamp:r.toISOString(),status_code:p.status||500,request_payload_summary:{note:"Early error, request body may be malformed.",custom_api_config_id_attempted:u},response_payload_summary:{error_message_summary:p.message?.substring(0,100)},error_message:p.message,error_source:"RoKey",error_details_zod:p.issues?JSON.stringify(p.issues):null,llm_provider_name:null,llm_model_name:null,llm_provider_status_code:null,llm_provider_latency_ms:null,processing_duration_ms:s,classified_role_llm:null,classified_complexity_level:null,classified_complexity_llm:null,cost:null,input_tokens:null,output_tokens:null,is_multimodal:!1},n=(0,L.createClient)("https://hpkzzhpufhbxtxqaugjh.supabase.co",process.env.SUPABASE_SERVICE_ROLE_KEY,{auth:{autoRefreshToken:!1,persistSession:!1}}),{error:o}=await n.from("request_logs").insert(a)}catch(e){}})}}async function nY(e){return D.NextResponse.json({},{status:200,headers:{"Access-Control-Allow-Origin":"*","Access-Control-Allow-Methods":"POST, OPTIONS","Access-Control-Allow-Headers":"Content-Type, Authorization"}})}let nH=new N.AppRouteRouteModule({definition:{kind:R.RouteKind.APP_ROUTE,page:"/api/v1/chat/completions/route",pathname:"/api/v1/chat/completions",filename:"route",bundlePath:"app/api/v1/chat/completions/route"},resolvedPagePath:"C:\\RoKey App\\rokey-app\\src\\app\\api\\v1\\chat\\completions\\route.ts",nextConfigOutput:"",userland:I}),{workAsyncStorage:nX,workUnitAsyncStorage:nZ,serverHooks:nQ}=nH;function n0(){return(0,M.patchFetch)({workAsyncStorage:nX,workUnitAsyncStorage:nZ})}},74075:e=>{e.exports=require("zlib")},77598:e=>{e.exports=require("node:crypto")},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},81630:e=>{e.exports=require("http")},91645:e=>{e.exports=require("net")},94735:e=>{e.exports=require("events")},99366:(e,t,r)=>{r.d(t,{ez:()=>T,RG:()=>P,Ns:()=>I,Vt:()=>C,Qs:()=>A,D4:()=>O,g2:()=>S,QC:()=>j,Xm:()=>E});var i=Object.prototype.toString,a=Array.isArray||function(e){return"[object Array]"===i.call(e)};function s(e){return"function"==typeof e}function n(e){return e.replace(/[\-\[\]{}()*+?.,\\\^$|#\s]/g,"\\$&")}function o(e,t){return null!=e&&"object"==typeof e&&t in e}var l=RegExp.prototype.test,c=/\S/,u={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;","/":"&#x2F;","`":"&#x60;","=":"&#x3D;"},h=/\s*/,d=/\s+/,p=/\s*=/,m=/\s*\}/,f=/#|\^|\/|>|\{|&|=|!/;function g(e){this.string=e,this.tail=e,this.pos=0}function b(e,t){this.view=e,this.cache={".":this.view},this.parent=t}function y(){this.templateCache={_cache:{},set:function(e,t){this._cache[e]=t},get:function(e){return this._cache[e]},clear:function(){this._cache={}}}}g.prototype.eos=function(){return""===this.tail},g.prototype.scan=function(e){var t=this.tail.match(e);if(!t||0!==t.index)return"";var r=t[0];return this.tail=this.tail.substring(r.length),this.pos+=r.length,r},g.prototype.scanUntil=function(e){var t,r=this.tail.search(e);switch(r){case -1:t=this.tail,this.tail="";break;case 0:t="";break;default:t=this.tail.substring(0,r),this.tail=this.tail.substring(r)}return this.pos+=t.length,t},b.prototype.push=function(e){return new b(e,this)},b.prototype.lookup=function(e){var t=this.cache;if(t.hasOwnProperty(e))a=t[e];else{for(var r,i,a,n,l,c,u=this,h=!1;u;){if(e.indexOf(".")>0)for(n=u.view,l=e.split("."),c=0;null!=n&&c<l.length;)c===l.length-1&&(h=o(n,l[c])||(r=n,i=l[c],null!=r&&"object"!=typeof r&&r.hasOwnProperty&&r.hasOwnProperty(i))),n=n[l[c++]];else n=u.view[e],h=o(u.view,e);if(h){a=n;break}u=u.parent}t[e]=a}return s(a)&&(a=a.call(this.view)),a},y.prototype.clearCache=function(){void 0!==this.templateCache&&this.templateCache.clear()},y.prototype.parse=function(e,t){var r=this.templateCache,i=e+":"+(t||_.tags).join(":"),s=void 0!==r,o=s?r.get(i):void 0;return void 0==o&&(o=function(e,t){if(!e)return[];var r,i,s,o,u,b,y,w,v,k=!1,O=[],x=[],S=[],C=!1,A=!1,T="",P=0;function E(){if(C&&!A)for(;S.length;)delete x[S.pop()];else S=[];C=!1,A=!1}function j(e){if("string"==typeof e&&(e=e.split(d,2)),!a(e)||2!==e.length)throw Error("Invalid tags: "+e);r=RegExp(n(e[0])+"\\s*"),i=RegExp("\\s*"+n(e[1])),s=RegExp("\\s*"+n("}"+e[1]))}j(t||_.tags);for(var I=new g(e);!I.eos();){if(o=I.pos,b=I.scanUntil(r))for(var N=0,R=b.length;N<R;++N)!function(e){return!l.call(c,e)}(y=b.charAt(N))?(A=!0,k=!0,T+=" "):(S.push(x.length),T+=y),x.push(["text",y,o,o+1]),o+=1,"\n"===y&&(E(),T="",P=0,k=!1);if(!I.scan(r))break;if(C=!0,u=I.scan(f)||"name",I.scan(h),"="===u?(b=I.scanUntil(p),I.scan(p),I.scanUntil(i)):"{"===u?(b=I.scanUntil(s),I.scan(m),I.scanUntil(i),u="&"):b=I.scanUntil(i),!I.scan(i))throw Error("Unclosed tag at "+I.pos);if(w=">"==u?[u,b,o,I.pos,T,P,k]:[u,b,o,I.pos],P++,x.push(w),"#"===u||"^"===u)O.push(w);else if("/"===u){if(!(v=O.pop()))throw Error('Unopened section "'+b+'" at '+o);if(v[1]!==b)throw Error('Unclosed section "'+v[1]+'" at '+o)}else"name"===u||"{"===u||"&"===u?A=!0:"="===u&&j(b)}if(E(),v=O.pop())throw Error('Unclosed section "'+v[1]+'" at '+I.pos);return function(e){for(var t,r=[],i=r,a=[],s=0,n=e.length;s<n;++s)switch((t=e[s])[0]){case"#":case"^":i.push(t),a.push(t),i=t[4]=[];break;case"/":a.pop()[5]=t[2],i=a.length>0?a[a.length-1][4]:r;break;default:i.push(t)}return r}(function(e){for(var t,r,i=[],a=0,s=e.length;a<s;++a)(t=e[a])&&("text"===t[0]&&r&&"text"===r[0]?(r[1]+=t[1],r[3]=t[3]):(i.push(t),r=t));return i}(x))}(e,t),s&&r.set(i,o)),o},y.prototype.render=function(e,t,r,i){var a=this.getConfigTags(i),s=this.parse(e,a),n=t instanceof b?t:new b(t,void 0);return this.renderTokens(s,n,r,e,i)},y.prototype.renderTokens=function(e,t,r,i,a){for(var s,n,o,l="",c=0,u=e.length;c<u;++c)o=void 0,"#"===(n=(s=e[c])[0])?o=this.renderSection(s,t,r,i,a):"^"===n?o=this.renderInverted(s,t,r,i,a):">"===n?o=this.renderPartial(s,t,r,a):"&"===n?o=this.unescapedValue(s,t):"name"===n?o=this.escapedValue(s,t,a):"text"===n&&(o=this.rawValue(s)),void 0!==o&&(l+=o);return l},y.prototype.renderSection=function(e,t,r,i,n){var o=this,l="",c=t.lookup(e[1]);if(c){if(a(c))for(var u=0,h=c.length;u<h;++u)l+=this.renderTokens(e[4],t.push(c[u]),r,i,n);else if("object"==typeof c||"string"==typeof c||"number"==typeof c)l+=this.renderTokens(e[4],t.push(c),r,i,n);else if(s(c)){if("string"!=typeof i)throw Error("Cannot use higher-order sections without the original template");null!=(c=c.call(t.view,i.slice(e[3],e[5]),function(e){return o.render(e,t,r,n)}))&&(l+=c)}else l+=this.renderTokens(e[4],t,r,i,n);return l}},y.prototype.renderInverted=function(e,t,r,i,s){var n=t.lookup(e[1]);if(!n||a(n)&&0===n.length)return this.renderTokens(e[4],t,r,i,s)},y.prototype.indentPartial=function(e,t,r){for(var i=t.replace(/[^ \t]/g,""),a=e.split("\n"),s=0;s<a.length;s++)a[s].length&&(s>0||!r)&&(a[s]=i+a[s]);return a.join("\n")},y.prototype.renderPartial=function(e,t,r,i){if(r){var a=this.getConfigTags(i),n=s(r)?r(e[1]):r[e[1]];if(null!=n){var o=e[6],l=e[5],c=e[4],u=n;0==l&&c&&(u=this.indentPartial(n,c,o));var h=this.parse(u,a);return this.renderTokens(h,t,r,u,i)}}},y.prototype.unescapedValue=function(e,t){var r=t.lookup(e[1]);if(null!=r)return r},y.prototype.escapedValue=function(e,t,r){var i=this.getConfigEscape(r)||_.escape,a=t.lookup(e[1]);if(null!=a)return"number"==typeof a&&i===_.escape?String(a):i(a)},y.prototype.rawValue=function(e){return e[1]},y.prototype.getConfigTags=function(e){return a(e)?e:e&&"object"==typeof e?e.tags:void 0},y.prototype.getConfigEscape=function(e){return e&&"object"==typeof e&&!a(e)?e.escape:void 0};var _={name:"mustache.js",version:"4.2.0",tags:["{{","}}"],clearCache:void 0,escape:void 0,parse:void 0,render:void 0,Scanner:void 0,Context:void 0,Writer:void 0,set templateCache(cache){w.templateCache=cache},get templateCache(){return w.templateCache}},w=new y;_.clearCache=function(){return w.clearCache()},_.parse=function(e,t){return w.parse(e,t)},_.render=function(e,t,r,i){if("string"!=typeof e)throw TypeError('Invalid template! Template should be a "string" but "'+(a(e)?"array":typeof e)+'" was given as the first argument for mustache#render(template, view, partials)');return w.render(e,t,r,i)},_.escape=function(e){return String(e).replace(/[&<>"'`=\/]/g,function(e){return u[e]})},_.Scanner=g,_.Context=b,_.Writer=y;var v=r(13346);function k(){_.escape=e=>e}let O=e=>{let t=e.split(""),r=[],i=(e,r)=>{for(let i=r;i<t.length;i+=1)if(e.includes(t[i]))return i;return -1},a=0;for(;a<t.length;)if("{"===t[a]&&a+1<t.length&&"{"===t[a+1])r.push({type:"literal",text:"{"}),a+=2;else if("}"===t[a]&&a+1<t.length&&"}"===t[a+1])r.push({type:"literal",text:"}"}),a+=2;else if("{"===t[a]){let e=i("}",a);if(e<0)throw Error("Unclosed '{' in template.");r.push({type:"variable",name:t.slice(a+1,e).join("")}),a=e+1}else if("}"===t[a])throw Error("Single '}' in template.");else{let e=i("{}",a),s=(e<0?t.slice(a):t.slice(a,e)).join("");r.push({type:"literal",text:s}),a=e<0?t.length:e}return r},x=e=>e.map(e=>"name"===e[0]?{type:"variable",name:e[1].includes(".")?e[1].split(".")[0]:e[1]}:["#","&","^",">"].includes(e[0])?{type:"variable",name:e[1]}:{type:"literal",text:e[1]}),S=e=>(k(),x(_.parse(e))),C=(e,t)=>O(e).reduce((e,r)=>{if("variable"===r.type){if(r.name in t)return e+("string"==typeof t[r.name]?t[r.name]:JSON.stringify(t[r.name]));throw Error(`(f-string) Missing value for input ${r.name}`)}return e+r.text},""),A=(e,t)=>(k(),_.render(e,t)),T={"f-string":C,mustache:A},P={"f-string":O,mustache:S},E=(e,t,r)=>{try{return T[t](e,r)}catch(e){throw(0,v.Y)(e,"INVALID_PROMPT_INPUT")}},j=(e,t)=>P[t](e),I=(e,t,r)=>{if(!(t in T)){let e=Object.keys(T);throw Error(`Invalid template format. Got \`${t}\`;
                         should be one of ${e}`)}try{let i=r.reduce((e,t)=>(e[t]="foo",e),{});Array.isArray(e)?e.forEach(e=>{if("text"===e.type)E(e.text,t,i);else if("image_url"===e.type)if("string"==typeof e.image_url)E(e.image_url,t,i);else{let r=e.image_url.url;E(r,t,i)}else throw Error(`Invalid message template received. ${JSON.stringify(e,null,2)}`)}):E(e,t,i)}catch(e){throw Error(`Invalid prompt schema: ${e.message}`)}}}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),i=t.X(0,[4447,580,9398,3410,5697,5601,9805],()=>r(71344));module.exports=i})();